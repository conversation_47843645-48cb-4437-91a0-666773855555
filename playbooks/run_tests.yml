---
- name: Run SimpliPIed Test Scripts
  hosts: simplipied_servers
  become: yes
  vars_files:
    - vars/deploy_vars.yml
  environment:
    ORACLE_BASE: /opt/oracle
    ORACLE_HOME: /opt/oracle/product/19c/dbhome_1
    ORA_INVENTORY: /u01/app/oraInventory
    ORACLE_SID: orcl
    DATA_DIR: /opt/oracle/oradata
    PATH: /usr/bin:/usr/sbin:/usr/local/bin:/opt/oracle/product/19c/dbhome_1/bin:/usr/local/bin/shunit2:$PATH
    SIMPLIPIED_HOME: /usr/local/bin/simplipied
    SIMPLIPIED_INBOUND: /opt/inbound
    SIMPLIPIED_OUTBOUND: /opt/outbound



  tasks:
    - name: Get hostname
      shell: echo "$(hostname | awk -F. '{print $1}')"
      register: results 

    - debug: msg="{{ results.stdout }}"

   
    - name: Print ansible_env
      debug:
        var: ansible_env

       
    - name: Run test scripts 
      shell: "./{{ item }}"
      args:
        chdir: "/usr/local/bin/simplipied"
      register: test_results
      loop: "{{ test_scripts }}"
      ignore_errors: no
      when: test_scripts is defined and test_scripts | length > 0
    
    - name: Display test results
      debug:
        msg: "{{ item.stdout_lines }}"
      loop: "{{ test_results.results }}"
      when: test_results.results is defined
    
    - name: Display test errors
      debug:
        msg: "{{ item.stderr_lines }}"
      loop: "{{ test_results.results }}"
      when: test_results.results is defined

    - name: Summarize test results
      debug:
        msg: |
          Test Summary:
          {% for result in test_results.results %}
          - {{ result.item }}: {{ 'PASSED' if result.rc == 0 else 'FAILED' }}
          {% endfor %}
