---
- name: Deploy SimpliPIed Application
  hosts: simplipied_servers
  become: yes
  vars_files:
    - vars/deploy_vars.yml

  vars:
    apex_app_dir: "{{ playbook_dir }}/../apex"
    bin_dir: "{{ playbook_dir }}/../bin"
    ctl_dir: "{{ playbook_dir }}/../ctl"
    sql_dir: "{{ playbook_dir }}/../sql"
    test_dir: "{{ playbook_dir }}/../test"
    deploy_timestamp: "{{ lookup('pipe', 'date +%Y%m%d%H%M%S') }}"

  environment:
    ORACLE_BASE: /opt/oracle
    ORACLE_HOME: /opt/oracle/product/19c/dbhome_1
    ORA_INVENTORY: /u01/app/oraInventory
    ORACLE_SID: orcl
    DATA_DIR: /opt/oracle/oradata
    PATH: /usr/bin:/usr/sbin:/usr/local/bin:/opt/oracle/product/19c/dbhome_1/bin:/usr/local/bin/shunit2:$PATH

  tasks:
    - name: Get hostname
      shell: echo "$(hostname | awk -F. '{print $1}')"
      register: get_hostname_results 
      changed_when: "'apex-app' not in get_hostname_results.stdout"

    # - debug: msg="{{ get_hostname_results.stdout }}"

    # - name: Print ansible_env
    #   debug:
    #   var: ansible_env
      
    - name: Create deployment directories
      file:
        path: "{{ item }}"
        state: directory
        mode: '0777'
      with_items: "{{ deployment_directories }}"
    
    - name: Copy bin scripts
      copy:
        src: "{{ bin_dir }}/"
        dest: "/usr/local/bin/simplipied/"
        mode: '0777'

    - name: Copy Control files
      copy:
        src: "{{ ctl_dir }}/"
        dest: "/usr/local/bin/simplipied/ctl"
        mode: '0666'    

    - name: Copy SQL scripts
      copy:
        src: "{{ sql_dir }}/"
        dest: "/usr/local/bin/simplipied/sql"
        mode: '0666'    

    - name: Copy Test support files
      copy:
        src: "{{ test_dir }}/"
        dest: "/usr/local/bin/simplipied/test"
        mode: '0666'     
    
    - name: Generate PI_ADMIN PGP key 
      shell: |
         if [ "$(gpg --list-keys | grep -i {{ key_name }})" = "" ] 
         then 
            ./pi_create_admin_key.sh "{{ key_name }}" {{ key_email }} "{{ key_comment }}" "{{ key_expiration }}" 
         fi
      args:
        chdir: "/usr/local/bin/simplipied"
      register: add_key_result
      changed_when: "'already exists' in add_key_result.stdout"

    - name: Create the database connections
      shell: ./pi_add_db_connection.sh "{{ item }}" "{{ db_password }}" "ORCL" "/usr/local/bin/simplipied/.connect"
      with_items: "{{ database_connections }}"
      args:
        chdir: "/usr/local/bin/simplipied"
      register: add_connections_results
      changed_when: "'already exists' not in add_connections_results.stdout"

    # - name: Display connection results
    #   debug:
    #     msg: "{{ item.stdout_lines }}"
    #   loop: "{{ add_connections_results.results }}"
    #   when: add_connections_results.results is defined


    - name: Create the remote connections
      shell: \
         ./pi_add_remote_connection.sh "apex-app.rapidpie.com" "{{ item }}" "{{ remote_password }}" "/usr/local/bin/simplipied/.netrc"
      with_items: "{{ remote_connections }}"
      args:
        chdir: "/usr/local/bin/simplipied"
      register: add_remote_connections_results
      changed_when: "'already exists' not in add_remote_connections_results.stdout"

    # - name: Display remote connection results
    #  debug:
    #     msg: "{{ item.stdout_lines }}"
    #   loop: "{{ add_remote_connections_results.results }}"
    #   when: add_remote_connections_results.results is defined


    - name: Create the email connection configuration file
      shell: ./pi_add_email_connection.sh "{{ mailbox }}" "{{ mailbox_user }}" "{{ mailbox_password }}" "/usr/local/bin/simplipied/.mail_connect"
      args:
        chdir: "/usr/local/bin/simplipied"
      register: add_email_connections_results
      changed_when: "'already exists' not in add_email_connections_results.stdout"

    # - name: Display email connection results
    #   debug:
    #     msg: "{{ item.stdout_lines }}"
    #   loop: "{{ add_email_connections_results.results }}"
    #   when: add_email_connections_results.results is defined


    - name: Notify deployment via email (Not working; investigate)
      shell: |
        ./pi_send_email.sh \
        -t "{{ deployment_email_recipients }}" \
        -s "{{ email_subject_prefix | default('') }} {{ app_name }} Deployment Notification - {{ env }}" \
        "{{ app_name }} application has been successfully deployed to {{ env }} environment at {{ deploy_timestamp }}.
        
        Deployed version: {{ app_version }}
        Deployed by: Ansible
        Deployment ID: {{ deploy_timestamp }}"
      args:
        chdir: "/usr/local/bin/simplipied"
      when: send_email_notification | bool
      register: email_results 
      changed_when: "'pi_send_email' not in email_results.stdout"

    # - debug: msg="{{ emails_results.stdout }}"

