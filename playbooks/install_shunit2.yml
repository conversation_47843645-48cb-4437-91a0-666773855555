---
- name: Install shunit2
  hosts: "{{ target_hosts | default('all') }}"
  become: yes
  vars:
    shunit2_version: "2.1.8"  # Specify the version you want to install
  
  tasks:
    - name: Check if shunit2 is installed
      command: which shunit2
      register: shunit2_check
      ignore_errors: yes
      changed_when: false
    
    - name: Install shunit2 via package manager (Debian/Ubuntu)
      apt:
        name: shunit2
        state: present
      when: 
        - shunit2_check.rc != 0
        - ansible_os_family == "Debian"
    
    - name: Install shunit2 via package manager (RedHat/CentOS)
      yum:
        name: shunit2
        state: present
      when: 
        - shunit2_check.rc != 0
        - ansible_os_family == "RedHat"
    
    - name: Download and install shunit2 manually if package not available
      block:
        - name: Create shunit2 directory
          file:
            path: /usr/local/lib/shunit2
            state: directory
            mode: '0755'
        
        - name: Download shunit2
          get_url:
            url: "https://github.com/kward/shunit2/archive/v{{ shunit2_version }}.tar.gz"
            dest: "/tmp/shunit2-{{ shunit2_version }}.tar.gz"
          when: shunit2_check.rc != 0
        
        - name: Extract shunit2
          unarchive:
            src: "/tmp/shunit2-{{ shunit2_version }}.tar.gz"
            dest: "/tmp"
            remote_src: yes
          when: shunit2_check.rc != 0
        
        - name: Install shunit2
          copy:
            src: "/tmp/shunit2-{{ shunit2_version }}/shunit2"
            dest: "/usr/local/bin/shunit2"
            mode: '0755'
            remote_src: yes
          when: shunit2_check.rc != 0
        
        - name: Clean up temporary files
          file:
            path: "{{ item }}"
            state: absent
          loop:
            - "/tmp/shunit2-{{ shunit2_version }}.tar.gz"
            - "/tmp/shunit2-{{ shunit2_version }}"
          when: shunit2_check.rc != 0
      when: 
        - shunit2_check.rc != 0
        - (ansible_os_family != "Debian" and ansible_os_family != "RedHat")