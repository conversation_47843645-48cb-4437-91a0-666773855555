---
- name: Test connectivity to apex-app.rapidpie.com
  hosts: localhost
  gather_facts: no
  
  tasks:
    - name: Check if apex-app.rapidpie.com is reachable (ping)
      command: ping -c 3 apex-app.rapidpie.com
      register: ping_result
      ignore_errors: yes
      changed_when: false
    
    - name: Display ping results
      debug:
        msg: "{{ ping_result.stdout_lines }}"
      when: ping_result.rc == 0
    
    - name: Display ping failure
      debug:
        msg: "Ping to apex-app.rapidpie.com failed: {{ ping_result.stderr }}"
      when: ping_result.rc != 0
    
    - name: Check HTTP connectivity (port 80)
      uri:
        url: http://apex-app.rapidpie.com
        method: GET
        status_code: 200,301,302,403
        timeout: 10
      register: http_result
      ignore_errors: yes
      changed_when: false
    
    - name: Display HTTP connectivity results
      debug:
        msg: "HTTP connection successful. Status code: {{ http_result.status }}"
      when: http_result is succeeded
    
    - name: Display HTTP connectivity failure
      debug:
        msg: "HTTP connection failed: {{ http_result.msg | default('Unknown error') }}"
      when: http_result is failed
    
    - name: Check HTTPS connectivity (port 443)
      uri:
        url: https://apex-app.rapidpie.com
        method: GET
        status_code: 200,301,302,403
        timeout: 10
        validate_certs: yes
      register: https_result
      ignore_errors: yes
      changed_when: false
    
    - name: Display HTTPS connectivity results
      debug:
        msg: "HTTPS connection successful. Status code: {{ https_result.status }}"
      when: https_result is succeeded
    
    - name: Display HTTPS connectivity failure
      debug:
        msg: "HTTPS connection failed: {{ https_result.msg | default('Unknown error') }}"
      when: https_result is failed
    
    - name: Check SSH connectivity (port 22)
      wait_for:
        host: apex-app.rapidpie.com
        port: 22
        timeout: 5
      register: ssh_result
      ignore_errors: yes
      changed_when: false
    
    - name: Display SSH connectivity results
      debug:
        msg: "SSH port is open and accessible"
      when: ssh_result is succeeded
    
    - name: Display SSH connectivity failure
      debug:
        msg: "SSH port is not accessible: {{ ssh_result.msg | default('Connection timed out') }}"
      when: ssh_result is failed
    
    - name: Check Oracle APEX port (8080)
      wait_for:
        host: apex-app.rapidpie.com
        port: 8080
        timeout: 5
      register: apex_result
      ignore_errors: yes
      changed_when: false
    
    - name: Display APEX connectivity results
      debug:
        msg: "APEX port (8080) is open and accessible"
      when: apex_result is succeeded
    
    - name: Display APEX connectivity failure
      debug:
        msg: "APEX port (8080) is not accessible: {{ apex_result.msg | default('Connection timed out') }}"
      when: apex_result is failed
    
    - name: Summarize connectivity results
      debug:
        msg: |
          Connectivity Summary for apex-app.rapidpie.com:
          - Ping: {{ 'SUCCESS' if ping_result.rc == 0 else 'FAILED' }}
          - HTTP (80): {{ 'SUCCESS' if http_result is succeeded else 'FAILED' }}
          - HTTPS (443): {{ 'SUCCESS' if https_result is succeeded else 'FAILED' }}
          - SSH (22): {{ 'SUCCESS' if ssh_result is succeeded else 'FAILED' }}
          - APEX (8080): {{ 'SUCCESS' if apex_result is succeeded else 'FAILED' }}