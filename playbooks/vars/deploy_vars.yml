---
# Application details
app_name: "SimpliPIed"
app_version: "1.0.0"

# Deployment settings
env: "development"
send_email_notification: true
deployment_email_recipients: "<EMAIL>" #"<EMAIL>,<EMAIL>"

# PGP Administrator Key
key_name: "PI_ADMIN"
key_email: "<EMAIL>"
key_comment: "Administration Services"
key_expiration: "Never"


# Application IDs for APEX export/import
apex_app_ids:
  - 1959
  

# SQL files to import
sql_files:
  - "PI_PDFViewer_REST.sql"
  - "PI_SpreadsheetViewer_REST.sql"
  - "PI-CONFIG.sql"
  

# Unit test scripts
test_scripts:
  - test_pi_common.sh
  - test_pi_email.sh
  - test_pi_host_request.sh  
  - test_spreadsheet_db_pkg.sh
  - test_letters_db_pkg.sh      # Sends a test letter to PostGrid
  - test_host_request_db_pkg.sh
  - test_pi_postgrid.sh         # Sends test letters to PostGrid

# Daemon processes to start
daemon_processes:
  - pi_startup_public_key_daemon.sh
  - pi_startup_host_request_daemon.sh
  - pi_startup_filewatcher.sh

# Daemon processes to shutdown before deployment
daemon_shutdown_scripts:
  - pi_shutdown_public_key_daemon.sh
  - pi_shutdown_host_request_daemon.sh
  - pi_shutdown_filewatcher.sh

# Directories to create
deployment_directories:
  - "/opt/aopdocs"
  - "/opt/inbound"
  - "/opt/outbound"
  - "/usr/local/bin/simplipied"
  - "/usr/local/bin/simplipied/apex"
  - "/usr/local/bin/simplipied/ctl"
  - "/usr/local/bin/simplipied/sql"
  - "/usr/local/bin/simplipied/test"


# Email notification settings
email_subject_prefix: "[DEPLOY]"


# Database connections to create
database_connections:
  - apps
  - cre
  - crestagg
  - vnd


# Remote connections to create
remote_connections: 
  - "rp_migrateDev"
  - "rp_migrateQA"
  - "rp_migrateProd"


# Email connection variables
mailbox: "imaps://rapidpie-com-mail.dynu.com:993/INBOX"
mailbox_user: "<EMAIL>"
