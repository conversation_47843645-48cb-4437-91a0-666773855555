"""
Base letter generation functionality for SimpliPIed.

This module provides common functionality shared between claim and provider
letter generation.
"""

import os
from typing import List, Dict, Any, Optional
from docxtpl import DocxTemplate
from ..utils.database import get_db_connection, execute_plsql_function
from ..utils.file_operations import (
    get_template_path, 
    ensure_directory_exists, 
    convert_docx_to_pdf,
    safe_remove_file
)
import cx_Oracle


class LetterGenerator:
    """Base class for letter generation functionality."""
    
    def __init__(self, output_dir: str = "/opt/aopdocs"):
        """
        Initialize the letter generator.
        
        Args:
            output_dir (str): Directory where generated files should be saved
        """
        self.output_dir = output_dir
        ensure_directory_exists(self.output_dir)
    
    def fill_template(self, 
                     letter_data: List[Dict[str, Any]], 
                     template_name: str,
                     filename_pattern: str) -> List[str]:
        """
        Fill a document template with letter data and generate PDF files.
        
        Args:
            letter_data (List[Dict[str, Any]]): List of letter data dictionaries
            template_name (str): Name of the template file
            filename_pattern (str): Pattern for generating output filenames
            
        Returns:
            List[str]: List of generated PDF file paths
        """
        template_path = get_template_path(template_name)
        generated_files = []
        
        for letter in letter_data:
            doc = DocxTemplate(template_path)
            doc.render(letter)
            
            # Generate filename based on pattern
            base_filename = filename_pattern.format(**letter)
            output_docx = os.path.join(self.output_dir, base_filename + ".docx")
            output_pdf = os.path.join(self.output_dir, base_filename + ".pdf")
            
            # Save DOCX file
            doc.save(output_docx)
            
            # Convert to PDF
            if convert_docx_to_pdf(output_docx, self.output_dir):
                print(f"PDF generated: {output_pdf}")
                generated_files.append(output_pdf)
            else:
                print(f"Failed to generate PDF for: {base_filename}")
            
            # Clean up DOCX file
            safe_remove_file(output_docx)
        
        return generated_files
    
    def fetch_letter_data_from_db(self, 
                                 function_name: str, 
                                 parameters: List[Any],
                                 detail_function_name: Optional[str] = None) -> List[Dict[str, Any]]:
        """
        Fetch letter data from database using PL/SQL functions.
        
        Args:
            function_name (str): Name of the main PL/SQL function
            parameters (List[Any]): Parameters for the function
            detail_function_name (Optional[str]): Name of detail function if needed
            
        Returns:
            List[Dict[str, Any]]: Letter data from database
        """
        connection = get_db_connection()
        
        try:
            # Get main letter cursor
            letter_cursor = execute_plsql_function(
                function_name, 
                cx_Oracle.CURSOR, 
                parameters,
                connection
            )
            
            letter_data = []
            
            for letter_row in letter_cursor:
                letter = self._process_letter_row(letter_row)
                
                # Fetch details if detail function is provided
                if detail_function_name:
                    letter = self._fetch_letter_details(
                        letter, 
                        detail_function_name, 
                        connection
                    )
                
                letter_data.append(letter)
            
            letter_cursor.close()
            return letter_data
            
        finally:
            connection.close()
    
    def _process_letter_row(self, letter_row: tuple) -> Dict[str, Any]:
        """
        Process a letter row from database cursor.
        
        This method should be overridden by subclasses to handle
        specific letter row structures.
        
        Args:
            letter_row (tuple): Row data from database cursor
            
        Returns:
            Dict[str, Any]: Processed letter data
        """
        raise NotImplementedError("Subclasses must implement _process_letter_row")
    
    def _fetch_letter_details(self, 
                             letter: Dict[str, Any], 
                             detail_function_name: str,
                             connection: cx_Oracle.Connection) -> Dict[str, Any]:
        """
        Fetch additional details for a letter.
        
        This method should be overridden by subclasses if they need
        to fetch additional details.
        
        Args:
            letter (Dict[str, Any]): Letter data
            detail_function_name (str): Name of detail function
            connection (cx_Oracle.Connection): Database connection
            
        Returns:
            Dict[str, Any]: Letter data with details added
        """
        return letter
