"""
Claim letter generation functionality for SimpliPIed.

This module provides functionality for generating claim letters from
database data and templates.
"""

from typing import List, Dict, Any, TYPE_CHECKING
from .base import LetterGenerator
from ..utils.database import execute_plsql_function

if TYPE_CHECKING:
    import cx_Oracle

try:
    import cx_Oracle
except ImportError:
    cx_Oracle = None


class ClaimLetterGenerator(LetterGenerator):
    """Generator for claim letters."""
    
    def _process_letter_row(self, letter_row: tuple) -> Dict[str, Any]:
        """
        Process a claim letter row from database cursor.
        
        Args:
            letter_row (tuple): Row data from database cursor
            
        Returns:
            Dict[str, Any]: Processed claim letter data
        """
        return {
            "letternumber": letter_row[0],
            "sourcesystemcode": letter_row[1],
            "letterdate": letter_row[2],
            "providertin": letter_row[3],
            "providerattention": letter_row[4],
            "providergroupname": letter_row[5],
            "provideraddressline1": letter_row[6],
            "provideraddressline2": letter_row[7],
            "providercity": letter_row[8],
            "providerstatecode": letter_row[9],
            "providerzip": letter_row[10],
            "appealpobox": letter_row[11], 
            "appealaddressline1": letter_row[12], 
            "appealaddressline2": letter_row[13], 
            "appealcity": letter_row[14], 
            "appealstatecode": letter_row[15], 
            "appealzipcode": letter_row[16], 
            "blurb": letter_row[17], 
            "membername": letter_row[18], 
            "memberid": letter_row[19], 
            "memberdob": letter_row[20], 			
            "claimnumber": letter_row[21], 			
            "patientaccountnumber": letter_row[22], 
            "dateofservicefrom": letter_row[23], 
            "dateofserviceto": letter_row[24], 
            "totalbillamount": letter_row[25], 
            "paidbyplan": letter_row[26], 
            "claimpaiddate": letter_row[27], 
            "checknumber": letter_row[28], 
            "overpaidamount": letter_row[29], 
            "hspceffdate": letter_row[30], 
            "hspcenddate": letter_row[31],
            "membershortname": letter_row[32],
            "details": []
        }
    
    def _fetch_letter_details(self,
                             letter: Dict[str, Any],
                             detail_function_name: str,
                             connection: Any) -> Dict[str, Any]:
        """
        Fetch claim letter details from database.
        
        Args:
            letter (Dict[str, Any]): Letter data
            detail_function_name (str): Name of detail function
            connection (cx_Oracle.Connection): Database connection
            
        Returns:
            Dict[str, Any]: Letter data with details added
        """
        details_cursor = execute_plsql_function(
            detail_function_name,
            cx_Oracle.CURSOR,
            [letter["letternumber"], letter["claimnumber"]],
            connection
        )
        
        for detail_row in details_cursor:
            letter["details"].append({
                "overpaymentdescription": detail_row[0]
            })
        
        details_cursor.close()
        return letter
    
    def generate_claim_letter(self, 
                             letter_number: str, 
                             claim_number: str,
                             output_dir: str = None) -> List[str]:
        """
        Generate a claim letter for the specified letter and claim numbers.
        
        Args:
            letter_number (str): The letter number to generate
            claim_number (str): The claim number for the letter
            output_dir (str): Optional output directory override
            
        Returns:
            List[str]: List of generated PDF file paths
        """
        if output_dir:
            self.output_dir = output_dir
        
        # Fetch letter data from database
        letter_data = self.fetch_letter_data_from_db(
            "letters.get_claim_letter_cursor",
            [letter_number, claim_number],
            "letters.get_claim_letter_details_cursor"
        )
        
        # Generate filename pattern
        filename_pattern = "{sourcesystemcode}_{claimnumber}_{membershortname}"
        
        # Fill template and generate PDFs
        return self.fill_template(
            letter_data,
            "Claim_Overpayment_Template.docx",
            filename_pattern
        )


def generate_claim_letter(letter_number: str, 
                         claim_number: str,
                         output_dir: str = "/opt/aopdocs") -> List[str]:
    """
    Convenience function to generate a claim letter.
    
    Args:
        letter_number (str): The letter number to generate
        claim_number (str): The claim number for the letter
        output_dir (str): Directory where files should be saved
        
    Returns:
        List[str]: List of generated PDF file paths
    """
    generator = ClaimLetterGenerator(output_dir)
    return generator.generate_claim_letter(letter_number, claim_number)
