"""
Provider letter generation functionality for SimpliPIed.

This module provides functionality for generating provider letters from
database data and templates.
"""

from typing import List, Dict, Any, TYPE_CHECKING
from .base import LetterGenerator
from ..utils.database import execute_plsql_function

if TYPE_CHECKING:
    import cx_Oracle

try:
    import cx_Oracle
except ImportError:
    cx_Oracle = None


class ProviderLetterGenerator(LetterGenerator):
    """Generator for provider letters."""
    
    def _process_letter_row(self, letter_row: tuple) -> Dict[str, Any]:
        """
        Process a provider letter row from database cursor.
        
        Args:
            letter_row (tuple): Row data from database cursor
            
        Returns:
            Dict[str, Any]: Processed provider letter data
        """
        return {
            "letternumber": letter_row[0],
            "sourcesystemcode": letter_row[1],
            "letterdate": letter_row[2],       
            "providertin": letter_row[3],
            "providerattention": letter_row[4],
            "providergroupname": letter_row[5],
            "provideraddressline1": letter_row[6],
            "provideraddressline2": letter_row[7],
            "provideraddressline2_available": letter_row[8],
            "providercity": letter_row[9],
            "providerstatecode": letter_row[10],
            "providerzip": letter_row[11],
            "totaloverpayment": letter_row[12],
            "appealpobox": letter_row[13],
            "appealaddressline1": letter_row[14],
            "appealaddressline2": letter_row[15],
            "appealaddressline2_available": letter_row[16],
            "appealcity": letter_row[17],
            "appealstatecode": letter_row[18],
            "appealzipcode": letter_row[19],
            "blurb": letter_row[20],
            "providercode": letter_row[21],
            "provider_short_name": letter_row[22],
            "claims": []
        }
    
    def _fetch_letter_details(self,
                             letter: Dict[str, Any],
                             detail_function_name: str,
                             connection: Any) -> Dict[str, Any]:
        """
        Fetch provider letter claims from database.
        
        Args:
            letter (Dict[str, Any]): Letter data
            detail_function_name (str): Name of detail function
            connection (cx_Oracle.Connection): Database connection
            
        Returns:
            Dict[str, Any]: Letter data with claims added
        """
        claims_cursor = execute_plsql_function(
            detail_function_name,
            cx_Oracle.CURSOR,
            [letter["letternumber"]],
            connection
        )
        
        for claim_row in claims_cursor:
            letter["claims"].append({
                "notificationdeliveryid": claim_row[0],
                "claimnumber": claim_row[1],
                "claimlinenumber": claim_row[2],
                "membername": claim_row[3],
                "memberid": claim_row[4],
                "patientaccountnumber": claim_row[5],
                "dateofservicefrom": claim_row[6],
                "dateofserviceto": claim_row[7],
                "totalbillamount": claim_row[8],
                "paidbyplan": claim_row[9],
                "claimpaiddate": claim_row[10],
                "checknumber": claim_row[11],
                "overpaidamount": claim_row[12],
                "overpaymentdescription": claim_row[13],
                "overpaymentconcept": claim_row[14],
                "submitdate": claim_row[15],
                "statuscode": claim_row[16],
                "hspceffdate": claim_row[17],
                "hspcenddate": claim_row[18]
            })
        
        claims_cursor.close()
        return letter
    
    def generate_provider_letter(self, 
                                letter_number: str,
                                output_dir: str = None) -> List[str]:
        """
        Generate a provider letter for the specified letter number.
        
        Args:
            letter_number (str): The letter number to generate
            output_dir (str): Optional output directory override
            
        Returns:
            List[str]: List of generated PDF file paths
        """
        if output_dir:
            self.output_dir = output_dir
        
        # Fetch letter data from database
        letter_data = self.fetch_letter_data_from_db(
            "letters.get_provider_letter_cursor",
            [letter_number],
            "letters.get_provider_letter_details_cursor"
        )
        
        # Generate filename pattern
        filename_pattern = "{sourcesystemcode}_{providercode}_{provider_short_name}_{letternumber}"
        
        # Fill template and generate PDFs
        return self.fill_template(
            letter_data,
            "Provider_Overpayment_Template.docx",
            filename_pattern
        )


def generate_provider_letter(letter_number: str,
                           output_dir: str = "/opt/aopdocs") -> List[str]:
    """
    Convenience function to generate a provider letter.
    
    Args:
        letter_number (str): The letter number to generate
        output_dir (str): Directory where files should be saved
        
    Returns:
        List[str]: List of generated PDF file paths
    """
    generator = ProviderLetterGenerator(output_dir)
    return generator.generate_provider_letter(letter_number)
