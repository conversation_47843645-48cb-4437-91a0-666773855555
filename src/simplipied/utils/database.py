"""
Database utilities for SimpliPIed.

This module provides database connection management and password retrieval
functionality shared across the application.
"""

import os
import csv
from typing import Optional, Any, TYPE_CHECKING

if TYPE_CHECKING:
    import cx_Oracle

try:
    import cx_Oracle
    HAS_ORACLE = True
except ImportError:
    HAS_ORACLE = False
    cx_Oracle = None


def get_db_password(user_id: str) -> Optional[str]:
    """
    Retrieve database password for a given user ID from the connection file.
    
    Args:
        user_id (str): The user ID to look up
        
    Returns:
        Optional[str]: The password if found, None otherwise
    """
    user_list = []
    home_directory = os.getenv("HOME")
    filepath = f"{home_directory}/.connect.txt"
    db_password = None

    try:
        with open(filepath, mode='r', newline='') as file:
            # Filter out comment lines
            filtered_lines = (line for line in file if not line.strip().startswith('#'))
            
            reader = csv.DictReader(filtered_lines, delimiter=':')
            for row in reader:
                user_list.append(row)

            for entry in user_list:
                if entry.get("test_user") == user_id:
                    db_password = entry.get("password")
                    break
    except FileNotFoundError:
        raise FileNotFoundError(f"Connection file not found: {filepath}")
    except Exception as e:
        raise Exception(f"Error reading connection file: {e}")

    return db_password


def get_db_connection(username: str = "apps", 
                     host: str = "apex-app.rapidpie.com", 
                     port: int = 1521, 
                     service_name: str = "orcl") -> cx_Oracle.Connection:
    """
    Create and return a database connection.
    
    Args:
        username (str): Database username
        host (str): Database host
        port (int): Database port
        service_name (str): Oracle service name
        
    Returns:
        cx_Oracle.Connection: Database connection object
        
    Raises:
        Exception: If connection fails or password not found
    """
    password = get_db_password(username)
    if not password:
        raise Exception(f"Password not found for user: {username}")
    
    dsn = cx_Oracle.makedsn(host, port, service_name=service_name)
    
    try:
        connection = cx_Oracle.connect(user=username, password=password, dsn=dsn)
        return connection
    except cx_Oracle.Error as e:
        raise Exception(f"Database connection failed: {e}")


def execute_plsql_function(function_name: str, 
                          return_type, 
                          parameters: list = None,
                          connection: cx_Oracle.Connection = None) -> any:
    """
    Execute a PL/SQL function and return the result.
    
    Args:
        function_name (str): Name of the PL/SQL function to call
        return_type: Expected return type (e.g., cx_Oracle.CURSOR, str, int)
        parameters (list): List of parameters to pass to the function
        connection (cx_Oracle.Connection): Existing connection to use
        
    Returns:
        any: Result of the function call
    """
    if connection is None:
        connection = get_db_connection()
        close_connection = True
    else:
        close_connection = False
    
    try:
        cursor = connection.cursor()
        result = cursor.callfunc(function_name, return_type, parameters or [])
        cursor.close()
        return result
    finally:
        if close_connection:
            connection.close()
