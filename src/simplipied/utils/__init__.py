"""
Utility modules for SimpliPIed.

This module provides shared functionality including database connections,
password management, and common utilities.
"""

from .database import get_db_connection, get_db_password, execute_plsql_function
from .templates import render_template, render_json_template, safe_eval_template
from .file_operations import (
    get_home_directory,
    get_template_path,
    ensure_directory_exists,
    convert_docx_to_pdf,
    safe_remove_file
)

__all__ = [
    'get_db_connection',
    'get_db_password',
    'execute_plsql_function',
    'render_template',
    'render_json_template',
    'safe_eval_template',
    'get_home_directory',
    'get_template_path',
    'ensure_directory_exists',
    'convert_docx_to_pdf',
    'safe_remove_file'
]
