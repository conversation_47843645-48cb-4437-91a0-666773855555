"""
Template processing utilities for SimpliPIed.

This module provides template rendering functionality using Jinja2.
"""

import json
from jinja2 import Template
from typing import Dict, Any, Union


def render_template(template_str: str, context: Dict[str, Any]) -> str:
    """
    Render a Jinja2 template with the given context.
    
    Args:
        template_str (str): The template string
        context (Dict[str, Any]): Context data for template rendering
        
    Returns:
        str: Rendered template string
    """
    template = Template(template_str)
    return template.render(context)


def render_json_template(template_str: str, context: Dict[str, Any]) -> Union[list, dict]:
    """
    Render a Jinja2 template that produces JSON and parse it.
    
    Args:
        template_str (str): The template string that produces JSON
        context (Dict[str, Any]): Context data for template rendering
        
    Returns:
        Union[list, dict]: Parsed JSON data
        
    Note:
        This is safer than using eval() for JSON template rendering.
    """
    rendered = render_template(template_str, context)
    try:
        return json.loads(rendered)
    except json.JSONDecodeError as e:
        raise ValueError(f"Template did not produce valid JSON: {e}")


def safe_eval_template(template_str: str, context: Dict[str, Any]) -> Any:
    """
    Safely evaluate a template that produces Python data structures.
    
    This is a safer alternative to eval() for simple data structures.
    
    Args:
        template_str (str): The template string
        context (Dict[str, Any]): Context data for template rendering
        
    Returns:
        Any: Evaluated result
        
    Warning:
        This still uses eval() but is intended as a transitional function.
        Consider using render_json_template() for JSON data instead.
    """
    rendered = render_template(template_str, context)
    # TODO: Replace with safer JSON parsing where possible
    return eval(rendered)
