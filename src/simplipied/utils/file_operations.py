"""
File operation utilities for SimpliPIed.

This module provides common file operations and path management.
"""

import os
import subprocess
from typing import Optional


def get_home_directory() -> str:
    """
    Get the user's home directory.
    
    Returns:
        str: Path to home directory
    """
    return os.getenv("HOME", os.path.expanduser("~"))


def get_template_path(template_name: str, subdir: str = "rapidpie/Reports") -> str:
    """
    Get the full path to a template file.
    
    Args:
        template_name (str): Name of the template file
        subdir (str): Subdirectory under home where templates are stored
        
    Returns:
        str: Full path to template file
    """
    home_directory = get_home_directory()
    return os.path.join(home_directory, subdir, template_name)


def ensure_directory_exists(directory_path: str, mode: int = 0o755) -> None:
    """
    Ensure a directory exists, creating it if necessary.
    
    Args:
        directory_path (str): Path to directory
        mode (int): Directory permissions (default: 0o755)
    """
    os.makedirs(directory_path, mode=mode, exist_ok=True)


def convert_docx_to_pdf(docx_path: str, output_dir: str) -> bool:
    """
    Convert a DOCX file to PDF using LibreOffice.
    
    Args:
        docx_path (str): Path to the DOCX file
        output_dir (str): Directory where PDF should be saved
        
    Returns:
        bool: True if conversion successful, False otherwise
    """
    try:
        subprocess.run([
            "libreoffice",
            "--headless",
            "--convert-to", "pdf",
            "--outdir", output_dir,
            docx_path
        ], check=True)
        return True
    except subprocess.CalledProcessError as e:
        print(f"Failed to convert DOCX to PDF: {e}")
        return False
    except FileNotFoundError:
        print("LibreOffice not found. Please install LibreOffice to enable PDF conversion.")
        return False


def safe_remove_file(file_path: str) -> bool:
    """
    Safely remove a file if it exists.
    
    Args:
        file_path (str): Path to file to remove
        
    Returns:
        bool: True if file was removed or didn't exist, False if error occurred
    """
    try:
        if os.path.exists(file_path):
            os.remove(file_path)
        return True
    except OSError as e:
        print(f"Error removing file {file_path}: {e}")
        return False
