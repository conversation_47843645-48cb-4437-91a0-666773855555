"""
Excel export functionality for SimpliPIed.

This module provides functions for generating Excel files from templates
and data sources.
"""

from openpyxl import Workbook
from typing import List, Dict, Any, Optional
from ..utils.templates import safe_eval_template


def generate_excel_from_template(context: Dict[str, Any], 
                                template_str: str, 
                                output_file: str,
                                sheet_title: str = "Data") -> None:
    """
    Generate an Excel file from a Jinja2 template and context data.
    
    Args:
        context (Dict[str, Any]): Context data for template rendering
        template_str (str): Jinja2 template string that produces data structure
        output_file (str): Path where Excel file should be saved
        sheet_title (str): Title for the Excel worksheet
        
    Raises:
        ValueError: If template doesn't produce valid data structure
        Exception: If Excel file creation fails
    """
    try:
        # Render the template to get data
        rendered_data = safe_eval_template(template_str, context)
        
        if not isinstance(rendered_data, list) or not rendered_data:
            raise ValueError("Template must produce a non-empty list of dictionaries")
        
        # Create Excel workbook
        wb = Workbook()
        ws = wb.active
        ws.title = sheet_title
        
        # Write headers from first row
        if isinstance(rendered_data[0], dict):
            headers = list(rendered_data[0].keys())
            ws.append(headers)
            
            # Write data rows
            for row in rendered_data:
                if isinstance(row, dict):
                    ws.append(list(row.values()))
                else:
                    raise ValueError("All data items must be dictionaries")
        else:
            raise ValueError("Data items must be dictionaries")
        
        # Save the Excel file
        wb.save(output_file)
        
    except Exception as e:
        raise Exception(f"Failed to generate Excel file: {e}")


def create_excel_from_data(data: List[Dict[str, Any]], 
                          output_file: str,
                          sheet_title: str = "Data") -> None:
    """
    Create an Excel file directly from a list of dictionaries.
    
    Args:
        data (List[Dict[str, Any]]): List of dictionaries containing the data
        output_file (str): Path where Excel file should be saved
        sheet_title (str): Title for the Excel worksheet
        
    Raises:
        ValueError: If data is not in expected format
        Exception: If Excel file creation fails
    """
    if not isinstance(data, list) or not data:
        raise ValueError("Data must be a non-empty list of dictionaries")
    
    try:
        wb = Workbook()
        ws = wb.active
        ws.title = sheet_title
        
        # Write headers from first row
        if isinstance(data[0], dict):
            headers = list(data[0].keys())
            ws.append(headers)
            
            # Write data rows
            for row in data:
                if isinstance(row, dict):
                    ws.append(list(row.values()))
                else:
                    raise ValueError("All data items must be dictionaries")
        else:
            raise ValueError("Data items must be dictionaries")
        
        # Save the Excel file
        wb.save(output_file)
        
    except Exception as e:
        raise Exception(f"Failed to create Excel file: {e}")


def get_sample_data() -> List[Dict[str, Any]]:
    """
    Get sample employee data for testing purposes.
    
    Returns:
        List[Dict[str, Any]]: Sample employee data
    """
    return [
        {"name": "Alice", "age": 30, "department": "HR"},
        {"name": "Bob", "age": 25, "department": "Engineering"},
        {"name": "Charlie", "age": 28, "department": "Marketing"},
    ]


def get_sample_template() -> str:
    """
    Get sample Jinja2 template for testing purposes.
    
    Returns:
        str: Sample template string
    """
    return """
[
    {% for user in users %}
    {
        "Name": "{{ user.name }}",
        "Age": {{ user.age }},
        "Department": "{{ user.department }}"
    }{% if not loop.last %},{% endif %}
    {% endfor %}
]
"""
