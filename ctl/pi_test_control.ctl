--------------------------------------------------------------------------
--
-- PROGRAM:   pi_test_control.ctl
--
-- PURPOSE:   This control file is used to test the run_sql_loader shell
--            function.
-- 
-- AUTHOR:    <PERSON>
--
-- USAGE:     sqlldr userid=apps control=pi_test_control.ctl
--
-- NOTES:     When utilized with the pi_run_sql_loader function, the
--            DATA_FILE_NAME value will be replaced with the actual data
--            file name.
--
-- Copyright (c) 2025 Kevin <PERSON>
-- All rights reserved.
--
-- This software is proprietary and confidential. Unauthorized copying of this
-- file, via any medium, is strictly prohibited without the express permission
-- of the author or owning organization.
--
----------------------------------------------------------------------------
-- OPTIONS (SKIP = 1)  -- Header
LOAD DATA
INFILE '/var/tmp/PI_TEST_DATA_FILE.dat'
APPEND
INTO TABLE pi_test_control
WHEN (input_line <> BLANKS) 
-- FIELDS TERMINATED BY '~' OPTIONALLY ENCLOSED BY '"'
TRAILING NULLCOLS
(
   input_line     POSITION(1:1000) CHAR "TRIM(:input_line)",
   --
   source         CONSTANT "DATA_FILE_NAME",
   load_date      SYSDATE,
   status         CONSTANT "NEW",
   record_no      SEQUENCE(MAX)
)

-- Complete
