--------------------------------------------------------------------------
--
-- PROGRAM:   pi_host_request_log_stg.ctl
--
-- PURPOSE:   This control file is used to test the run_sql_loader shell
--            function.
--
-- AUTHOR:    <PERSON>
--
-- USAGE:     sqlldr userid=apps control=pi_host_request_log_stg.ctl
--
-- NOTES:     When utilized with the run_sql_loader function, the
--            DATA_FILE_NAME value will be replaced with the actual data
--            file name.
-- 
-- Copyright (c) 2025 <PERSON>
-- All rights reserved.
--
-- This software is proprietary and confidential. Unauthorized copying of this
-- file, via any medium, is strictly prohibited without the express permission
-- of the author or owning organization.
--
----------------------------------------------------------------------------
-- OPTIONS (SKIP = 1)  -- no header
LOAD DATA
INFILE '/var/tmp/temp_pi_host_request_log_stg.dat'
APPEND
INTO TABLE pi_host_request_log_stg
WHEN (request_id <> BLANKS) 
FIELDS TERMINATED BY ','
-- TRAILING NULLCOLS
(
   request_id     CHAR(10),
   log_file       CHAR(100),
   log_contents   LOBFILE(log_file) TERMINATED BY EOF
)

-- Complete
