# SimpliPIed

A comprehensive system for processing insurance claims and generating letters.

## Overview

SimpliPIed is a Python-based system that provides functionality for:

- **Excel file generation**: Create Excel files from Jinja2 templates and data sources
- **Letter generation**: Generate claim and provider letters from database data and Word templates
- **Database integration**: Connect to Oracle databases and execute PL/SQL functions
- **Template processing**: Render Jinja2 templates with context data

## Project Structure

The project follows Python best practices with a clean package structure:

```
simplipied/
├── README.md
├── requirements.txt
├── setup.py
├── pyproject.toml
├── src/
│   └── simplipied/
│       ├── __init__.py
│       ├── excel/
│       │   ├── __init__.py
│       │   └── export.py
│       ├── letters/
│       │   ├── __init__.py
│       │   ├── base.py
│       │   ├── claim_generator.py
│       │   └── provider_generator.py
│       └── utils/
│           ├── __init__.py
│           ├── database.py
│           ├── templates.py
│           └── file_operations.py
├── tests/
│   ├── __init__.py
│   ├── excel/
│   ├── letters/
│   └── utils/
├── bin/                           # Shell scripts and CLI tools
│   ├── pi_export_excel_new.py    # New Python CLI scripts
│   ├── pi_generate_claim_letter_new.py
│   ├── pi_generate_provider_letter_new.py
│   └── ...                       # Original shell scripts
├── sql/                          # SQL scripts
├── apex/                         # APEX applications
└── reports/                      # Report templates
```

## Installation

### Prerequisites

- Python 3.8 or higher
- Oracle Instant Client (for database connectivity)
- LibreOffice (for PDF conversion)

### Install Dependencies

```bash
pip install -r requirements.txt
```

### Development Installation

For development, install in editable mode with development dependencies:

```bash
pip install -e ".[dev]"
```

## Usage

### Command Line Interface

The new CLI scripts provide the same functionality as the original scripts but with improved structure:

#### Excel Export

```bash
# Using sample data
./bin/pi_export_excel_new.py --sample --output employees.xlsx

# Using custom template and data
./bin/pi_export_excel_new.py --template template.j2 --data data.json --output output.xlsx
```

#### Claim Letter Generation

```bash
./bin/pi_generate_claim_letter_new.py --letter_number 2025033000170 --claim_number 20241029821052100079
```

#### Provider Letter Generation

```bash
./bin/pi_generate_provider_letter_new.py --letter_number 2025033000170
```

### Python API

You can also use the package directly in Python code:

```python
from simplipied.excel import generate_excel_from_template
from simplipied.letters import generate_claim_letter, generate_provider_letter

# Generate Excel file
context = {"users": [{"name": "Alice", "age": 30, "department": "HR"}]}
template = '[{"Name": "{{ user.name }}", "Age": {{ user.age }}}]'
generate_excel_from_template(context, template, "output.xlsx")

# Generate letters
generate_claim_letter("2025033000170", "20241029821052100079")
generate_provider_letter("2025033000170")
```

## Configuration

### Database Connection

The system reads database credentials from `~/.connect.txt` in the following format:

```
test_user:apps
password:your_password
host:localhost

test_user:other_user
password:other_password
host:localhost
```

### Template Paths

By default, templates are expected in `~/rapidpie/Reports/`. You can customize this by:

1. Setting environment variables
2. Modifying the `get_template_path()` function
3. Passing custom paths to the generation functions

## Testing

Run the test suite:

```bash
# Run all tests
python -m pytest tests/

# Run specific test module
python -m pytest tests/excel/test_export.py

# Run with coverage
python -m pytest tests/ --cov=simplipied --cov-report=html
```

Run individual test files:

```bash
python -m unittest tests.excel.test_export
python -m unittest tests.utils.test_database
```

## Migration from Original Scripts

The new package structure maintains backward compatibility while providing improved organization:

### Original vs New Scripts

| Original Script | New Script | Package Function |
|----------------|------------|------------------|
| `pi_export_excel.py` | `pi_export_excel_new.py` | `simplipied.excel.generate_excel_from_template` |
| `pi_generate_claim_letter.py` | `pi_generate_claim_letter_new.py` | `simplipied.letters.generate_claim_letter` |
| `pi_generate_provider_letter.py` | `pi_generate_provider_letter_new.py` | `simplipied.letters.generate_provider_letter` |

### Key Improvements

1. **Separation of Concerns**: Business logic separated from CLI interface
2. **Reusable Components**: Shared utilities for database, templates, and file operations
3. **Better Error Handling**: Comprehensive exception handling and validation
4. **Type Hints**: Improved code documentation and IDE support
5. **Unit Tests**: Comprehensive test coverage for all modules
6. **Package Structure**: Follows Python packaging best practices

## Development

### Code Style

The project uses Black for code formatting:

```bash
black src/ tests/
```

### Adding New Features

1. Add functionality to the appropriate module in `src/simplipied/`
2. Add corresponding tests in `tests/`
3. Update CLI scripts if needed
4. Update documentation

### Shell Scripts

The original shell scripts remain in the `bin/` directory and continue to work as before. The new Python package provides additional functionality and better structure for Python-based operations.

## License

Copyright (c) 2025 Kevin LeQuire. All rights reserved.

This software is proprietary and confidential. Unauthorized copying of this software, via any medium, is strictly prohibited without the express permission of the author or owning organization.



## Getting started

To make it easy for you to get started with GitLab, here's a list of recommended next steps.

Already a pro? Just edit this README.md and make it your own. Want to make it easy? [Use the template at the bottom](#editing-this-readme)!

## Add your files

- [ ] [Create](https://docs.gitlab.com/ee/user/project/repository/web_editor.html#create-a-file) or [upload](https://docs.gitlab.com/ee/user/project/repository/web_editor.html#upload-a-file) files
- [ ] [Add files using the command line](https://docs.gitlab.com/topics/git/add_files/#add-files-to-a-git-repository) or push an existing Git repository with the following command:

```
cd existing_repo
git remote add origin http://gitlab.rapidpie.com/simplipied/simplipied.git
git branch -M main
git push -uf origin main
```

## Integrate with your tools

- [ ] [Set up project integrations](http://gitlab.rapidpie.com/simplipied/simplipied/-/settings/integrations)

## Collaborate with your team

- [ ] [Invite team members and collaborators](https://docs.gitlab.com/ee/user/project/members/)
- [ ] [Create a new merge request](https://docs.gitlab.com/ee/user/project/merge_requests/creating_merge_requests.html)
- [ ] [Automatically close issues from merge requests](https://docs.gitlab.com/ee/user/project/issues/managing_issues.html#closing-issues-automatically)
- [ ] [Enable merge request approvals](https://docs.gitlab.com/ee/user/project/merge_requests/approvals/)
- [ ] [Set auto-merge](https://docs.gitlab.com/user/project/merge_requests/auto_merge/)

## Test and Deploy

Use the built-in continuous integration in GitLab.

- [ ] [Get started with GitLab CI/CD](https://docs.gitlab.com/ee/ci/quick_start/)
- [ ] [Analyze your code for known vulnerabilities with Static Application Security Testing (SAST)](https://docs.gitlab.com/ee/user/application_security/sast/)
- [ ] [Deploy to Kubernetes, Amazon EC2, or Amazon ECS using Auto Deploy](https://docs.gitlab.com/ee/topics/autodevops/requirements.html)
- [ ] [Use pull-based deployments for improved Kubernetes management](https://docs.gitlab.com/ee/user/clusters/agent/)
- [ ] [Set up protected environments](https://docs.gitlab.com/ee/ci/environments/protected_environments.html)

***

# Editing this README

When you're ready to make this README your own, just edit this file and use the handy template below (or feel free to structure it however you want - this is just a starting point!). Thanks to [makeareadme.com](https://www.makeareadme.com/) for this template.

## Suggestions for a good README

Every project is different, so consider which of these sections apply to yours. The sections used in the template are suggestions for most open source projects. Also keep in mind that while a README can be too long and detailed, too long is better than too short. If you think your README is too long, consider utilizing another form of documentation rather than cutting out information.

## Name
Choose a self-explaining name for your project.

## Description
Let people know what your project can do specifically. Provide context and add a link to any reference visitors might be unfamiliar with. A list of Features or a Background subsection can also be added here. If there are alternatives to your project, this is a good place to list differentiating factors.

## Badges
On some READMEs, you may see small images that convey metadata, such as whether or not all the tests are passing for the project. You can use Shields to add some to your README. Many services also have instructions for adding a badge.

## Visuals
Depending on what you are making, it can be a good idea to include screenshots or even a video (you'll frequently see GIFs rather than actual videos). Tools like ttygif can help, but check out Asciinema for a more sophisticated method.

## Installation
Within a particular ecosystem, there may be a common way of installing things, such as using Yarn, NuGet, or Homebrew. However, consider the possibility that whoever is reading your README is a novice and would like more guidance. Listing specific steps helps remove ambiguity and gets people to using your project as quickly as possible. If it only runs in a specific context like a particular programming language version or operating system or has dependencies that have to be installed manually, also add a Requirements subsection.

## Usage
Use examples liberally, and show the expected output if you can. It's helpful to have inline the smallest example of usage that you can demonstrate, while providing links to more sophisticated examples if they are too long to reasonably include in the README.

## Support
Tell people where they can go to for help. It can be any combination of an issue tracker, a chat room, an email address, etc.

## Roadmap
If you have ideas for releases in the future, it is a good idea to list them in the README.

## Contributing
State if you are open to contributions and what your requirements are for accepting them.

For people who want to make changes to your project, it's helpful to have some documentation on how to get started. Perhaps there is a script that they should run or some environment variables that they need to set. Make these steps explicit. These instructions could also be useful to your future self.

You can also document commands to lint the code or run tests. These steps help to ensure high code quality and reduce the likelihood that the changes inadvertently break something. Having instructions for running tests is especially helpful if it requires external setup, such as starting a Selenium server for testing in a browser.

## Authors and acknowledgment
Show your appreciation to those who have contributed to the project.

## License
For open source projects, say how it is licensed.

## Project status
If you have run out of energy or time for your project, put a note at the top of the README saying that development has slowed down or stopped completely. Someone may choose to fork your project or volunteer to step in as a maintainer or owner, allowing your project to keep going. You can also make an explicit request for maintainers.
