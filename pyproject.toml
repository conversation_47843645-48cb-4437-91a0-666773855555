[build-system]
requires = ["setuptools>=45", "wheel"]
build-backend = "setuptools.build_meta"

[project]
name = "simplipied"
version = "1.0.0"
description = "A comprehensive system for processing insurance claims and generating letters"
readme = "README.md"
requires-python = ">=3.8"
license = {text = "MIT"}
authors = [
    {name = "<PERSON>uire", email = "<EMAIL>"},
]
classifiers = [
    "Development Status :: 4 - Beta",
    "Intended Audience :: Healthcare Industry",
    "License :: OSI Approved :: MIT License",
    "Operating System :: OS Independent",
    "Programming Language :: Python :: 3",
    "Programming Language :: Python :: 3.8",
    "Programming Language :: Python :: 3.9",
    "Programming Language :: Python :: 3.10",
    "Programming Language :: Python :: 3.11",
]
dependencies = [
    "jinja2>=3.0.0",
    "openpyxl>=3.0.0",
    "cx_Oracle>=8.0.0",
    "python-docx-template>=0.16.0",
]

[project.optional-dependencies]
dev = [
    "pytest>=6.0.0",
    "pytest-cov>=2.0.0",
    "black>=21.0.0",
    "flake8>=3.8.0",
]

[project.scripts]
pi-export-excel = "simplipied.cli.excel:main"
pi-generate-claim-letter = "simplipied.cli.claim_letter:main"
pi-generate-provider-letter = "simplipied.cli.provider_letter:main"

[project.urls]
Homepage = "https://github.com/your-org/simplipied"
Repository = "https://github.com/your-org/simplipied.git"
Issues = "https://github.com/your-org/simplipied/issues"

[tool.setuptools.packages.find]
where = ["src"]

[tool.setuptools.package-dir]
"" = "src"

[tool.black]
line-length = 88
target-version = ['py38']
include = '\.pyi?$'
extend-exclude = '''
/(
  # directories
  \.eggs
  | \.git
  | \.hg
  | \.mypy_cache
  | \.tox
  | \.venv
  | build
  | dist
)/
'''

[tool.pytest.ini_options]
testpaths = ["tests"]
python_files = ["test_*.py"]
python_classes = ["Test*"]
python_functions = ["test_*"]
addopts = "-v --tb=short"
