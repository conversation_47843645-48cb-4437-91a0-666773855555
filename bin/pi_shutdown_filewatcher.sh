#!/bin/bash
#-----------------------------------------------------------------------------
#
#   PROCEDURE:    pi_shutdown_filewatcher.sh
#
#   PURPOSE:      This script is used to shutdown the filewatcher daemon 
#                 process.
#
#   PARAMETERS:   None.
#
#   USAGE:        At the UNIX prompt, type:
#                    pi_shutdown_filewatcher.sh
#
#   AUTHOR:       <PERSON>
#
#
# Copyright (c) 2025 <PERSON>re
# All rights reserved.
#
# This software is proprietary and confidential. Unauthorized copying of this
# file, via any medium, is strictly prohibited without the express permission
# of the author or owning organization.
#
#-----------------------------------------------------------------------------


# Include the "common" functions
. $(dirname $0)/pi_common.sh

# Local variables
typeset result


# The filewatcher daemon process looks for the pi_stop_filewatcher_daemon
# file in the /var/tmp directory. If it finds it, it shuts down gracefully.
# The daemon process will remove the file as it shuts down.

# Check if the daemon is currently running.  If so shut it down.
result=$(ps -ef | grep pi_filewatcher_daemon.sh | grep -v grep)

if [ "$result" = "" ]
then
   printf "%s\n" "Filewatcher daemon not currently running."
else
   printf "%s\n" "Shutting down the filewatcher daemon ..."

   # Create the semiphore file.
   touch /var/tmp/pi_stop_filewatcher_daemon

   # Change the permissions on the file
   chmod 777 /var/tmp/pi_stop_filewatcher_daemon

   # Wait for the file to be removed by the filewatcher daemon process
   while [ -r /var/tmp/pi_stop_filewatcher_daemon ]
   do
      sleep 10 
      printf "%s\n" "...waiting (`date`)"
   done

   printf "%s\n\n" ...stopped.

fi


unset result

# Complete
