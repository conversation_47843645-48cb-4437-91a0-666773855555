# !/usr/bin/env python3
# write_claims_pivot_xlsx.py
import pandas as pd
import xlsxwriter



def write_claims_pivot_xlsx(claims_rows, out_path="claims_with_pivot.xlsx"):
    df = pd.DataFrame(claims_rows)
    df["OpenPayableAmount"] = pd.to_numeric(df["OpenPayableAmount"], errors="coerce").fillna(0.0)

    with pd.ExcelWriter(out_path, engine="xlsxwriter") as writer:
        df.to_excel(writer, sheet_name="Summary Claims", index=False)
        wb = writer.book
        ws_pivot = wb.add_worksheet("Pivot")

        # Define table from the data
        nrows, ncols = df.shape
        table_cols = [{"header": col} for col in df.columns]
        ws_claims = writer.sheets["Summary Claims"]
        ws_claims.add_table(0, 0, nrows, ncols - 1, {"name": "ClaimsTable", "columns": table_cols})

        # Add PivotTable
        ws_pivot.add_pivot_table({
            "source": f"'Summary Claims'!$A$1:${chr(65 + ncols - 1)}${nrows + 1}",
            "name": "PivotTable1",
            "location": "B3",
            "rows": [{"data": "ProviderName"}],
            "columns": [{"data": "LOB"}],
            "values": [{"data": "OpenPayableAmount", "function": "sum"}],
        })

    print(f"✅ Created {out_path} with a PivotTable.")



if __name__ == "__main__":

    claims = [
        {"ProviderName": "ABC Clinic", "LOB": "Medicare", "OpenPayableAmount": 1200.50},
        {"ProviderName": "ABC Clinic", "LOB": "Commercial", "OpenPayableAmount": 300.00},
        {"ProviderName": "Beta Medical", "LOB": "Medicare", "OpenPayableAmount": 950.00},
        {"ProviderName": "Beta Medical", "LOB": "Commercial", "OpenPayableAmount": 110.00},
    ]
    write_claims_pivot_xlsx(claims, "claims_with_pivot.xlsx")
