#!/bin/bash
#-----------------------------------------------------------------------------
#
#   PROCEDURE:    test_pi_email.sh
#
#   PURPOSE:      This script contains the unit tests for the Rapid PIE 
#                 email processing functions.
#
#   PARAMETERS:   None.
#
#   USAGE:        From the command-line, type:
#                    ./test_pi_email.sh
#
#   AUTHOR:       <PERSON>
#
#
# Copyright (c) 2025 Kevin <PERSON>uire
# All rights reserved.
#
# This software is proprietary and confidential. Unauthorized copying of this
# file, via any medium, is strictly prohibited without the express permission
# of the author or owning organization.
#
#-----------------------------------------------------------------------------

#suite()
suite_test_off()
{
   suite_addTest test_list_email_headers_gets_email_header_list
   suite_addTest test_add_email_connection_creates_file_if_missing
   suite_addTest test_email_connection_exists_detects_connection
   suite_addTest test_email_connection_exists_detects_missing_connection
}

# Before all
oneTimeSetUp()
{

   # Include the functions
   . $(dirname $0)/pi_common.sh

   unset PI_EMAIL_FUNCTIONS
   . $(dirname $0)/pi_email.sh


   # echo "{" > /var/tmp/mail_connect.txt
   # echo "\"mailbox\":\"imaps://rapidpie-com-mail.dynu.com:993/INBOX\"," >> /var/tmp/mail_connect.txt
   # echo "\"mailbox_user\":\"<EMAIL>\"," >> /var/tmp/mail_connect.txt
   # echo "\"mailbox_password\":\"abc123\"" >> /var/tmp/mail_connect.txt
   # echo "}" >> /var/tmp/mail_connect.txt
   add_email_connection "imaps://rapidpie-com-mail.dynu.com:993/INBOX" \
      "<EMAIL>" "abc123" /var/tmp/mail_connect.txt

   # To recreate the test email, run the following:
   # pi_send_email.sh -<EMAIL> -s"Unit Test Email" "This test supports unit testing."
}

# After all
oneTimeTearDown()
{
   rm /var/tmp/mail_connect.txt 2> /dev/null
   :
}


# get_mailbox tests -----------------------------------------------------------
test_get_mailbox_detects_missing_file()
{
   expected="ERROR: Mail connect file"
   actual=$(get_mailbox non-existing_file.txt)
   assertContains "${actual} CONTAINS ${expected}" "${actual}" "${expected}"
}


test_get_mailbox_returns_mailbox_from_file()
{
   expected="imaps://rapidpie-com-mail.dynu.com:993/INBOX"
   actual=$(get_mailbox "/var/tmp/mail_connect.txt")
   assertEquals "${expected} == ${actual}" "${expected}" "${actual}"
}


# get_mailbox_user tests ------------------------------------------------------
test_get_mailbox_user_returns_user_id_from_file()
{
   expected="<EMAIL>"
   actual=$(get_mailbox_user "/var/tmp/mail_connect.txt")
   assertEquals "${expected} == ${actual}" "${expected}" "${actual}"
}


# get_mailbox_password tests --------------------------------------------------
test_get_mailbox_password_returns_password_from_file()
{
   expected="abc123"
   actual=$(get_mailbox_password "/var/tmp/mail_connect.txt")
   assertEquals "${expected} == ${actual}" "${expected}" "${actual}"
}


# list_email_headers tests ----------------------------------------------------
test_list_email_headers_gets_email_header_list()
{
   # Relies of current state. Add new test email.
   expected="Test mail 2"
   actual=$(list_email_headers)
   assertContains "${actual} CONTAINS ${expected}" "${actual}" "${expected}"
}



# get_email_subject tests -----------------------------------------------------
test_get_email_subject_gets_subject()
{
   typeset test_message_number=$(get_email_message_number "Unit Test Email")

   if [ "$test_message_number" = "0" ]
   then
      echo "... Skipped; test message not found."
   else
      # Hardcoded to existing email. Skip if not found.
      expected="Unit Test Email"
      actual=$(get_email_subject $test_message_number) 
      assertEquals "${expected} == ${actual}" "${expected}" "${actual}"
   fi

   unset test_message_number
}


# get_email_sender tests ------------------------------------------------------
test_get_email_sender_gets_from_address()
{
   typeset test_message_number=$(get_email_message_number "Unit Test Email")

   if [ "$test_message_number" = "0" ]
   then
      echo "... Skipped; test message not found."
   else
      # Hardcoded to existing email. Skip if not found.
      expected="LeQuire"
      actual=$(get_email_sender $test_message_number) 
      assertContains "${actual} CONTAINS ${expected}" "${actual}" "${expected}"
   fi

   unset test_message_number
}


# get_email_date tests --------------------------------------------------------
test_get_email_date_gets_sent_date()
{
   typeset test_message_number=$(get_email_message_number "Unit Test Email")

   if [ "$test_message_number" = "0" ]
   then
      echo "... Skipped; test message not found."
   else
      # Hardcoded to existing email. Skip if not found.
      expected="Tue, 26 Nov 2024"
      actual=$(get_email_date $test_message_number)
      assertContains "${actual} CONTAINS ${expected}" "${actual}" "${expected}"
   fi

   unset test_message_number
}



# execute_mailx_command tests -------------------------------------------------
test_execute_mailx_command_creates_command_file()
{
   typeset mailx_command="header"
   typeset command_results=$(execute_mailx_command "$mailx_command")
   typeset test_message_number=$(get_email_message_number "Unit Test Email")

   if [ "$test_message_number" = "0" ]
   then
      echo "... Skipped; test message not found."
   else
      expected="Unit Test Email"
      actual=$(echo $command_results)
      assertContains "${actual} CONTAINS ${expected}" "${actual}" "${expected}"
   fi

   unset mailx_command
   unset command_results
   unset test_message_number
}


# get_email_message_number tests ----------------------------------------------
test_get_email_message_number_finds_email_with_subject()
{
   typeset test_message_number=$(get_email_message_number "Unit Test Email")


   if [ "$test_message_number" = "0" ]
   then
      echo "... Skipped; test message not found."
   else
      expected="$test_message_number"
      actual=$(get_email_message_number "Unit Test Email")
      assertEquals "${expected} == ${actual}" "${expected}" "${actual}"
   fi

   unset test_message_number
}

test_get_email_message_number_returns_zero_if_subject_not_found()
{
   expected="0"
   actual=$(get_email_message_number "Non-existent Email")
   assertEquals "${expected} == ${actual}" "${expected}" "${actual}"
}



# move_email tests ------------------------------------------------------------
test_move_email_copies_email_to_folder()
{
   typeset test_message_subject="Unit Test Email"
   typeset test_message_number=$(get_email_message_number "$test_message_subject")
   typeset move_results


   if [ "$test_message_number" = "0" ]
   then
      echo "... Skipped; test message not found."
   else
      expected="$test_message_subject"

      move_results=$(move_email -sN $test_message_number "test_folder")

      actual=$(execute_mailx_command "folder +test_folder")
      
      assertContains "${actual} CONTAINS ${expected}" "${actual}" "${expected}"
   fi

   rm +test_folder
   unset test_message_subject
   unset test_message_number
   unset move_results
}



# add_email_connection tests --------------------------------------------------
test_add_email_connection_creates_file_if_missing()
{
   typeset connection_file="/var/tmp/temp_mail_connect.txt"

   expected="$connection_file"
   add_email_connection "imaps://rapidpie-com-mail.dynu.com:993/INBOX" \
      "<EMAIL>" "test_password" $connection_file

   actual="$(ls $connection_file)"

   assertEquals "${expected} == ${actual}" "${expected}" "${actual}"

   rm $connection_file 2> /dev/null
   unset connection_file
}



# email_connection_exists tests -----------------------------------------------
test_email_connection_exists_detects_connection()
{
   typeset connection_file="/var/tmp/temp_mail_connect.txt"

   expected="YES"
   add_email_connection "imaps://rapidpie-com-mail.dynu.com:993/INBOX" \
      "<EMAIL>" "test_password" $connection_file

   actual="$(email_connection_exists "imaps://rapidpie-com-mail.dynu.com:993/INBOX" "<EMAIL>" "$connection_file")"

   assertEquals "${expected} == ${actual}" "${expected}" "${actual}"

   rm $connection_file 2> /dev/null
   unset connection_file
}


test_email_connection_exists_detects_missing_connection()
{
   typeset connection_file="/var/tmp/temp_mail_connect.txt"

   expected="NO"
   add_email_connection "imaps://rapidpie-com-mail.dynu.com:993/INBOX" \
      "<EMAIL>" "test_password" $connection_file

   actual="$(email_connection_exists "imaps://rapidpie-com-mail.dynu.com:993/INBOX" \
                "<EMAIL>" "$connection_file")"

   assertEquals "${expected} == ${actual}" "${expected}" "${actual}"

   rm $connection_file 2> /dev/null
   unset connection_file
}



# Source shunit2 (run the tests) ---------------------------------------------
. shunit2


# Complete
