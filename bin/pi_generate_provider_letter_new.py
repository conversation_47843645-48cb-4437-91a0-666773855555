#!/usr/bin/env python3
"""
Provider letter generation CLI for SimpliPIed.

This script provides a command-line interface for generating provider letters
using the SimpliPIed package.
"""

import sys
import os
import argparse

# Add the src directory to Python path
sys.path.insert(0, os.path.join(os.path.dirname(os.path.dirname(__file__)), 'src'))

from simplipied.letters import generate_provider_letter


def parse_args():
    """Parse command line arguments."""
    parser = argparse.ArgumentParser(description="Generate provider letter.")
    parser.add_argument(
        "--letter_number",
        required=True,
        help="Letter number to generate (e.g., 2025033000170)"
    )
    parser.add_argument(
        "--output_dir",
        default="/opt/aopdocs",
        help="Output directory for generated files (default: /opt/aopdocs)"
    )
    return parser.parse_args()


def main():
    """Main entry point."""
    args = parse_args()
    
    print(f"Letter Number: {args.letter_number}")
    print(f"Output Directory: {args.output_dir}")
    
    try:
        # Generate provider letter
        generated_files = generate_provider_letter(
            args.letter_number,
            args.output_dir
        )
        
        if generated_files:
            print(f"\nSuccessfully generated {len(generated_files)} file(s):")
            for file_path in generated_files:
                print(f"  - {file_path}")
        else:
            print("No files were generated.")
            sys.exit(1)
            
    except Exception as e:
        print(f"Error generating provider letter: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
