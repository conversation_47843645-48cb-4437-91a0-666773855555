#!/bin/bash
#-----------------------------------------------------------------------------
#
#   PROCEDURE:    pi_send_letters_to_postgrid.sh
#
#   PURPOSE:      This script checks the AOP document directory /opt/aopdocs) 
#                 for letter files and, if found, sends it to PostGrid for 
#                 processing.
#
#   PARAMETERS:   transfer_mode - test or production
#
#   USAGE:        From the command-line, type:
#                    ./pi_send_letters_to_postgrid.sh <transfer_mode>
#                 Or, to run as a daemon process:
#                    nohup ./pi_send_letters_to_postgrid.sh <transfer_mode> &
#
#   AUTHOR:       <PERSON>
#
#
# Copyright (c) 2025 Kevin <PERSON>
# All rights reserved.
#
# This software is proprietary and confidential. Unauthorized copying of this
# file, via any medium, is strictly prohibited without the express permission
# of the author or owning organization.
#
#-----------------------------------------------------------------------------


# Include the support functions
. $(dirname $0)/pi_common.sh
. $(dirname $0)/pi_postgrid.sh

# Local variables
typeset -u transfer_mode=${1}
typeset aop_directory=$(get_aop_directory)
typeset -i error_status=0
typeset timestamp="$(date +%Y%m%d%H%M%S)"
typeset log_file=/var/tmp/pi_send_letters_to_postgrid_${timestamp}.log
typeset letter_number
typeset api_key
typeset postgrid_url="https://api.postgrid.com/print-mail/v1/letters"
typeset to_contact
typeset from_contact
typeset letter_id
typeset transmit_file_name

if [ "${transfer_mode}" != "PRODUCTION" ]
then
   transfer_mode=TEST
fi

api_key=$(get_postgrid_api_key $transfer_mode)



# Add the standard header information to the log file
printf "%-10s %s\n" "Program:" "$(basename $0)" | tee $log_file
printf "%-10s %s\n" "Log File:" "$log_file" | tee -a $log_file
printf "%-10s %s\n" "Execution:" "$(date)" | tee -a $log_file

printf "\n%s\n" "Parameters:" | tee -a $log_file
printf "   %-18s %s\n\n\n" "Transfer Mode:" "${transfer_mode}" | \
   tee -a $log_file

# Process only PDF files
for file in ${aop_directory}/*.pdf; do

   if [ -f "$file" ] # Make sure the file still exists
   then
      echo "Processing ${file##*/}" | tee -a $log_file

      # Get the Letter Number from the file name
      letter_number=$(get_aop_letter_number ${file##*/})

      # Check that it's a Provider file (NULL if the letter was not found in the DB)
      if [ "$letter_number" = "" ]
      then
         echo "...Matching provider letter not found...skipped." | tee -a $log_file
      else
      
         echo "Letter Number: $letter_number" | tee -a $log_file

         # Build the PostGrid command
         to_contact=$(create_postgrid_contact $letter_number $transfer_mode)
         echo "To: $to_contact" | tee -a $log_file

         from_contact=$(create_postgrid_from_contact $letter_number $transfer_mode)
         echo "From: $from_contact" | tee -a $log_file

         # Send the letter to PostGrid
         letter_id=$(create_postgrid_letter "$to_contact" "$from_contact" "$file" "$transfer_mode")
         echo "Letter: $letter_id" | tee -a $log_file

         # Move the file to the temp directory
         mv $file /var/tmp/${file##*/}_$timestamp 2>&1 | tee -a $log_file

         # The transmitfilename does not include the ".pdf" file extension
         transmit_file_name=$(echo ${file##*/} | awk -F. '{print $1}')
         if [ "$transfer_mode" = "PRODUCTION" ] 
         then
            # Update the letter status to "Sent" and save the Letter ID
            echo "Marking letter as \"sent\"." | tee -a $log_file
            run_sql_proc "letters.mark_letter_sent('$letter_number', '$transmit_file_name', '$letter_id')" | \
               tee -a $log_file
         else
            echo "$transfer_mode mode; not marking letter file ${transmit_file_name} as \"sent\"." | tee -a $log_file
         fi

      fi

   fi
      
   echo | tee -a $log_file

done

echo "Check $log_file for results."

echo "Program complete." | tee -a $log_file

# Clean-up
unset transfer_mode
unset aop_directory
unset error_status0
unset timestamp
unset log_file
unset letter_number
unset to_contact
unset from_contact
unset letter_id
unset transmit_file_name

# Complete
