#!/bin/bash
#-----------------------------------------------------------------------------
#
#   PROCEDURE:    pi_add_db_connection.sh
#
#   PURPOSE:      This script adds the given database connection to the
#                 given configuration file. 
#
#   PARAMETERS:   db_user_id  - The database user/schema
#                 db_password - the database password (will be ecrypted)
#                 database    - The Oracle SID (Defaults to ORCL)
#                 config_file - Defaults to $SIMPLIPIED_HOME/.connect
#
#   USAGE:        From the command-line, type:
#                    pi_add_db_connection.sh <db_user_id> <db_password> \
#                       [database] [config_file]
#
#   AUTHOR:       <PERSON>
#
#   NOTES:        Because GitLab variables cannot contain "#", "~" was used.
#                 They will be replaced before encrypting the password.
#
#                 Parameter defaults (database and confile_file) are handled
#                 in the add_db_connection function (see pi_common.sh).
#
# Copyright (c) 2025 Kevin <PERSON>
# All rights reserved.
#
# This software is proprietary and confidential. Unauthorized copying of this
# file, via any medium, is strictly prohibited without the express permission
# of the author or owning organization.
#
#-----------------------------------------------------------------------------


# Include the "common" functions
unset PI_COMMON_FUNCTIONS
. $(dirname $0)/pi_common.sh

# Local variables
typeset db_user_id=${1}
typeset db_password=${2}
typeset database=${3:-ORCL}
typeset config_file=${4:-$SIMPLIPIED_HOME/.connect}
typeset translated_password="$(echo ${2} | tr \~ \#)"

if [ "$db_user_id" = "" ]
then
   echo "USAGE:"
   echo "   pi_add_db_connection.sh <db_user_id> <db_password> [database] [config_file]"
   echo
   echo "Where"
   echo "   db_user_id  - The database user/schema"
   echo "   db_password - the database password (will be ecrypted)"
   echo "   database    - The Oracle SID (Defaults to ORCL)"
   echo "   config_file - Defaults to $SIMPLIPIED_HOME/.connect"
   exit 1
fi


# Check if the key already exists
if [ "$(db_connection_exists ${db_user_id} ${database} ${config_file})" = "YES" ]
then
   echo "The ${db_user_id} connection for ${database} already exists in ${config_file}."
else
   add_db_connection ${db_user_id} ${translated_password} ${database} ${config_file}
fi

# Clean-up
unset db_user_id
unset db_password
unset database
unset config_file
unset translated_password

# Complete
