#!/usr/bin/env python3
"""
Enhanced Claims Pivot Table Generator for SimpliPIed.

This script creates Excel files with pivot tables using multiple approaches:
1. xlsxwriter for basic pivot tables
2. openpyxl for more advanced pivot table features
3. pandas pivot_table for data analysis
"""

import pandas as pd
import sys
import os
from pathlib import Path

# Add src to path for SimpliPIed package
sys.path.insert(0, str(Path(__file__).parent.parent / 'src'))

try:
    import xlsxwriter
    HAS_XLSXWRITER = True
except ImportError:
    HAS_XLSXWRITER = False

try:
    from openpyxl import Workbook
    from openpyxl.pivot.table import PivotTable, TableDefinition
    from openpyxl.pivot.cache import CacheDefinition
    from openpyxl.pivot.fields import RowField, ColField, DataField
    from openpyxl.utils.dataframe import dataframe_to_rows
    HAS_OPENPYXL = True
except ImportError:
    HAS_OPENPYXL = False


def write_claims_pivot_xlsxwriter(claims_rows, out_path="claims_pivot_xlsxwriter.xlsx"):
    """Create pivot table using xlsxwriter (simpler but limited)."""
    if not HAS_XLSXWRITER:
        raise ImportError("xlsxwriter is required for this function")
    
    df = pd.DataFrame(claims_rows)
    df["OpenPayableAmount"] = pd.to_numeric(df["OpenPayableAmount"], errors="coerce").fillna(0.0)

    with pd.ExcelWriter(out_path, engine="xlsxwriter") as writer:
        df.to_excel(writer, sheet_name="Summary Claims", index=False)
        wb = writer.book
        ws_pivot = wb.add_worksheet("Pivot")

        # Define table from the data
        nrows, ncols = df.shape
        table_cols = [{"header": col} for col in df.columns]
        ws_claims = writer.sheets["Summary Claims"]
        ws_claims.add_table(0, 0, nrows, ncols - 1, {"name": "ClaimsTable", "columns": table_cols})

        # Add formatting
        money_format = wb.add_format({'num_format': '$#,##0.00'})
        header_format = wb.add_format({'bold': True, 'bg_color': '#D7E4BC'})
        
        # Add PivotTable
        ws_pivot.add_pivot_table({
            "source": f"'Summary Claims'!$A$1:${chr(65 + ncols - 1)}${nrows + 1}",
            "name": "PivotTable1",
            "location": "B3",
            "rows": [{"data": "ProviderName"}],
            "columns": [{"data": "LOB"}],
            "values": [{"data": "OpenPayableAmount", "function": "sum"}],
        })
        
        # Add title and formatting to pivot sheet
        ws_pivot.write('B1', 'Claims Pivot Analysis', header_format)
        ws_pivot.set_column('B:Z', 15)  # Set column width

    print(f"✅ Created {out_path} with xlsxwriter PivotTable.")


def write_claims_pivot_openpyxl(claims_rows, out_path="claims_pivot_openpyxl.xlsx"):
    """Create pivot table using openpyxl (more advanced features)."""
    if not HAS_OPENPYXL:
        raise ImportError("openpyxl is required for this function")
    
    df = pd.DataFrame(claims_rows)
    df["OpenPayableAmount"] = pd.to_numeric(df["OpenPayableAmount"], errors="coerce").fillna(0.0)
    
    wb = Workbook()
    
    # Create data sheet
    ws_data = wb.active
    ws_data.title = "Claims Data"
    
    # Write data to worksheet
    for r in dataframe_to_rows(df, index=False, header=True):
        ws_data.append(r)
    
    # Create pivot table sheet
    ws_pivot = wb.create_sheet("Pivot Table")
    
    # Define the data range
    data_range = f"A1:{chr(65 + len(df.columns) - 1)}{len(df) + 1}"
    
    # Create pivot table
    pivot_table = PivotTable()
    pivot_table.cache = CacheDefinition(
        cacheSource=TableDefinition(ref=f"'Claims Data'!{data_range}")
    )
    
    # Add fields
    pivot_table.addRowField(RowField(x=0))  # ProviderName (first column)
    pivot_table.addColField(ColField(x=1))  # LOB (second column)
    pivot_table.addDataField(DataField(fld=2, subtotal="sum"))  # OpenPayableAmount (third column)
    
    # Add pivot table to worksheet
    ws_pivot.add_pivot(pivot_table, "B3")
    
    wb.save(out_path)
    print(f"✅ Created {out_path} with openpyxl PivotTable.")


def write_claims_pivot_pandas(claims_rows, out_path="claims_pivot_pandas.xlsx"):
    """Create pivot table using pandas (most flexible for analysis)."""
    df = pd.DataFrame(claims_rows)
    df["OpenPayableAmount"] = pd.to_numeric(df["OpenPayableAmount"], errors="coerce").fillna(0.0)
    
    # Create pivot table with pandas
    pivot_df = df.pivot_table(
        values='OpenPayableAmount',
        index='ProviderName',
        columns='LOB',
        aggfunc='sum',
        fill_value=0,
        margins=True,
        margins_name='Total'
    )
    
    # Create summary statistics
    summary_stats = df.groupby(['ProviderName', 'LOB'])['OpenPayableAmount'].agg([
        'sum', 'count', 'mean', 'min', 'max'
    ]).round(2)
    
    with pd.ExcelWriter(out_path, engine='openpyxl') as writer:
        # Write original data
        df.to_excel(writer, sheet_name='Raw Data', index=False)
        
        # Write pivot table
        pivot_df.to_excel(writer, sheet_name='Pivot Summary')
        
        # Write detailed statistics
        summary_stats.to_excel(writer, sheet_name='Detailed Stats')
        
        # Format the sheets
        workbook = writer.book
        
        # Format pivot sheet
        pivot_sheet = writer.sheets['Pivot Summary']
        for cell in pivot_sheet[1]:  # Header row
            cell.font = cell.font.copy(bold=True)
        
        # Add a chart sheet
        chart_sheet = workbook.create_sheet('Charts')
        chart_sheet['A1'] = 'Pivot table created with pandas'
    
    print(f"✅ Created {out_path} with pandas pivot analysis.")


def write_claims_pivot_comprehensive(claims_rows, out_path="claims_comprehensive_pivot.xlsx"):
    """Create comprehensive Excel file with multiple pivot approaches."""
    df = pd.DataFrame(claims_rows)
    df["OpenPayableAmount"] = pd.to_numeric(df["OpenPayableAmount"], errors="coerce").fillna(0.0)
    
    # Create multiple pivot views
    pivot_by_provider = df.pivot_table(
        values='OpenPayableAmount', index='ProviderName', aggfunc=['sum', 'count', 'mean']
    ).round(2)
    
    pivot_by_lob = df.pivot_table(
        values='OpenPayableAmount', index='LOB', aggfunc=['sum', 'count', 'mean']
    ).round(2)
    
    pivot_cross = df.pivot_table(
        values='OpenPayableAmount', index='ProviderName', columns='LOB', 
        aggfunc='sum', fill_value=0, margins=True
    ).round(2)
    
    with pd.ExcelWriter(out_path, engine='openpyxl') as writer:
        # Raw data
        df.to_excel(writer, sheet_name='Raw Data', index=False)
        
        # Different pivot views
        pivot_by_provider.to_excel(writer, sheet_name='By Provider')
        pivot_by_lob.to_excel(writer, sheet_name='By LOB')
        pivot_cross.to_excel(writer, sheet_name='Cross Analysis')
        
        # Summary dashboard
        summary_sheet = writer.book.create_sheet('Dashboard')
        summary_sheet['A1'] = 'Claims Analysis Dashboard'
        summary_sheet['A3'] = f'Total Claims: {len(df)}'
        summary_sheet['A4'] = f'Total Amount: ${df["OpenPayableAmount"].sum():,.2f}'
        summary_sheet['A5'] = f'Average Amount: ${df["OpenPayableAmount"].mean():,.2f}'
        summary_sheet['A6'] = f'Providers: {df["ProviderName"].nunique()}'
        summary_sheet['A7'] = f'LOBs: {df["LOB"].nunique()}'
    
    print(f"✅ Created {out_path} with comprehensive pivot analysis.")


# Update the original function to use the enhanced version
def write_claims_pivot_xlsx(claims_rows, out_path="claims_with_pivot.xlsx"):
    """Enhanced version of the original function."""
    return write_claims_pivot_comprehensive(claims_rows, out_path)


if __name__ == "__main__":
    # Sample data
    claims = [
        {"ProviderName": "ABC Clinic", "LOB": "Medicare", "OpenPayableAmount": 1200.50},
        {"ProviderName": "ABC Clinic", "LOB": "Commercial", "OpenPayableAmount": 300.00},
        {"ProviderName": "Beta Medical", "LOB": "Medicare", "OpenPayableAmount": 950.00},
        {"ProviderName": "Beta Medical", "LOB": "Commercial", "OpenPayableAmount": 110.00},
        {"ProviderName": "Gamma Health", "LOB": "Medicare", "OpenPayableAmount": 2100.75},
        {"ProviderName": "Gamma Health", "LOB": "Medicaid", "OpenPayableAmount": 450.25},
        {"ProviderName": "ABC Clinic", "LOB": "Medicaid", "OpenPayableAmount": 675.00},
    ]
    
    print("Creating pivot tables with different approaches...")
    
    # Try different approaches
    try:
        write_claims_pivot_pandas(claims, "claims_pandas_pivot.xlsx")
    except Exception as e:
        print(f"❌ Pandas approach failed: {e}")
    
    try:
        if HAS_XLSXWRITER:
            write_claims_pivot_xlsxwriter(claims, "claims_xlsxwriter_pivot.xlsx")
        else:
            print("⚠️ xlsxwriter not available")
    except Exception as e:
        print(f"❌ xlsxwriter approach failed: {e}")
    
    try:
        write_claims_pivot_comprehensive(claims, "claims_comprehensive_pivot.xlsx")
    except Exception as e:
        print(f"❌ Comprehensive approach failed: {e}")
    
    print("\n🎉 Pivot table generation complete!")
