#!/bin/python3
#-----------------------------------------------------------------------------
#
#   Script:      pi_export_excel.py
#
#   Description: This script is used to generate the an Excel file from a 
#                template.
#
#   Parameters:  None.
#
#   Usage:       python3 pi_export_excel.py --spreadsheet "<spreadsheet_name>" \
#                   --worksheet "<worksheet_name>"
#                or
#                ./pi_generapi_export_excel.py --spreadsheet "<spreadsheet_name>" \
#                   --worksheet "<worksheet_name>"
#
#   Author:      <PERSON>
#  
#   Test:        python3 -m unittest test_pi_export_excel.py
#
# Copyright (c) 2025 Kevin <PERSON>re
# All rights reserved.
#
# This software is proprietary and confidential. Unauthorized copying of this
# file, via any medium, is strictly prohibited without the express permission
# of the author or owning organization.
#
#-----------------------------------------------------------------------------
from jinja2 import Template
from openpyxl import Workbook, load_workbook
from openpyxl.styles import Font, Alignment, numbers, PatternFill, Protection
from openpyxl.utils import get_column_letter
from openpyxl.formatting.rule import CellIsRule
from openpyxl.worksheet.datavalidation import DataValidation
from openpyxl.workbook.defined_name import DefinedName

import cx_Oracle
import argparse
import os
import csv
import textwrap
import subprocess
import json
from json import JSONDecodeError



def parse_args():
    parser = argparse.ArgumentParser(description="Export Excel spreadsheet.")
    parser.add_argument(
        "--spreadsheet",
        required=True,
        help="The spreadsheet name format to generate (e.g., \"open payables\")"
    )
    return parser.parse_args()


def validate_json_string(json_string):
    try:
        parsed = json.loads(json_string)
        # print("✅ JSON is syntactically valid.")
        return parsed
    except JSONDecodeError as e:
        print("❌ JSON syntax error:")
        print(f"  {e.msg} at line {e.lineno} column {e.colno}")
        print(f"{json_string}")
        raise


def validate_cell_styles(styles):
    for col, style in styles.items():
        if "font_size" in style and not isinstance(style["font_size"], (int, float)):
            print(f"⚠️ Warning: 'font_size' for column '{col}' should be a number.")
        if "bold" in style and not isinstance(style["bold"], bool):
            print(f"⚠️ Warning: 'bold' for column '{col}' should be a boolean.")
        if "italic" in style and not isinstance(style["italic"], bool):
            print(f"⚠️ Warning: 'italic' for column '{col}' should be a boolean.")
        if "font_color" in style and not isinstance(style["font_color"], str):
            print(f"⚠️ Warning: 'font_color' for column '{col}' should be a hex color string.")
        if "font_name" in style and not isinstance(style["font_name"], str):
            print(f"⚠️ Warning: 'font_name' for column '{col}' should be a string.")


def call_bash_function(function_name, bash_script_path="./pi_common.sh"):
    """
    Calls a bash function defined in a given bash script.

    Args:
        function_name (str): Name of the Bash function to call.
        bash_script_path (str): Path to the Bash script that defines the function.
    """
    bash_command = f"source {bash_script_path} && {function_name}"
    result = subprocess.run(
        ['bash', '-c', bash_command],
        capture_output=True,
        text=True
    )

    # Remove any newlines
    return result.stdout.strip()




# Call the Bash function to get and decrypt the password
def get_db_password(user_id):
    db_password = None

    return call_bash_function(f"get_db_password {user_id}")




def fetch_claims_from_db():
    dsn = cx_Oracle.makedsn("apex-app.rapidpie.com", 1521, service_name="orcl")
    username = "apps"
    password = get_db_password("apps")

    connection = cx_Oracle.connect(user=username, password=password, dsn=dsn)
    cursor = connection.cursor()

    # Call the PL/SQL function to get the returned sys_refcursor
    claims_cursor = cursor.callfunc("spreadsheet.get_open_payables_claims_cursor", cx_Oracle.CURSOR)

    claims_data = []

    for row in claims_cursor:
        claims_data.append({
            "SourceRecordID": row[0],
            "SeqNo": row[1],
            "StateID": row[2],
            "ProviderName": row[3],
            "ProviderID": row[4],
            "ProviderTIN": row[5],
            "LOB": row[6],
            "ProviderAccountID": row[7],
            "ClaimNumber": row[8],
            "ClaimPaidDate": row[9],
            "ClaimPaidAmount": row[10],
            "OpenPayableAmount": row[11],
            "OpenPayableDate": row[12],
            "OpenPayableAge": row[13],
            "ShiftToAccount": row[14]
        })

    claims_cursor.close()
    connection.close()
    return claims_data


def fetch_instructions_from_db():
    dsn = cx_Oracle.makedsn("apex-app.rapidpie.com", 1521, service_name="orcl")
    username = "apps"
    password = get_db_password("apps")

    connection = cx_Oracle.connect(user=username, password=password, dsn=dsn)
    cursor = connection.cursor()

    # Call the PL/SQL function to get the returned sys_refcursor
    db_cursor = cursor.callfunc("spreadsheet.get_open_payables_instructions_cursor", cx_Oracle.CURSOR)

    db_data = []

    for row in db_cursor:
        db_data.append({
            "record_no": row[0],
            "Instruction": row[1]
        })

    db_cursor.close()
    connection.close()
    return db_data



def get_worksheet_template(spreadsheet_name, worksheet_name):
    dsn = cx_Oracle.makedsn("apex-app.rapidpie.com", 1521, service_name="orcl")
    username = "apps"
    password = get_db_password(username)

    connection = cx_Oracle.connect(user=username, password=password, dsn=dsn)
    cursor = connection.cursor()

    # Call the PL/SQL function to get the returned sys_refcursor
    worksheet_template = cursor.callfunc("spreadsheet.get_worksheet_template", cx_Oracle.STRING, [spreadsheet_name, worksheet_name])

    cursor.close()
    connection.close()

    return worksheet_template


def get_worksheets(spreadsheet_name):
    dsn = cx_Oracle.makedsn("apex-app.rapidpie.com", 1521, service_name="orcl")
    username = "apps"
    password = get_db_password(username)

    connection = cx_Oracle.connect(user=username, password=password, dsn=dsn)
    cursor = connection.cursor()

    # Call the PL/SQL function to get the returned sys_refcursor
    worksheets_cursor = cursor.callfunc("spreadsheet.get_worksheets_cursor", cx_Oracle.CURSOR, [spreadsheet_name])

    worksheets = []

    for row in worksheets_cursor:
        worksheets.append({ 
            "worksheet_id": row[0],
            "spreadsheet_id": row[1],
            "worksheet_order": row[2],
            "worksheet_name": row[3]
        })

    cursor.close()
    connection.close()

    return worksheets


def get_spreadsheet_filename(spreadsheet_name):
    dsn = cx_Oracle.makedsn("apex-app.rapidpie.com", 1521, service_name="orcl")
    username = "apps"
    password = get_db_password(username)

    connection = cx_Oracle.connect(user=username, password=password, dsn=dsn)
    cursor = connection.cursor()

    # Call the PL/SQL function to get the file name
    spreadsheet_filename = cursor.callfunc("spreadsheet.get_spreadsheet_filename", cx_Oracle.STRING, [spreadsheet_name])

    cursor.close()
    connection.close()

    return spreadsheet_filename



def generate_excel_worksheet(context, template_str, output_file, worksheet_name="Sheet1"):
    template_str = textwrap.dedent(template_str)
    template = Template(template_str)
    rendered_string = template.render(context)

    # Validate JSON syntax
    rendered_obj = validate_json_string(rendered_string)

    worksheet_name = rendered_obj.get("worksheet_name", "Sheet1")
    rows = rendered_obj.get("rows", [])
    freeze_top_row = rendered_obj.get("freeze_top_row", False)
    bold_headers = rendered_obj.get("bold_headers", False)
    auto_fit_columns = rendered_obj.get("auto_fit_columns", False)
    column_format = rendered_obj.get("column_format", {})
    hidden_columns = rendered_obj.get("hidden_columns", [])
    cell_styles = rendered_obj.get("cell_styles", {})
    conditional_rules = rendered_obj.get("conditional_formatting", [])
    lock_sheet = rendered_obj.get("lock_sheet", False)
    locked_columns = rendered_obj.get("locked_columns", [])
    unlocked_columns = rendered_obj.get("unlocked_columns", [])


    if cell_styles:
        validate_cell_styles(cell_styles)

    # Open existing workbook or create new one
    if os.path.exists(output_file):
        wb = load_workbook(output_file)
    else:
        wb = Workbook()
        # Remove default sheet only if we’ll create a new one
        default_sheet = wb.active
        if default_sheet.title == "Sheet":
            wb.remove(default_sheet)

    # Avoid overwriting a sheet
    if worksheet_name in wb.sheetnames:
        raise ValueError(f"Worksheet '{worksheet_name}' already exists in '{output_file}'.")

    ws = wb.create_sheet(title=worksheet_name)

    if not rows:
        ws.append(["No data"])
        wb.save(output_file)
        return

    headers = list(rows[0].keys())
    ws.append(headers)

    if bold_headers:
        from openpyxl.styles import Font
        bold_font = Font(bold=True)
        for cell in ws[1]:
            cell.font = bold_font

    if freeze_top_row:
        ws.freeze_panes = "A2"

    for row in rows:
        ws.append(list(row.values()))

    if auto_fit_columns:
        for col in ws.columns:
            max_length = 0
            col_letter = col[0].column_letter
            for cell in col:
                if cell.value:
                    max_length = max(max_length, len(str(cell.value)))
            ws.column_dimensions[col_letter].width = max_length + 2

    if column_format:
        apply_column_format(ws, column_format, headers)

    if cell_styles:
        apply_cell_styles(ws, cell_styles, headers)

    if conditional_rules:
        apply_conditional_formatting(ws, conditional_rules, headers)


    if lock_sheet:
        ws.protection.sheet = True
        ws.protection.enable()
        # Optional: password
        # ws.protection.set_password("mypassword")

        col_map = {name: idx + 1 for idx, name in enumerate(headers)}

        # Lock specified columns
        for col_name in locked_columns:
            col_idx = col_map.get(col_name)
            if col_idx:
                for row in ws.iter_rows(min_row=2, min_col=col_idx, max_col=col_idx):
                    for cell in row:
                        cell.protection = Protection(locked=True)

        # Unlock specified columns
        for col_name in unlocked_columns:
            col_idx = col_map.get(col_name)
            if col_idx:
                for row in ws.iter_rows(min_row=2, min_col=col_idx, max_col=col_idx):
                    for cell in row:
                        cell.protection = Protection(locked=False)

    if hidden_columns:
        col_map = {name: idx + 1 for idx, name in enumerate(headers)}
        for col_name in hidden_columns:
            col_idx = col_map.get(col_name)
            if col_idx:
                col_letter = get_column_letter(col_idx)
                ws.column_dimensions[col_letter].hidden = True


    lookup_sources = rendered_obj.get("lookup_sources", {})
    data_validations = rendered_obj.get("data_validations", [])
    xlookups = rendered_obj.get("xlookups", [])

    # 1) Write lookup sources to hidden sheet and create named ranges
    name_to_range = write_lookup_sources(wb, lookup_sources) if lookup_sources else {}

    # 2) Apply dropdowns (data validation)
    if data_validations:
        apply_data_validations(ws, headers, data_validations, wb, name_to_range)

    # 3) Apply XLOOKUP formulas
    if xlookups:
        apply_xlookups(ws, headers, xlookups)



    wb.save(output_file)



def apply_column_format(ws, column_format, header_row):
    format_map = {
        "date": numbers.FORMAT_DATE_YYYYMMDD2,
        "currency": numbers.FORMAT_CURRENCY_USD_SIMPLE,
        "number": numbers.FORMAT_NUMBER_COMMA_SEPARATED1,
        "percent": numbers.FORMAT_PERCENTAGE_00
    }

    col_indices = {name: i + 1 for i, name in enumerate(header_row)}

    for col_name, fmt_key in column_format.items():
        if col_name not in col_indices:
            continue
        col_idx = col_indices[col_name]
        col_letter = get_column_letter(col_idx)
        number_format = format_map.get(fmt_key)
        if number_format:
            for cell in ws[col_letter][1:]:  # skip header
                cell.number_format = number_format
                if fmt_key == "currency" or fmt_key == "number":
                    cell.alignment = Alignment(horizontal="right")



def apply_cell_styles(ws, cell_styles, headers):
    col_map = {name: idx + 1 for idx, name in enumerate(headers)}  # 1-based

    for col_name, style in cell_styles.items():
        col_idx = col_map.get(col_name)
        if not col_idx:
            continue
        for row in ws.iter_rows(min_row=2, min_col=col_idx, max_col=col_idx):
            for cell in row:
                # Font configuration
                font_args = {}
                if "font_color" in style:
                    font_args["color"] = style["font_color"]
                if "font_size" in style:
                    font_args["size"] = style["font_size"]
                if "font_name" in style:
                    font_args["name"] = style["font_name"]
                if "bold" in style:
                    font_args["bold"] = style["bold"]
                if "italic" in style:
                    font_args["italic"] = style["italic"]

                if font_args:
                    cell.font = Font(**font_args)

                # Background fill
                if "bg_color" in style:
                    cell.fill = PatternFill(
                        start_color=style["bg_color"],
                        end_color=style["bg_color"],
                        fill_type="solid"
                    )



def apply_conditional_formatting(ws, rules, headers):
    col_map = {name: idx + 1 for idx, name in enumerate(headers)}

    for rule in rules:
        col_name = rule["column"]
        col_idx = col_map.get(col_name)
        if not col_idx:
            continue
        col_letter = ws.cell(row=1, column=col_idx).column_letter
        cell_range = f"{col_letter}2:{col_letter}{ws.max_row}"

        fill = PatternFill(start_color=rule["bg_color"], end_color=rule["bg_color"], fill_type="solid")
        font = Font(color=rule["font_color"])

        ws.conditional_formatting.add(
            cell_range,
            CellIsRule(
                operator=rule["type"],
                formula=[str(rule["value"])],
                fill=fill,
                font=font
            )
        )





def ensure_lookup_sheet(wb):
    """Create or fetch a hidden LOOKUPS sheet."""
    sheet_name = "LOOKUPS"
    if sheet_name in wb.sheetnames:
        ws_lu = wb[sheet_name]
    else:
        ws_lu = wb.create_sheet(title=sheet_name)
        ws_lu.sheet_state = "hidden"
    return ws_lu





def _quote_sheet(sheet_name: str) -> str:
    """Quote the sheet name if it contains spaces or special characters."""
    if sheet_name.startswith("'") and sheet_name.endswith("'"):
        return sheet_name
    return f"'{sheet_name}'" if any(c in sheet_name for c in " []()*?{}+-/\\.!,'\"") else sheet_name


from openpyxl.workbook.defined_name import DefinedName
from openpyxl.utils import get_column_letter

def _quote_sheet(sheet_name: str) -> str:
    """Quote a sheet name if it contains spaces/special chars."""
    if sheet_name.startswith("'") and sheet_name.endswith("'"):
        return sheet_name
    special = " []()*?{}+-/\\.!,'\""
    return f"'{sheet_name}'" if any(c in sheet_name for c in special) else sheet_name



def _named_range_exists(wb, name: str) -> bool:
    """Version-safe check for an existing defined name."""
    try:
        return name in wb.defined_names  # DefinedNameDict supports membership test
    except Exception:
        # Fallback for very old openpyxl versions
        try:
            return any(dn.name == name for dn in wb.defined_names)
        except Exception:
            return False



def _add_defined_name_if_missing(wb, name: str, ref: str) -> None:
    """Add a defined name only if it doesn't already exist."""
    if _named_range_exists(wb, name):
        return
    dn = DefinedName(name=name, attr_text=ref)
    # Prefer .add(), fallback to .append(), last-resort mapping assignment
    if hasattr(wb.defined_names, "add"):
        wb.defined_names.add(dn)
    elif hasattr(wb.defined_names, "append"):
        wb.defined_names.append(dn)
    else:
        wb.defined_names[name] = dn  # rare fallback



def write_lookup_sources(wb, lookup_sources):
    """
    Write lookup sources to a hidden LOOKUPS sheet and create named ranges
    using wb.defined_names. Skips creating duplicates. Returns a map of
    source name -> range string(s) for convenience.
    - If the source is a list: defines a single named range <name>
    - If the source is a {headers, rows} dict (2 columns): defines <name>_keys, <name>_vals
    """
    if not lookup_sources:
        return {}

    # Ensure hidden LOOKUPS sheet
    sheet_name = "LOOKUPS"
    if sheet_name in wb.sheetnames:
        ws_lu = wb[sheet_name]
    else:
        ws_lu = wb.create_sheet(title=sheet_name)
        ws_lu.sheet_state = "hidden"

    qsheet = _quote_sheet(ws_lu.title)
    start_row = ws_lu.max_row + 1 if ws_lu.max_row else 1
    name_to_range = {}

    for name, src in lookup_sources.items():
        if isinstance(src, list):
            # Write a single-column list
            col = 1
            r = start_row
            for item in src:
                ws_lu.cell(row=r, column=col, value=item)
                r += 1
            end_row = r - 1
            col_letter = get_column_letter(col)
            rng = f"{qsheet}!${col_letter}${start_row}:${col_letter}${end_row}"
            _add_defined_name_if_missing(wb, name, rng)
            name_to_range[name] = rng
            start_row = end_row + 2  # leave a blank row between blocks

        elif isinstance(src, dict) and "headers" in src and "rows" in src:
            # Two-column key/value table
            headers = src["headers"]
            rows = src["rows"]
            col_start = 1

            # headers
            for i, h in enumerate(headers, start=col_start):
                ws_lu.cell(row=start_row, column=i, value=h)

            # data
            r = start_row + 1
            for rec in rows:
                for i, h in enumerate(headers, start=col_start):
                    ws_lu.cell(row=r, column=i, value=rec.get(h))
                r += 1
            end_row = r - 1

            key_col_letter = get_column_letter(col_start)
            val_col_letter = get_column_letter(col_start + 1)
            keys_rng = f"{qsheet}!${key_col_letter}${start_row+1}:${key_col_letter}${end_row}"
            vals_rng = f"{qsheet}!${val_col_letter}${start_row+1}:${val_col_letter}${end_row}"

            _add_defined_name_if_missing(wb, f"{name}_keys", keys_rng)
            _add_defined_name_if_missing(wb, f"{name}_vals",  vals_rng)
            name_to_range[name] = {"keys": keys_rng, "vals": vals_rng}

            start_row = end_row + 2

        else:
            # Unsupported source shape: skip
            continue

    return name_to_range






def apply_data_validations(ws, headers, validations, wb, name_to_range):
    """
    Apply dropdowns based on:
    - named lists via lookup_sources (e.g., "States")
    - direct range references (e.g., "=LOOKUPS!$A$2:$A$50")
    """
    if not validations:
        return

    col_map = {name: idx + 1 for idx, name in enumerate(headers)}

    for v in validations:
        col_name = v.get("column")
        src = v.get("source")  # either a lookup source name or an '=' range
        allow_blank = v.get("allow_blank", True)

        cidx = col_map.get(col_name)
        if not cidx or not src:
            continue

        if src.startswith("="):
            formula = src  # direct range
        else:
            # assume named range from lookup_sources
            if src in name_to_range:
                # For simple list, the named range is exactly the source name
                # We reference it as =<Name>
                # (openpyxl supports named ranges in data validation formulae)
                formula = f"={src}"
            else:
                # Fallback: treat as a named range even if not found
                formula = f"={src}"

        dv = DataValidation(type="list", formula1=formula, allow_blank=allow_blank)
        col_letter = get_column_letter(cidx)
        dv.ranges.add(f"{col_letter}2:{col_letter}{ws.max_row}")
        ws.add_data_validation(dv)


def apply_xlookups(ws, headers, xlookups):
    """
    Applies XLOOKUP formulas. Assumes a hidden LOOKUPS sheet contains named ranges
    <source>_keys and <source>_vals, as created by write_lookup_sources.
    """
    if not xlookups:
        return

    col_map = {name: idx + 1 for idx, name in enumerate(headers)}

    for xl in xlookups:
        result_col_name = xl.get("result_column")
        key_col_name = xl.get("key_column")
        source = xl.get("source")  # lookup source name
        if_not_found = xl.get("if_not_found", "")

        rc = col_map.get(result_col_name)
        kc = col_map.get(key_col_name)
        if not rc or not kc or not source:
            continue

        rcol = get_column_letter(rc)
        kcol = get_column_letter(kc)

        # Use named ranges <source>_keys and <source>_vals
        keys_name = f"{source}_keys"
        vals_name = f"{source}_vals"

        # Fill from row 2 down to last row
        for r in range(2, ws.max_row + 1):
            key_cell = f"{kcol}{r}"
            # XLOOKUP(key, keys, vals, if_not_found)
            #formula = f'=IFERROR(XLOOKUP({key_cell},{keys_name},{vals_name},"{if_not_found}"),"{if_not_found}")'
            formula = f'=IFERROR(INDEX({vals_name}, MATCH({key_cell}, {keys_name}, 0)), "{if_not_found}")'
            
            ws[f"{rcol}{r}"].value = formula



def main():
    """Main entry point"""

    """
    To Do:
    Add "generic" context load
    Pivot Tables
    """
    args = parse_args()
    spreadsheet_name = args.spreadsheet
    print(f"Spreadsheet Name: {spreadsheet_name}")

    output_file = get_spreadsheet_filename(spreadsheet_name)
    print(f"File Name: {output_file}")

    # Fetch all worksheet names for the given spreadsheet
    worksheets = get_worksheets(spreadsheet_name)

    for worksheet_dict in worksheets:
        worksheet_name = worksheet_dict['worksheet_name']

        print(f"Generating worksheet: {worksheet_name}")

        # Get the Excel template using Jinja2 syntax
        data_template = get_worksheet_template(spreadsheet_name, worksheet_name)

        # Get the context data to render the template from the database
        if (worksheet_name == "Claims"):
            print("Getting Claims data...")
            context = {
                "records": fetch_claims_from_db()
            }
        elif (worksheet_name == "Instructions"):
            print("Getting Instructions data...")
            context = {
                "records": fetch_instructions_from_db()
            }

        # Generate the Excel file
        generate_excel_worksheet(context, data_template, output_file, worksheet_name)
 
    print(f"Excel file {output_file} generated successfully.")


if __name__ == "__main__":
    main()
