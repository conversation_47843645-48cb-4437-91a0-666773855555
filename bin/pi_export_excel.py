#!/bin/python3
#-----------------------------------------------------------------------------
#
#   Script:      pi_export_excel.py
#
#   Description: This script is used to generate the an Excel file from a 
#                template.
#
#   Parameters:  None.
#
#   Usage:       python3 pi_export_excel.py 
#                or
#                ./pi_generapi_export_excel.py 
#
#   Author:      <PERSON>
#  
#   Test:        python3 -m unittest test_pi_export_excel.py
#
# Copyright (c) 2025 Kevin <PERSON>re
# All rights reserved.
#
# This software is proprietary and confidential. Unauthorized copying of this
# file, via any medium, is strictly prohibited without the express permission
# of the author or owning organization.
#
#-----------------------------------------------------------------------------
from jinja2 import Template
from openpyxl import Workbook

# Sample data template using Jinja2 syntax
data_template = """
[
    {% for user in users %}
    {
        "Name": "{{ user.name }}",
        "Age": {{ user.age }},
        "Department": "{{ user.department }}"
    }{% if not loop.last %},{% endif %}
    {% endfor %}
]
"""

# Context data to render the template
context = {
    "users": [
        {"name": "Alice", "age": 30, "department": "HR"},
        {"name": "Bob", "age": 25, "department": "Engineering"},
        {"name": "Charlie", "age": 28, "department": "Marketing"},
    ]
}

# Render the template
template = Template(data_template)
rendered_data = eval(template.render(context))  # This results in a list of dicts

# Create Excel file using openpyxl
wb = Workbook()
ws = wb.active
ws.title = "Employees"

# Write headers
headers = list(rendered_data[0].keys())
ws.append(headers)

# Write rows
for row in rendered_data:
    ws.append(list(row.values()))

# Save the Excel file
wb.save("employees.xlsx")

print("Excel file 'employees.xlsx' generated successfully.")
