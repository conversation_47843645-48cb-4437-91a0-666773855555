#!/bin/bash
#-----------------------------------------------------------------------------
#
#   PROCEDURE:    pi_public_key_daemon.sh
#
#   PURPOSE:      This script continuously checks the PI_PUBLIC_KEYS database 
#                 table for "NEW" records. If found, it generated a new public 
#                 key file for each and updates the table with the results.
#
#   PARAMETERS:   None
#
#   USAGE:        From the command-line, type:
#                    pi_public_key_daemon.sh 
#                 Or, to run as a daemon process:
#                    nohup pi_public_key_daemon.sh &
#
#   AUTHOR:       <PERSON>
#
#-----------------------------------------------------------------------------


# Include the "common" functions
unset PI_COMMON_FUNCTIONS
. $(dirname $0)/pi_common.sh

# Local variables
typeset -i error_status=0
typeset timestamp="$(date +%Y%m%d%H%M%S)"
typeset log_file=/var/tmp/pi_public_key_daemon_${timestamp}.log
typeset shutdown_semaphore=/var/tmp/pi_stop_public_key_daemon
typeset requests

typeset key_id
typeset key_name
typeset key_email
typeset key_comment
typeset key_expire
typeset key_status
typeset key_fingerprint
typeset key_file
typeset key_expire_date

# Add the standard header information to the log file
printf "%-10s %s\n" "Program:" "$(basename $0)" | tee $log_file
printf "%-10s %s\n" "Log File:" "$log_file" | tee -a $log_file
printf "%-10s %s\n\n\n" "Execution:" "$(date)" | tee -a $log_file



until [ -r ${shutdown_semaphore} ]
do
   # Get the new public keys to generate
   echo "Getting new public key requests..."
   #run_sql_proc common.get_new_public_key_requests | tee /var/tmp/temp_public_key_requests.dat

   # Loop through the list
   run_sql_proc common.get_new_public_key_requests | while read key_record
   do 
      key_id=$(echo $key_record | awk -F, '{print $1}')
      key_name="$(echo $key_record | awk -F, '{print $2}')"
      key_email="$(echo $key_record | awk -F, '{print $3}')"
      key_comment="$(echo $key_record | awk -F, '{print $4}')"
      key_expire="$(echo $key_record | awk -F, '{print $5}')"
      key_status="$(echo $key_record | awk -F, '{print $6}')"

      if [ "$key_status" = "NEW" ]
      then
         # Call generate_gpg_key to create the new key
         generate_gpg_key "$key_name" $key_email "$key_comment" $key_expire

         key_fingerprint=$(get_gpg_fingerprint "${key_name} (${key_comment})")

         if [ "${key_fingerprint}" != "" ]
         then
            echo "Adding new $key_name public key ..."

            key_file=/var/tmp/public_key_${key_fingerprint}

            export_public_key "${key_fingerprint}" $key_file

            key_expire_date=$(get_gpg_key_expiration ${key_fingerprint})

            run_sql_proc -sy "common.update_public_key($key_id, 'PROCESSED', '$key_fingerprint', '$key_expire_date', '$key_file')"

            echo "... New $key_name public key added."

         else # No fingerprint found
            echo "ERROR: A problem occurred when trying to generate the new $key_name public key."
            run_sql_proc -sy "common.update_public_key($key_id, 'ERROR')"
         fi
      fi
   done

   sleep 1m

done


# Clean-up
rm $shutdown_semaphore 2> /dev/null
unset shutdown_semaphore
unset error_status
unset timestamp
unset log_file
unset requests
unset key_id
unset key_name
unset key_email
unset key_comment
unset key_expire
unset key_status
unset key_fingerprint
unset key_file
unset key_expire_date

# Complete
