#!/bin/bash
#-----------------------------------------------------------------------------
#
#   PROCEDURE:    test_pi_host_request.sh
#
#   PURPOSE:      This script contains the unit tests for the PostGrid 
#                 functions.
#
#   PARAMETERS:   None.
#
#   USAGE:        From the command-line, type:
#                    ./test_pi_host_request.sh
#
#   AUTHOR:       <PERSON>
#
#
# Copyright (c) 2025 <PERSON>re
# All rights reserved.
#
# This software is proprietary and confidential. Unauthorized copying of this
# file, via any medium, is strictly prohibited without the express permission
# of the author or owning organization.
#
#-----------------------------------------------------------------------------

#suite()
#{
#   suite_addTest test_create_
#}

# Before all
oneTimeSetUp()
{

   # Include the functions
   unset PI_COMMON_FUNCTIONS
   . $(dirname $0)/pi_common.sh

   unset PI_HOST_REQUEST_FUNCTIONS
   . $(dirname $0)/pi_host_request.sh

   typeset sql_results

   sql_results=$(run_sql_proc "host_request.hold_new_requests")
}

# After all
oneTimeTearDown()
{
   sql_results=$(run_sql_proc "host_request.release_held_requests")

   #rm /var/tmp/temp_new_requests.dat 2> /dev/null
   :

   unset sql_results
}





# get_new_host_requests tests ------------------------------------------------
test_get_new_host_requests_gets_json_response()
{
   expected="{\"host_requests\":[]}"
   actual=$(get_new_host_requests)
   # echo $actual | jq .
   assertEquals "${expected} == ${actual}" "${expected}" "${actual}"
}


test_get_new_host_requests_handles_no_new_requests()
{
   expected=""
   actual=$(get_new_host_requests | jq '.host_requests[]')
   # echo $actual | jq '.host_requests[]'
   assertEquals "${expected} == ${actual}" "${expected}" "${actual}"
}

test_get_new_host_requests_gets_request()
{
   typeset request_id
   expected="pi_test.sh"

   sql_results=$(run_sql_proc "host_request.create_request('pi_test.sh', 'parm1')")
   request_id=$(run_sql_func "host_request.find_request_id('pi_test.sh', 'parm1')")

   actual=$(get_new_host_requests)
   #echo $actual | jq .
   assertContains "${actual} CONTAINS ${expected}" "${actual}" "${expected}"

   sql_results=$(run_sql_proc "host_request.delete_request(${request_id})")
   unset request_id
}


# get_script_name tests ------------------------------------------------------
test_get_script_name_retrieves_script_name()
{
   typeset request_id
   expected="pi_test_dummy.sh"

   sql_results=$(run_sql_proc "host_request.create_request('pi_test_dummy.sh')")
   request_id=$(run_sql_func "host_request.find_request_id('pi_test_dummy.sh')")

   actual=$(get_script_name $request_id "$(get_new_host_requests)")
   assertEquals "${expected} == ${actual}" "${expected}" "${actual}"

   sql_results=$(run_sql_proc "host_request.delete_request(${request_id})")
   unset request_id
}


# get_parameters tests ------------------------------------------------------
test_get_parameters_retrieves_empty_string_for_null_parameter_string()
{
   typeset request_id
   expected=""

   sql_results=$(run_sql_proc "host_request.create_request('pi_test_dummy.sh')")
   request_id=$(run_sql_func "host_request.find_request_id('pi_test_dummy.sh')")

   actual=$(get_parameters $request_id "$(get_new_host_requests)")
   assertEquals "${expected} == ${actual}" "${expected}" "${actual}"

   sql_results=$(run_sql_proc "host_request.delete_request(${request_id})")
   unset request_id
}


test_get_parameters_retrieves_single_parameter()
{
   typeset request_id
   expected="Parm1"

   sql_results=$(run_sql_proc "host_request.create_request('pi_test_dummy.sh', 'Parm1')")
   request_id=$(run_sql_func "host_request.find_request_id('pi_test_dummy.sh', 'Parm1')")

   actual=$(get_parameters $request_id "$(get_new_host_requests)")
   assertEquals "${expected} == ${actual}" "${expected}" "${actual}"

   sql_results=$(run_sql_proc "host_request.delete_request(${request_id})")
   unset request_id
}


test_get_parameters_retrieves_multiple_parameters()
{
   typeset request_id
   expected="Parm1 parm2"

   sql_results=$(run_sql_proc "host_request.create_request('pi_test_dummy.sh', 'Parm1 parm2')")
   request_id=$(run_sql_func "host_request.find_request_id('pi_test_dummy.sh', 'Parm1 parm2')")

   actual=$(get_parameters $request_id "$(get_new_host_requests)")
   assertEquals "${expected} == ${actual}" "${expected}" "${actual}"

   sql_results=$(run_sql_proc "host_request.delete_request(${request_id})")
   unset request_id
}


test_get_parameters_retrieves_handles_command_line_options()
{
   typeset request_id
   expected="-v -x"


   sql_results=$(run_sql_proc "host_request.create_request('pi_test_dummy.sh', '-v -x Parm1 parm2')")
   request_id=$(run_sql_func "host_request.find_request_id('pi_test_dummy.sh', '-v -x Parm1 parm2')")

   actual=$(get_parameters $request_id "$(get_new_host_requests)")
   assertContains "${actual} CONTAINS ${expected}" "${actual}" "${expected}"

   sql_results=$(run_sql_proc "host_request.delete_request(${request_id})")
   unset request_id
}


test_get_parameters_retrieves_handles_quoted_parameters()
{
   typeset request_id
   expected='"parm1"'

   sql_results=$(run_sql_proc "host_request.create_request('pi_test_dummy.sh', '-v -x \"parm1\" parm2')")
   request_id=$(run_sql_func "host_request.find_request_id('pi_test_dummy.sh', '-v -x \"parm1\" parm2')")

   actual=$(get_parameters $request_id "$(get_new_host_requests)")
   assertContains "${actual} CONTAINS ${expected}" "${actual}" "${expected}"

   sql_results=$(run_sql_proc "host_request.delete_request(${request_id})")
   unset request_id
}




# mark_request_processing tests -----------------------------------------------
test_mark_request_processing_sets_status_to_processing()
{
   typeset request_id
   expected="PROCESSING"


   sql_results=$(run_sql_proc "host_request.create_request('pi_test_dummy.sh')")
   request_id=$(run_sql_func "host_request.find_request_id('pi_test_dummy.sh')")
   mark_request_processing ${request_id}

   actual=$(run_sql_func "host_request.get_status(${request_id})")
   assertEquals "${expected} == ${actual}" "${expected}" "${actual}"

   sql_results=$(run_sql_proc "host_request.delete_request(${request_id})")
   unset request_id
}



# mark_request_complete tests -----------------------------------------------
test_mark_request_complete_sets_status_to_complete()
{
   typeset request_id
   expected="COMPLETE"


   sql_results=$(run_sql_proc "host_request.create_request('pi_test_dummy.sh')")
   request_id=$(run_sql_func "host_request.find_request_id('pi_test_dummy.sh')")
   mark_request_complete ${request_id}

   actual=$(run_sql_func "host_request.get_status(${request_id})")
   assertEquals "${expected} == ${actual}" "${expected}" "${actual}"

   sql_results=$(run_sql_proc "host_request.delete_request(${request_id})")
   unset request_id
}



# load_log_file tests -------------------------------------------------------
test_load_log_file_loads_execution_results()
{
   typeset request_id
   typeset temp_log_file=/var/tmp/temp_load_log_file_loads_execution_results.log

   expected="Expected results loaded."

   echo $expected > $temp_log_file

   sql_results=$(run_sql_proc "host_request.create_request('pi_test_dummy.sh')")
   request_id=$(run_sql_func "host_request.find_request_id('pi_test_dummy.sh')")
   load_log_file ${request_id} $temp_log_file

   actual=$(run_sql_func "host_request.get_execution_results(${request_id})")
   assertEquals "${expected} == ${actual}" "${expected}" "${actual}"

   sql_results=$(run_sql_proc "host_request.delete_request(${request_id})")
   rm $temp_log_file 2> /dev/null
   unset request_id
   unset temp_log_file
}


test_load_log_file_logs_error_if_file_not_found()
{
   typeset request_id

   expected="ERROR: non-existing_file not found"


   sql_results=$(run_sql_proc "host_request.create_request('pi_test_dummy.sh')")
   request_id=$(run_sql_func "host_request.find_request_id('pi_test_dummy.sh')")
   actual=$(load_log_file ${request_id} non-existing_file)

   assertContains "${actual} CONTAINS ${expected}" "${actual}" "${expected}"

   sql_results=$(run_sql_proc "host_request.delete_request(${request_id})")
   unset request_id
}




# Source shunit2 (run the tests) ---------------------------------------------
. shunit2


# Complete
