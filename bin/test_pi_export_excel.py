#!/bin/python3
#-----------------------------------------------------------------------------
#
#   Script:      test_pi_export_excel.py
#
#   Description: This script contains the unit tests for pi_export_excel.py.
#
#   Parameters:  None
#
#   Usage:       python3 -m unittest test_pi_export_excel.py
#                or
#                ./test_pi_export_excel.py
#
#   Author:      <PERSON>
#
#   Note:        Some of the tests assume test spreadsheet data has been 
#                loaded into the database. Run:
#
#                    EXECUTE spreadsheet.reload_spreadsheet_template;
#
#                within sqlplus to restore the test data. 
#
#
# Copyright (c) 2025 Kevin <PERSON>uire
# All rights reserved.
#
# This software is proprietary and confidential. Unauthorized copying of this
# file, via any medium, is strictly prohibited without the express permission
# of the author or owning organization.
#
#-----------------------------------------------------------------------------
import unittest
import os
import textwrap
from openpyxl import load_workbook, Workbook
from jinja2 import Template, Environment, TemplateSyntaxError
from openpyxl.utils import get_column_letter
from openpyxl.styles import numbers, Font, PatternFill
from openpyxl.formatting.rule import CellIsRule

from pi_export_excel import get_db_password, get_worksheet_template, generate_excel_worksheet, apply_column_format, apply_cell_styles, apply_conditional_formatting, fetch_claims_from_db, fetch_instructions_from_db, get_worksheets, get_spreadsheet_filename



def is_valid_jinja2_template(template_str):
    """Returns True if template_str is a valid Jinja2 template; False otherwise."""
    env = Environment()
    try:
        env.parse(template_str)
        return True
    except TemplateSyntaxError as e:
        print(f"Syntax error at line {e.lineno}: {e.message}")
        return False



# Test class
class TestExcelGeneration(unittest.TestCase):

    def setUp(self):
        self.wb = Workbook()
        self.ws = self.wb.active
        self.headers = ["Name", "Amount", "Date"]
        self.ws.append(self.headers)
        self.ws.append(["Alice", 1000, "2024-01-01"])
        self.ws.append(["Bob", 50, "2024-02-02"])


        self.template_str = """
{
               "worksheet_name": "Claims", 
               "freeze_top_row": true,
               "bold_headers": true, 
               "auto_fit_columns": true, 
               "column_format": { 
                 "SourceRecordID": "text", 
                 "SeqNo": "text", 
                 "StateID": "text", 
                 "ProviderName": "text", 
                 "ProviderID": "text", 
                 "ProviderTIN": "text", 
                 "LOB": "text", 
                 "ProviderAccountID": "text", 
                 "ClaimNumber": "text", 
                 "ClaimPaidDate": "text", 
                 "ClaimPaidAmount": "text", 
                 "OpenPayableAmount": "text", 
                 "OpenPayableDate": "text", 
                 "OpenPayableAge": "text", 
                 "ShiftToAccount": "text" 
             }, 
            "rows": [
    {% for claim in claims %}
        {
            "SourceRecordID": "{{ claim.SourceRecordID }}",
            "SeqNo": "{{ claim.SeqNo }}",
            "StateID": "{{ claim.StateID }}",
            "ProviderName": "{{ claim.ProviderName }}",
            "ProviderID": "{{ claim.ProviderID }}",
            "ProviderTIN": "{{ claim.ProviderTIN }}",
            "LOB": "{{ claim.LOB }}",
            "ProviderAccountID": "{{ claim.ProviderAccountID }}",
            "ClaimNumber": "{{ claim.ClaimNumber }}",
            "ClaimPaidDate": "{{ claim.ClaimPaidDate }}",
            "ClaimPaidAmount": "{{ claim.ClaimPaidAmount }}",
            "OpenPayableAmount": "{{ claim.OpenPayableAmount }}",
            "OpenPayableDate": "{{ claim.OpenPayableDate }}",
            "OpenPayableAge": "{{ claim.OpenPayableAge }}",
            "ShiftToAccount": "{{ claim.ShiftToAccount }}"
        }{% if not loop.last %},{% endif %}
        {% endfor %}
    ]
}
"""
        self.context = {
            "claims": [
                {"SourceRecordID": "2983786", "SeqNo": "1", "StateID": "CA", "ProviderName": "ABC Providers", "ProviderID": "*********", "ProviderTIN": "**********", "LOB": "Medicare", "ProviderAccountID": "123999", "ClaimNumber": "********", "ClaimPaidDate": "2024-01-01", "ClaimPaidAmount": "100", "OpenPayableAmount": "70", "OpenPayableDate": "2024-02-02", "OpenPayableAge": "0-60", "ShiftToAccount": "Marketplace"},
                {"SourceRecordID": "2983787", "SeqNo": "2", "StateID": "CA", "ProviderName": "ABC Providers", "ProviderID": "*********", "ProviderTIN": "**********", "LOB": "Medicare", "ProviderAccountID": "123999", "ClaimNumber": "********", "ClaimPaidDate": "2024-01-02", "ClaimPaidAmount": "200", "OpenPayableAmount": "80", "OpenPayableDate": "2024-02-03", "OpenPayableAge": "0-60", "ShiftToAccount": "Marketplace"},
                {"SourceRecordID": "2983788", "SeqNo": "3", "StateID": "CA", "ProviderName": "ABC Providers", "ProviderID": "*********", "ProviderTIN": "**********", "LOB": "Medicare", "ProviderAccountID": "123999", "ClaimNumber": "********", "ClaimPaidDate": "2024-01-03", "ClaimPaidAmount": "300", "OpenPayableAmount": "90", "OpenPayableDate": "2024-02-04", "OpenPayableAge": "0-60", "ShiftToAccount": "Marketplace"},
                {"SourceRecordID": "2983789", "SeqNo": "4", "StateID": "CA", "ProviderName": "ABC Providers", "ProviderID": "*********", "ProviderTIN": "**********", "LOB": "Medicare", "ProviderAccountID": "123999", "ClaimNumber": "********", "ClaimPaidDate": "2024-01-04", "ClaimPaidAmount": "400", "OpenPayableAmount": "100", "OpenPayableDate": "2024-02-04", "OpenPayableAge": "60-120", "ShiftToAccount": "Marketplace"},
            ]
        }
        self.output_file = "test_claims.xlsx"

    def test_generate_excel_worksheet_creates_excel_file(self):
        generate_excel_worksheet(self.context, self.template_str, self.output_file, "test_worksheet")
        self.assertTrue(os.path.exists(self.output_file), "Excel file was not created.")

    def test_generate_excel_worksheet_adds_content(self):
        generate_excel_worksheet(self.context, self.template_str, self.output_file, "test_worksheet")
        wb = load_workbook(self.output_file)
        ws = wb.active

        # Check headers
        headers = [cell.value for cell in ws[1]]
        expected_headers = ["SourceRecordID", "SeqNo", "StateID", "ProviderName", "ProviderID", "ProviderTIN", "LOB", "ProviderAccountID", "ClaimNumber", "ClaimPaidDate", "ClaimPaidAmount", "OpenPayableAmount", "OpenPayableDate", "OpenPayableAge", "ShiftToAccount"]
        self.assertEqual(headers, expected_headers, "Headers do not match.")

        # Check row values
        expected_rows = [
            ["2983786", "1", "CA"],
            ["2983787", "2", "CA"],
            ["2983788", "3", "CA"],
            ["2983789", "4", "CA"],
        ]

        for i, expected_row in enumerate(expected_rows, start=2):
            actual_row = [ws.cell(row=i, column=j).value for j in range(1, 4)]
            self.assertEqual(actual_row, expected_row, f"Row {i} does not match expected data.")

    def test_get_db_password_retrieves_unencrypted_password(self):
        # Assumes the test_user entry exists in .connect
        expected="orcl_pw"
        actual = get_db_password("test_user")
        self.assertEqual(expected, actual, "The actual value was not equal to the expected.")

    def test_get_worksheet_template_returns_valid_template(self):
        actual = is_valid_jinja2_template(get_worksheet_template("Open Payables", "Claims"))
        self.assertTrue(actual, "Returned template was not a valid jinja2 format.")

    def test_get_worksheet_template_returns_worksheet(self):
        expected = "{\"worksheet_name\": \"Claims\","
        actual = get_worksheet_template("Open Payables", "Claims")
        # print (f"Actual:\n {actual}")
        self.assertIn(expected, actual, 'Returned template does not contain the worksheet data')

    def test_apply_column_format(self):
        column_format = {
            "Amount": "currency",
            "Date": "date"
        }
        apply_column_format(self.ws, column_format, self.headers)

        amount_col = self.ws["B"]
        self.assertEqual(amount_col[1].number_format, numbers.FORMAT_CURRENCY_USD_SIMPLE)
        self.assertEqual(amount_col[1].alignment.horizontal, "right")

        date_col = self.ws["C"]
        self.assertEqual(date_col[1].number_format, numbers.FORMAT_DATE_YYYYMMDD2)


    def test_apply_cell_styles(self):
        styles = {
            "Amount": {
                "font_color": "FF0000",
                "font_size": 14,
                "bg_color": "FFFF00"
            }
        }
        apply_cell_styles(self.ws, styles, self.headers)

        cell = self.ws["B2"]
        self.assertEqual(cell.font.color.rgb, "00FF0000")  # Returned rgb is eight bytes
        self.assertEqual(cell.font.size, 14)
        self.assertEqual(cell.fill.start_color.rgb, "00FFFF00")


    def test_fetch_claims_from_db_returns_data(self):
        expected = "SourceRecordID"
        context = {
            "records": fetch_claims_from_db()
        }
        template = Template(get_worksheet_template("Open Payables", "Claims"))
        
        actual = template.render(context)
        # print (f"Actual:\n {actual}")
        self.assertIn(expected, actual, 'Claims data not returned')


    def test_fetch_instructions_from_db_returns_data(self):
        expected = "Instruction"
        context = {
            "records": fetch_instructions_from_db()
        }
        template = Template(get_worksheet_template("Open Payables", "Instructions"))

        actual = template.render(context)
        # print (f"Actual:\n {actual}")
        self.assertIn(expected, actual, 'Instruction data not returned')



    def test_get_worksheets_returns_data(self):
        expected = {'worksheet_id': 41, 'spreadsheet_id': 81, 'worksheet_order': 1, 'worksheet_name': 'Instructions'}
        actual = get_worksheets("Open Payables")
        # print (f"Actual:\n {actual}")
        self.assertIn(expected, actual,'Worksheet data not returned') 


    def test_get_spreadsheet_filename_retieves_filename(self):
        expected="Open_Payables.xlsx"
        actual = get_spreadsheet_filename("Open Payables")
        self.assertEqual(expected, actual, "The actual value was not equal to the expected.")


    def tearDown(self):
        self.wb.close()
        if os.path.exists(self.output_file):
            os.remove(self.output_file)

if __name__ == "__main__":
    unittest.main()

