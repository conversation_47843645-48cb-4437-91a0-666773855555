import unittest
import os
from openpyxl import load_workbook
from jinja2 import Template
from openpyxl.utils import get_column_letter

# Reuse the original program logic
def generate_excel_from_template(context, template_str, output_file):
    template = Template(template_str)
    rendered_data = eval(template.render(context))  # Assuming safe context
    from openpyxl import Workbook

    wb = Workbook()
    ws = wb.active
    ws.title = "Employees"

    headers = list(rendered_data[0].keys())
    ws.append(headers)

    for row in rendered_data:
        ws.append(list(row.values()))

    wb.save(output_file)

# Test class
class TestExcelGeneration(unittest.TestCase):

    def setUp(self):
        self.template_str = """
        [
            {% for user in users %}
            {
                "Name": "{{ user.name }}",
                "Age": {{ user.age }},
                "Department": "{{ user.department }}"
            }{% if not loop.last %},{% endif %}
            {% endfor %}
        ]
        """
        self.context = {
            "users": [
                {"name": "<PERSON>", "age": 30, "department": "HR"},
                {"name": "<PERSON>", "age": 25, "department": "Engineering"},
                {"name": "<PERSON>", "age": 28, "department": "Marketing"},
            ]
        }
        self.output_file = "test_employees.xlsx"

    def test_excel_file_creation(self):
        generate_excel_from_template(self.context, self.template_str, self.output_file)
        self.assertTrue(os.path.exists(self.output_file), "Excel file was not created.")

    def test_excel_content(self):
        generate_excel_from_template(self.context, self.template_str, self.output_file)
        wb = load_workbook(self.output_file)
        ws = wb.active

        # Check headers
        headers = [cell.value for cell in ws[1]]
        expected_headers = ["Name", "Age", "Department"]
        self.assertEqual(headers, expected_headers, "Headers do not match.")

        # Check row values
        expected_rows = [
            ["Alice", 30, "HR"],
            ["Bob", 25, "Engineering"],
            ["Charlie", 28, "Marketing"]
        ]

        for i, expected_row in enumerate(expected_rows, start=2):
            actual_row = [ws.cell(row=i, column=j).value for j in range(1, 4)]
            self.assertEqual(actual_row, expected_row, f"Row {i} does not match expected data.")

    def tearDown(self):
        if os.path.exists(self.output_file):
            os.remove(self.output_file)

if __name__ == "__main__":
    unittest.main()
