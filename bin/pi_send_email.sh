#!/bin/bash
#-----------------------------------------------------------------------------
#
#   PROCEDURE:    pi_send_email.sh
#
#   PURPOSE:      This script is a wrapper for the send_email common function.
#
#   PARAMETERS:   body - the body of the email message (either a text string or
#                 a file)
#
#   USAGE:        From the command-line, type:
#                    pi_send_email.sh [-i <interface>] [-t <to_address>] \
#                       [-c <cc_address>] [-b <bcc_address>] [-s <subject>] \
#                       [-a <attachments>] <body>
#
#   AUTHOR:       <PERSON>
#
#
# Copyright (c) 2025 Kevin <PERSON>uire
# All rights reserved.
#
# This software is proprietary and confidential. Unauthorized copying of this
# file, via any medium, is strictly prohibited without the express permission
# of the author or owning organization.
#
#-----------------------------------------------------------------------------


# Include the "common" functions
unset PI_COMMON_FUNCTIONS
. $(dirname $0)/pi_common.sh

typeset subject=""
typeset addressees=""
typeset cc_addressees=""
typeset bcc_addressees=""
typeset attachments=""
typeset verbose=""

# Get the options
while getopts ":s:t:c:b:a:v" option
do
   case $option in
      s)  subject="${OPTARG} ";;
      t)  addressees="-t ${OPTARG} ";;
      c)  cc_addressees="-c ${OPTARG} ";;
      b)  bcc_addressees="-b ${OPTARG} ";;
      a)  attachments="-a ${OPTARG} ";;
      v)  verbose="-v ";;
      :)  echo "Option -$OPTARG requires a value";;
      \?) echo -u2 "Unknown option: $OPTARG";;
   esac
done
shift "$((OPTIND-1))"

typeset body="${1:-""}"

echo "pi_send_email Parameters"
      echo "   Interface:  $interface"
      echo "   To:         $addressees"
      echo "   CC:         $cc_addressees"
      echo "   BCC:        $bcc_addressees"
      echo "   Subject:    $subject"
      echo "   Attachment: $attachments"
      echo "   Body:       $body"

echo "send_email ${verbose} -s "${subject}" ${addressees}${cc_addressees}${bcc_addressees}${attachments} \"${body}\""
send_email ${verbose} -s "${subject}" ${addressees}${cc_addressees}${bcc_addressees}${attachments} "${body}"

# Works
# send_email -v -s "Testing pi_send_email.sh 19" -c <EMAIL> -t <EMAIL> -a body.txt  body.txt
# send_email -v -s "Testing pi_send_email.sh 19" -c <EMAIL> -t <EMAIL> -a body.txt "This is a text body"


# Complete
