#!/bin/bash
#-----------------------------------------------------------------------------
#
#   PROCEDURE:    pi_check_email.sh
#
#   PURPOSE:      This script checks for and processes (logs them to the 
#                 database) response emails. 
#
#   PARAMETERS:   None.
#
#   USAGE:        From the command-line, type:
#                    ./pi_check_email.sh
#
#   AUTHOR:       <PERSON>
#
#
# Copyright (c) 2025 <PERSON>re
# All rights reserved.
#
# This software is proprietary and confidential. Unauthorized copying of this
# file, via any medium, is strictly prohibited without the express permission
# of the author or owning organization.
#
#-----------------------------------------------------------------------------


# Include the "common" functions
unset PI_COMMON_FUNCTIONS
. $(dirname $0)/pi_common.sh

unset PI_EMAIL_FUNCTIONS
. $(dirname $0)/pi_email.sh


typeset email_record
typeset email_header
typeset email_number
typeset email_size
typeset subject
typeset sender
typeset email_date

typeset save_option

# Get the options
while getopts ":s:" option
do
   case $option in
      s)  typeset -u save_changes=${OPTARG};;
      :)  echo "Option -$OPTARG requires a value";;
      \?) echo -u2 "Unknown option: $OPTARG";;
   esac
done
shift $((OPTIND-1))

# Defaults to "Save" (N is for testing)
if [ "$save_changes" = "N" ]
then
   save_option="N"
else
   save_option="Y"
fi



# Get the email headers and process each email
list_email_headers | while read email_record
do
   # echo $email_record

   if is_number "$(echo $email_record | cut -c1-2 | tr -d ' ')"
   then
      email_header=$(echo "$email_record")
   else
      # Remove the first two characters
      email_header=$(echo "$email_record" | cut -c3-)
   fi

   email_number=$(echo $email_header | awk '{print $1}')

   email_size=$(echo $email_header | awk '{print $8}' | cut -c2-)

   # subject is being truncated to 21 characters???
   # => The header record is only returning 80 characters and
   # subject is the last field.
   # subject=$(echo $email_header | awk -F\" '{print $2}')
   # Use a function to retrieve the message and parse the Subject.
   subject="$(get_email_subject $email_number)"

   sender="$(get_email_sender $email_number)"
   email_date="$(get_email_date $email_number)" 

   echo "Email Number: $email_number"
   echo "Subject:      $subject"
   echo "Sender:       $sender"
   echo "Sent:         $email_date"
   echo "Size:         $email_size"
   echo

   # If subject starts with "#Workflow Update", log it and move it.
   # Example:
   #    #Workflow Update: Approve : 123456789 
   if [ "$(echo $subject | awk -F: '{print $1}')" == "#Workflow Update" ]
   then
      echo "Logging $subject ..."
      run_sql_proc "letters.log_email('$subject', '$sender', '$email_date')"

      # Move the email to the "+processed" local folder. If it dosn't exist, 
      # it'll be created.
      # The "-s[Y/N]" command-line option determines if changes to the server 
      # mailbox are saved (i.e. "-sN" leaves the server mailbox as it was).
      move_email -s${save_option} "$email_number" "processed"
      
   else
      echo "Skipping $subject"
   fi

done


# Clean up

unset email_record
unset email_header
unset email_number
unset email_size
unset subject
unset sender
unset email_date

# Complete
