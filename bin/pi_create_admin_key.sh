#!/bin/bash
#-----------------------------------------------------------------------------
#
#   PROCEDURE:    pi_create_admin_key.sh
#
#   PURPOSE:      This script creates the Simplipied administrator GPG key.
#
#   PARAMETERS:   key_name       - the GPG key identifier
#                 key_email      - the recipient's email address
#                 key_comment    - description of the key
#                 key_expiration - Typically "1Y"; defaults to "Never"
#
#   USAGE:        From the command-line, type:
#                    pi_create_admin_key.sh [key_name] [key_email] \
#                       [key_comment] [key_expiration]
#
#   AUTHOR:       <PERSON>
#
#
# Copyright (c) 2025 Kevin <PERSON>uire
# All rights reserved.
#
# This software is proprietary and confidential. Unauthorized copying of this
# file, via any medium, is strictly prohibited without the express permission
# of the author or owning organization.
#
#-----------------------------------------------------------------------------


# Include the "common" functions
unset PI_COMMON_FUNCTIONS
. $(dirname $0)/pi_common.sh

# Local variables
typeset key_name=${1:-"PI_ADMIN"}
typeset key_email=${2:-"<EMAIL>"}
typeset key_comment=${3:-"Administration Services"}
typeset key_expiration=${4:-"Never"}
typeset key_fingerprint
typeset key_file
typeset key_expire_date



# Check if the key already exists
if [ "$(gpg --list-keys | grep -i ${key_name})" = "" ]
then

   # Call generate_gpg_key to create the new key
   generate_gpg_key "$key_name" $key_email "$key_comment" $key_expiration

   # Needed for functions
   update_recipient $key_name $key_email
   update_key_used $key_name $key_email

   key_fingerprint=$(get_gpg_fingerprint "${key_name} (${key_comment})")

   if [ "${key_fingerprint}" != "" ]
   then
      echo "Adding new $key_name public key ..."

      key_file=/var/tmp/public_key_${key_fingerprint}

      export_public_key "${key_fingerprint}" $key_file

      key_expire_date=$(get_gpg_key_expiration ${key_fingerprint})

      echo "... New $key_name public key added."

   else # No fingerprint found
      echo "ERROR: A problem occurred when trying to generate the new $key_name public key."
   fi

else
   echo "$key_name already exists"
fi

# Clean-up
unset key_name
unset key_email
unset key_comment
unset key_expiration
unset key_fingerprint
unset key_file
unset key_expire_date

# Complete
