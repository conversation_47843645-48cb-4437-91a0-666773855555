#!/bin/bash
#-----------------------------------------------------------------------------
#
#   PROCEDURE:    pi_shutdown_host_request_daemon.sh
#
#   PURPOSE:      This script is used to shutdown the Host Request daemon 
#                 process.
#
#   PARAMETERS:   None.
#
#   USAGE:        At the LINUX prompt, type:
#                    ./pi_shutdown_host_request_daemon.sh
#
#   AUTHOR:       <PERSON>
#
#
# Copyright (c) 2025 <PERSON>
# All rights reserved.
#
# This software is proprietary and confidential. Unauthorized copying of this
# file, via any medium, is strictly prohibited without the express permission
# of the author or owning organization.
#
#-----------------------------------------------------------------------------


# Include the "common" functions
. $(dirname $0)/pi_common.sh

# Local variables
typeset result


# The host request daemon process looks for the pi_stop_host_request_daemon
# file in the /var/tmp directory. If it finds it, it shuts down gracefully.
# The daemon process will remove the file as it shuts down.

# Check if the daemon is currently running.  If so shut it down.
result=$(ps -ef | grep pi_host_request_daemon.sh | grep -v grep)

if [ "$result" = "" ]
then
   printf "%s\n" "Host Request daemon not currently running."
else
   printf "%s\n" "Shutting down the Host Request daemon ..."

   # Create the semiphore file.
   touch /var/tmp/pi_stop_host_request_daemon

   # Change the permissions on the file
   chmod 777 /var/tmp/pi_stop_host_request_daemon

   # Wait for the file to be removed by the host request daemon process
   while [ -r /var/tmp/pi_stop_host_request_daemon ]
   do
      sleep 10 
      printf "%s\n" "...waiting (`date`)"
   done

   printf "%s\n\n" ...stopped.

fi


unset result

# Complete
