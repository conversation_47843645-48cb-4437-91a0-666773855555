#!/bin/bash
#-----------------------------------------------------------------------------
#
#   PROCEDURE:    pi_shutdown_public_key_daemon.sh
#
#   PURPOSE:      This script is used to shutdown the "public key" daemon 
#                 process.
#
#   PARAMETERS:   None.
#
#   USAGE:        At the UNIX prompt, type:
#                    pi_shutdown_public_key_daemon.sh
#
#   AUTHOR:       <PERSON>
#
#
# Copyright (c) 2025 <PERSON>
# All rights reserved.
#
# This software is proprietary and confidential. Unauthorized copying of this
# file, via any medium, is strictly prohibited without the express permission
# of the author or owning organization.
#
#-----------------------------------------------------------------------------


# Include the "common" functions
. $(dirname $0)/pi_common.sh

# Local variables
typeset result
typeset shutdown_semaphore=/var/tmp/pi_stop_public_key_daemon

# The public key daemon process looks for the "shutdown_semaphore" file
# in the /var/tmp directory. If it finds it, it shuts down gracefully.
# The daemon process will remove the file as it shuts down.

# Check if the daemon is currently running.  If so shut it down.
result=$(ps -ef | grep pi_public_key_daemon.sh | grep -v grep)

if [ "$result" = "" ]
then
   printf "%s\n" "\"Public Key\" daemon not currently running."
else
   printf "%s\n" "Shutting down the \"public key\" daemon ..."

   # Create the semiphore file.
   touch $shutdown_semaphore

   # Change the permissions on the file
   chmod 777 $shutdown_semaphore

   # Wait for the file to be removed by the daemon process
   while [ -r ${shutdown_semaphore} ]
   do
      sleep 10 
      printf "%s\n" "...waiting (`date`)"
   done

   printf "%s\n\n" ...stopped.

fi

unset result
unset shutdown_semaphore

# Complete
