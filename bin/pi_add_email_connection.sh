#!/bin/bash
#-----------------------------------------------------------------------------
#
#   PROCEDURE:    pi_add_email_connection.sh
#
#   PURPOSE:      This script adds the given email connection to the
#                 given configuration file. 
#
#   PARAMETERS:   mailbox          - The mailx server inbox
#                 mailbox_user     - The mailx user account
#                 mailbox_password - The mailx user's password (will be encrypted)
#                 config_file      - Defaults to $SIMPLIPIED_HOME/.mail_connect
#
#   USAGE:        From the command-line, type:
#                    pi_add_email_connection.sh <mailbox> <mailbox_user> \
#                       <mailbox_password> [config_file]
#
#   AUTHOR:       <PERSON>
#
#   NOTES:        Because GitLab variables cannot contain "#", "~" was used.
#                 They will be replaced before encrypting the password.
#
#                 Parameter defaults (confile_file) are handled in the 
#                 add_email_connection function (see pi_email.sh).
#
# Copyright (c) 2025 <PERSON>
# All rights reserved.
#
# This software is proprietary and confidential. Unauthorized copying of this
# file, via any medium, is strictly prohibited without the express permission
# of the author or owning organization.
#
#-----------------------------------------------------------------------------


# Include the "common" functions
unset PI_COMMON_FUNCTIONS
. $(dirname $0)/pi_common.sh

# Include the "common" functions
unset PI_EMAIL_FUNCTIONS
. $(dirname $0)/pi_email.sh


# Local variables
typeset mailbox=${1}
typeset mailbox_user=${2}
typeset mailbox_password=${3}
typeset config_file=${4:-"${SIMPLIPIED_HOME}/.mail_connect"}
typeset translated_password="$(echo ${3} | tr \~ \#)"

if [ "$mailbox" = "" ]
then
   echo "USAGE:"
   echo "   pi_add_email_connection.sh <mailbox> <mailbox_user> <mailbox_password> [config_file]"
   echo
   echo "Where"
   echo "   mailbox          - The mailx server inbox"
   echo "   mailbox_user     - The mailx user account"
   echo "   mailbox_password - The mailx user's password (will be encrypted)"
   echo "   config_file      - Defaults to $SIMPLIPIED_HOME/.mail_connect"
   exit 1
fi


# Check if the key already exists
if [ "$(email_connection_exists ${mailbox} ${mailbox_user} "${config_file}")" = "YES" ]
then
   echo "The ${mailbox_user} connection for ${mailbox} already exists in ${config_file}."
else
   add_email_connection "${mailbox}" "${mailbox_user}" "${translated_password}" "${config_file}"
fi

# Clean-up
unset mailbox
unset mailbox_user
unset mailbox_password
unset config_file
unset translated_password

# Complete
