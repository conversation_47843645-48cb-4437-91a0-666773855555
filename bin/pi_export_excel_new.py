#!/usr/bin/env python3
"""
Excel export CLI for SimpliPIed.

This script provides a command-line interface for generating Excel files
from templates using the SimpliPIed package.
"""

import sys
import os
import argparse

# Add the src directory to Python path
sys.path.insert(0, os.path.join(os.path.dirname(os.path.dirname(__file__)), 'src'))

from simplipied.excel import generate_excel_from_template
from simplipied.excel.export import get_sample_data, get_sample_template


def parse_args():
    """Parse command line arguments."""
    parser = argparse.ArgumentParser(description="Generate Excel file from template.")
    parser.add_argument(
        "--output",
        default="employees.xlsx",
        help="Output Excel file path (default: employees.xlsx)"
    )
    parser.add_argument(
        "--template",
        help="Path to custom template file"
    )
    parser.add_argument(
        "--data",
        help="Path to custom data file (JSON format)"
    )
    parser.add_argument(
        "--sample",
        action="store_true",
        help="Use sample data and template"
    )
    return parser.parse_args()


def load_template_from_file(template_path: str) -> str:
    """Load template from file."""
    try:
        with open(template_path, 'r') as f:
            return f.read()
    except FileNotFoundError:
        print(f"Error: Template file not found: {template_path}")
        sys.exit(1)
    except Exception as e:
        print(f"Error reading template file: {e}")
        sys.exit(1)


def load_data_from_file(data_path: str) -> dict:
    """Load data from JSON file."""
    import json
    try:
        with open(data_path, 'r') as f:
            return json.load(f)
    except FileNotFoundError:
        print(f"Error: Data file not found: {data_path}")
        sys.exit(1)
    except json.JSONDecodeError as e:
        print(f"Error parsing JSON data file: {e}")
        sys.exit(1)
    except Exception as e:
        print(f"Error reading data file: {e}")
        sys.exit(1)


def main():
    """Main entry point."""
    args = parse_args()
    
    try:
        if args.sample or (not args.template and not args.data):
            # Use sample data and template
            print("Using sample data and template...")
            context = {"users": get_sample_data()}
            template_str = get_sample_template()
        else:
            # Load custom template and data
            if not args.template:
                print("Error: --template is required when not using --sample")
                sys.exit(1)
            if not args.data:
                print("Error: --data is required when not using --sample")
                sys.exit(1)
            
            print(f"Loading template from: {args.template}")
            template_str = load_template_from_file(args.template)
            
            print(f"Loading data from: {args.data}")
            context = load_data_from_file(args.data)
        
        # Generate Excel file
        print(f"Generating Excel file: {args.output}")
        generate_excel_from_template(context, template_str, args.output)
        
        print(f"Excel file '{args.output}' generated successfully.")
        
    except Exception as e:
        print(f"Error: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
