#!/bin/bash
#-----------------------------------------------------------------------------
#
#   PROCEDURE:    test_pi_postgrid.sh
#
#   PURPOSE:      This script contains the unit tests for the Rapid PIE 
#                 PostGrid functions.
#
#   PARAMETERS:   None.
#
#   USAGE:        From the command-line, type:
#                    ./test_pi_postgrid.sh
#
#   AUTHOR:       <PERSON>
#
#
# Copyright (c) 2025 <PERSON>uire
# All rights reserved.
#
# This software is proprietary and confidential. Unauthorized copying of this
# file, via any medium, is strictly prohibited without the express permission
# of the author or owning organization.
#
#-----------------------------------------------------------------------------

#suite()
#{
#   suite_addTest test_create_postgrid_contact_creates_contact
#   suite_addTest test_delete_postgrid_contact_removes_contact
#   suite_addTest test_create_postgrid_letter_creates_letter
#}

# Before all
oneTimeSetUp()
{

   # Include the functions
   unset PI_COMMON_FUNCTIONS
   . $(dirname $0)/pi_common.sh

   unset PI_POSTGRID_FUNCTIONS
   . $(dirname $0)/pi_postgrid.sh


   typeset results=""

   # Create the Letter file
   echo "This is a test." > /var/tmp/temp_notification_letter.txt
   cupsfilter /var/tmp/temp_notification_letter.txt > /var/tmp/ACME_123456789_ACMEANVILC_20241028.pdf 2> /dev/null

   # cp ../test/test_letter.pdf /var/tmp/ACME_123456789_ACMEANVILC_20241028.pdf
   cp ${SIMPLIPIED_HOME}/test/test_letter.pdf /var/tmp/


   # Create test notification letter
   echo "INSERT INTO ntf_notificationdeliveries " > /var/tmp/temp_create_test_notification.sql
   echo "(notificationbatchid, notificationrefnumber, sourcesystemcode, letterdate, " >> /var/tmp/temp_create_test_notification.sql
   echo "providertin, providerattention, providergroupname, provideraddressline1, " >> /var/tmp/temp_create_test_notification.sql 
   echo "provideraddressline2, providercity, providerstatecode, providerzip, transmitfilename) " >> /var/tmp/temp_create_test_notification.sql
   echo "VALUES " >> /var/tmp/temp_create_test_notification.sql

   echo "(-99, '**********', 'AHS', TO_DATE('06/21/24', 'MM/DD/RR'), '999999999', " >> /var/tmp/temp_create_test_notification.sql
   echo " NULL, 'TEST PROVIDER GROUP', '123 MAIN ST', NULL, 'ANYTOWN', " >> /var/tmp/temp_create_test_notification.sql
   echo " 'CA', '99999', 'ACME_123456789_ACMEANVILC_20241028'); " >> /var/tmp/temp_create_test_notification.sql


   echo "DELETE FROM ntf_notificationdeliveries WHERE notificationbatchid = '-99';" >> /var/tmp/temp_delete_test_notification.sql
   results=$(run_sql_script -s -d /var/tmp temp_create_test_notification.sql 2> /dev/null)

   unset results
}

# After all
oneTimeTearDown()
{
   typeset results=""

   results=$(run_sql_script -s -d /var/tmp temp_delete_test_notification.sql 2> /dev/null)
   rm /var/tmp/temp_create_test_notification.sql 2> /dev/null
   rm /var/tmp/temp_delete_test_notification.sql 2> /dev/null
   rm /var/tmp/temp_notification_letter.txt 2> /dev/null
   rm /var/tmp/ACME_123456789_ACMEANVILC_20241028.pdf* 2> /dev/null

   unset results
}


# get_aop_letter_number tests ------------------------------------------------
test_get_aop_letter_number_gets_letter_number()
{
   expected="**********"
   actual=$(get_aop_letter_number ACME_123456789_ACMEANVILC_20241028.pdf)
   assertEquals "${expected} == ${actual}" "${expected}" "${actual}"
}





# get_postgrid_api_key tests -------------------------------------------------
test_get_postgrid_api_key_defaults_to_test_key()
{
   expected="test_sk_"
   actual=$(get_postgrid_api_key | cut -c1-8) 
   assertEquals "${expected} == ${actual}" "${expected}" "${actual}"
}


test_get_postgrid_api_key_returns_production_key_for_production_mode()
{
   expected="live_sk_"
   actual=$(get_postgrid_api_key proDuction | cut -c1-8)
   assertEquals "${expected} == ${actual}" "${expected}" "${actual}"
}


test_get_postgrid_api_key_returns_test_key_for_test_mode()
{
   expected="test_sk_"
   actual=$(get_postgrid_api_key Test | cut -c1-8)
   assertEquals "${expected} == ${actual}" "${expected}" "${actual}"
}



# create_postgrid_contact tests ----------------------------------------------
test_create_postgrid_contact_creates_contact()
{
   typeset test_letter_number="**********"

   expected="contact_"
   actual=$(create_postgrid_contact $test_letter_number)
   assertContains "${actual} CONTAINS ${expected}" "${actual}" "${expected}"

   #delete_postgrid_contact $actual 2>&1 /dev/null
   unset test_letter_number
}



# delete_postgrid_contact tests ----------------------------------------------
test_delete_postgrid_contact_removes_contact()
{
   typeset test_letter_number="**********"
   typeset new_contact_id

   expected="deleted\":true"
   new_contact_id=$(create_postgrid_contact $test_letter_number)
   actual=$(delete_postgrid_contact $new_contact_id)
   assertContains "${actual} CONTAINS ${expected}" "${actual}" "${expected}"

   unset test_letter_number
   unset new_contact_id
}


# create_postgrid_letter tests -----------------------------------------------
test_create_postgrid_letter_creates_letter()
{
   typeset test_letter_file="/var/tmp/test_letter.pdf"
   typeset new_contact_id=$(create_postgrid_contact "**********")

# Manually created the test PDF file. Look into fixing cupsfilter.
#echo "Test is a test PDF file" > /var/tmp/ACME_123456789_ACMEANVILC_20241028.txt
#cupsfilter /var/tmp/ACME_123456789_ACMEANVILC_20241028.txt > /var/tmp/ACME_123456789_ACMEANVILC_20241028.pdf

   expected="letter_"
   actual=$(create_postgrid_letter "$new_contact_id" "$new_contact_id" "$test_letter_file" "test")
   assertContains "${actual} CONTAINS ${expected}" "${actual}" "${expected}"

   #delete_postgrid_contact $new_contact_id 2>&1 /dev/null
   unset test_letter_file
   unset new_contact_id
}



# Source shunit2 (run the tests) ---------------------------------------------
. shunit2


# Complete
