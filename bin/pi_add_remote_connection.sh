#!/bin/bash
#-----------------------------------------------------------------------------
#
#   PROCEDURE:    pi_add_remote_connection.sh
#
#   PURPOSE:      This script adds the given remote connection to the
#                 given configuration file. 
#
#   PARAMETERS:   site_name   - The target SFTP host
#                 user_id     - The remote user account
#                 password    - The remote user's password (will be encrypted)
#                 config_file - Defaults to $SIMPLIPIED_HOME/.netrc
#
#   USAGE:        From the command-line, type:
#                    pi_add_remote_connection.sh <site_name> <user_id> \
#                       <password> [config_file]
#
#   AUTHOR:       Kevin LeQuire
#
#   NOTES:        Because GitLab variables cannot contain "#", "~" was used.
#                 They will be replaced before encrypting the password.
#
#                 Parameter defaults (confile_file) are handled in the 
#                 add_remote_connection function (see pi_common.sh).
#
# Copyright (c) 2025 Kevin <PERSON>re
# All rights reserved.
#
# This software is proprietary and confidential. Unauthorized copying of this
# file, via any medium, is strictly prohibited without the express permission
# of the author or owning organization.
#
#-----------------------------------------------------------------------------


# Include the "common" functions
unset PI_COMMON_FUNCTIONS
. $(dirname $0)/pi_common.sh

# Local variables
typeset site_name=${1}
typeset user_id=${2}
typeset password=${3}
typeset config_file=${4:-"${SIMPLIPIED_HOME}/.netrc"}
typeset translated_password="$(echo ${3} | tr \~ \#)"

if [ "$site_name" = "" ]
then
   echo "USAGE:"
   echo "   pi_add_remote_connection.sh <site_name> <user_id> <password> [config_file]"
   echo
   echo "Where"
   echo "   site_name   - The target SFTP host"
   echo "   user_id     - The remote user account"
   echo "   password    - The remote user's password (will be encrypted)"
   echo "   config_file - Defaults to $SIMPLIPIED_HOME/.netrc"
   exit 1
fi


# Check if the key already exists
if [ "$(remote_connection_exists ${site_name} ${user_id} "${config_file}")" = "YES" ]
then
   echo "The ${site_name} connection for ${user_id} already exists in ${config_file}."
else
   add_remote_connection ${site_name} ${user_id} ${translated_password} ${config_file}
fi

# Clean-up
unset site_name
unset user_id
unset password
unset config_file
unset translated_password

# Complete
