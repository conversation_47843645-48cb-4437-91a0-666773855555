#!/bin/bash
#-----------------------------------------------------------------------------
#
#   PROCEDURE:    pi_run_sql_loader.sh
#
#   PURPOSE:      This script calls the run_sql_loader function to load the
#                 given data file using the given control file.
#
#   PARAMETERS:   control_file - The SQL*loader control file
#                 data_file    - The data file to load (includes the path)
#
#   USAGE:        From the command-line, type:
#                    ./pi_run_sql_loader.sh <control_file> <data_file>
#
#   AUTHOR:       <PERSON>
#
#
# Copyright (c) 2025 <PERSON>re
# All rights reserved.
#
# This software is proprietary and confidential. Unauthorized copying of this
# file, via any medium, is strictly prohibited without the express permission
# of the author or owning organization.
#
#-----------------------------------------------------------------------------


# Include the "common" functions
unset PI_COMMON_FUNCTIONS
. $(dirname $0)/pi_common.sh

# Get the parameter(s)
control_file=${1:-""}
data_file=${2:-""}

# Call the common function
#   Usage:
#      run_sql_loader [-u <user_id>] [-d <data_file>] \
#         [-b <bad_file>] <control_file> [substitutions]
#
#   Where:
#      [-u <user_id>] is the optional database ID (defaults to apps),
#      [-d <data_file>] is the optional data file (if overridding the control
#         file),
#      [-b <bad_file>] is the optional bad file (if overridding the default),
#      <control_file> is the required SQL*Loader control file name,
#      and
#      [substitutions] is for optional control file substitutions in the form
#      "Replacement string:New value[;string:value]".
#
# NOTES:
#   The default location for control files is ${SIMPLIED_HOME}/ctl.
#   (SIMPLIED_HOME is set in pi_common.sh).
#
#   "DATA_FILE_NAME" is replaced with the actual file name if it appears in the
#   control file (e.g. source CONSTANT "DATA_FILE_NAME").
#
run_sql_loader -d "$data_file" $control_file


# Complete
