#!/bin/bash
#-----------------------------------------------------------------------------
#
#   PROCEDURE:    test_pi_common.sh
#
#   PURPOSE:      This script contains the unit tests for the common functions.
#
#   PARAMETERS:   None.
#
#   USAGE:        From the command-line, type:
#                    ./test_pi_common.sh
#
#   AUTHOR:       <PERSON>uire
#
#
# Copyright (c) 2025 Kevin LeQuire
# All rights reserved.
#
# This software is proprietary and confidential. Unauthorized copying of this
# file, via any medium, is strictly prohibited without the express permission
# of the author or owning organization.
#
#-----------------------------------------------------------------------------

# xxxxxxxxxx
turn_off_suite()
#suite()
{
   suite_addTest test_add_remote_connection_creates_file_if_missing
   suite_addTest test_add_remote_connection_adds_site
   suite_addTest test_add_remote_connection_adds_user_id
   suite_addTest test_add_remote_connection_adds_password
   suite_addTest test_remote_connection_exists_detects_connection
   suite_addTest test_remote_connection_exists_detects_missing_connection
   suite_addTest test_update_remote_connection_updates_connection_password
   suite_addTest test_get_remote_connection_password_gets_password
   suite_addTest test_get_remote_connection_password_gets_first_user_if_multiple
   suite_addTest test_get_remote_connection_password_gets_correct_user_if_multiple_matching_sites
   suite_addTest test_get_remote_connection_get_connection
   suite_addTest test_get_remote_connection_gets_first_connection_if_multiple
   suite_addTest test_put_remote_file_transfers_file
   #suite_addTest test_encrypt_encrypts_string
   #suite_addTest test_encrypt_string_encrypts_string
   #suite_addTest test_encrypt_uses_default_key
   #suite_addTest test_decrypt_decrypts_encrypted_string
   #suite_addTest test_decrypt_uses_default_key
   #suite_addTest test_decrypt_string_decrypts_encrypted_string
   #suite_addTest test_decrypt_string_can_override_key
   #suite_addTest test_add_db_connection_creates_connect_file_if_missing
   #suite_addTest test_add_db_connection_encrypts_password
   #suite_addTest test_add_db_connection_updates_existing_connection
   #suite_addTest test_update_db_connection_updates_existing_connection_password
   #suite_addTest test_get_db_password_gets_and_decrypts_password
   #suite_addTest test_get_db_password_gets_correct_user
   #suite_addTest test_get_db_password_gets_correct_database
   #suite_addTest test_db_connection_exists_detects_existing_record
   #suite_addTest test_db_connection_exists_detects_missing_record
}

# Before all
oneTimeSetUp()
{

   # Include the "common" functions
   unset PI_COMMON_FUNCTIONS
   . $(dirname $0)/pi_common.sh

   typeset results=""

   touch /var/tmp/PI_TEST_DATA_FILE.dat

   # Create a dummy gpg key for checking the individual fields
   generate_gpg_key "Dummy User" <EMAIL> "Dummy comment" "1d" 2> /dev/null



   # Create a public key file 
   generate_gpg_key "External User" <EMAIL> "External Client" "1y" 2> /dev/null
   export_public_key "<EMAIL>" /var/tmp/temp_external_pub.gpg 2> /dev/null
   delete_gpg_key $(get_gpg_fingerprint <EMAIL> 2> /dev/null)

   # Create test email addresses
   echo "INSERT INTO pi_interfaces (interface, enabled, start_date, end_date) VALUES ('TEST_INTERFACE', 'Y', SYSDATE, SYSDATE + 1);" > /var/tmp/temp_create_test_emails.sql
   echo "INSERT INTO pi_interface_email_addresses (interface, email_address, address_type, enabled) VALUES ('TEST_INTERFACE', '<EMAIL>', 'TO', 'Y');" >> /var/tmp/temp_create_test_emails.sql
   echo "INSERT INTO pi_interface_email_addresses (interface, email_address, address_type, enabled) VALUES ('TEST_INTERFACE', '<EMAIL>', 'TO', 'Y');" >> /var/tmp/temp_create_test_emails.sql
   echo "INSERT INTO pi_interface_email_addresses (interface, email_address, address_type, enabled) VALUES ('TEST_INTERFACE', '<EMAIL>', 'CC', 'Y');" >> /var/tmp/temp_create_test_emails.sql
   echo "INSERT INTO pi_interface_email_addresses (interface, email_address, address_type, enabled) VALUES ('TEST_INTERFACE', '<EMAIL>', 'BCC', 'Y');" >> /var/tmp/temp_create_test_emails.sql
   echo "INSERT INTO pi_interface_email_addresses (interface, email_address, address_type, enabled) VALUES ('TEST_INTERFACE', '<EMAIL>', 'BCC', 'Y');" >> /var/tmp/temp_create_test_emails.sql
   echo "DELETE FROM pi_interface_email_addresses WHERE interface = 'TEST_INTERFACE';" > /var/tmp/temp_delete_test_emails.sql
   echo "DELETE FROM pi_interfaces WHERE interface = 'TEST_INTERFACE';" >> /var/tmp/temp_delete_test_emails.sql
   results=$(run_sql_script -s -d /var/tmp temp_create_test_emails.sql 2> /dev/null)


   # Create test notification letter
   echo "INSERT INTO ntf_notificationdeliveries " > /var/tmp/temp_create_test_notification.sql
   echo "(notificationbatchid, notificationrefnumber, sourcesystemcode, letterdate, " >> /var/tmp/temp_create_test_notification.sql
   echo "providertin, providerattention, providergroupname, provideraddressline1, " >> /var/tmp/temp_create_test_notification.sql 
   echo "provideraddressline2, providercity, providerstatecode, providerzip, transmitrefid) " >> /var/tmp/temp_create_test_notification.sql
   echo "VALUES " >> /var/tmp/temp_create_test_notification.sql
   echo "(-99, '**********', 'AHS', TO_DATE('06/21/24', 'MM/DD/RR'), '999999999', " >> /var/tmp/temp_create_test_notification.sql
   echo " NULL, 'TEST PROVIDER GROUP', '123 MAIN ST', NULL, 'ANYTOWN', " >> /var/tmp/temp_create_test_notification.sql
   echo " 'CA', '99999', 'ACME_123456789_ACMEANVILC_20241028'); " >> /var/tmp/temp_create_test_notification.sql
   echo "DELETE FROM ntf_notificationdeliveries WHERE notificationbatchid = '-99';" >> /var/tmp/temp_delete_test_notification.sql
   results=$(run_sql_script -s -d /var/tmp temp_create_test_notification.sql 2> /dev/null)

   # Create a test database password file and encrypt it
   if [ "$(db_connection_exists test_user testdb)" = "NO" ]
   then
      add_db_connection test_user password testdb $SIMPLIPIED_HOME/.connect
   fi
   if [ "$(db_connection_exists test_user orcl)" = "NO" ]
   then
      add_db_connection test_user orcl_pw orcl $SIMPLIPIED_HOME/.connect
   fi

   update_<NAME_EMAIL>
   update_key_<NAME_EMAIL>

   encrypt_file TEST /var/tmp/.test_connect.txt 2> /dev/null

   rm $connect_file 2> /dev/null
   unset results
}

# After all
oneTimeTearDown()
{

   typeset cleanup_fingerprint
   typeset revoke_file
   typeset results=""

   cleanup_fingerprint=$(get_gpg_fingerprint "<EMAIL>"  2> /dev/null)
   revoke_file="$HOME/.gnupg/openpgp-revocs.d/${cleanup_fingerprint}.rev"
   rm $revoke_file 2> /dev/null
   delete_gpg_key <EMAIL> 2> /dev/null

   rm /var/tmp/temp_pi_test_proc.lst 2> /dev/null
   rm /var/tmp/temp_run_sql_proc_$(date +%Y%m%d)*.lst 2> /dev/null
   rm /var/tmp/PI_TEST_DATA_FILE.dat 2> /dev/null
   rm /var/tmp/pi_test_control.ctl 2> /dev/null
   rm /var/tmp/PI_TEST_1234.dat 2> /dev/null

   rm /var/tmp/temp_external_pub.gpg  2> /dev/null

   results=$(run_sql_script -s -d /var/tmp temp_delete_test_emails.sql 2> /dev/null)
   rm /var/tmp/temp_create_test_emails.sql 2> /dev/null
   rm /var/tmp/temp_delete_test_emails.sql 2> /dev/null

   results=$(run_sql_script -s -d /var/tmp temp_delete_test_notification.sql 2> /dev/null)
   rm /var/tmp/temp_create_test_notification.sql 2> /dev/null
   rm /var/tmp/temp_delete_test_notification.sql 2> /dev/null

   rm /var/tmp/.test_connect.txt* 2> /dev/null

   unset cleanup_fingerprint
   unset revoke_file
   unset results
}



# Mock for the sqlldr command-line program
sqlldr()
{
   # Syntax:
   #   sqlldr userid=<user_id/password> control=<control_file> \
   #      log=<log_file> bad=<bad_file> data=<data_file>
   
   typeset arguments=("$@")
   
   echo "sqlldr ${arguments}"

#   for ((i = 0; i < ${#arguments[@]}; i++))
#   do 
#      echo ${arguments[${i}]}
#   done
   
   unset arguments
}



mailx()
{
   # Syntax:
   #   mailx -s <subject> -a <attachments> -c <CC list> -b <BCC list>
   #      -r <from address> to_address < email_body

   typeset subject=""
   typeset addressees=""
   typeset cc_addressees=""
   typeset bcc_addressees=""
   typeset attachments=""
   typeset body=""


   # Get the options
   local OPTIND s c b a r
   while getopts ":s:c:b:a:r:" option
   do
      case $option in
         s)  subject=${OPTARG};;
         c)  cc_addressees=${OPTARG};;
         b)  bcc_addressees=${OPTARG};;
         a)  attachments=${OPTARG};;
         r)  return_address=${OPTARG};;
         :)  echo "Option -$OPTARG requires a value";;
         \?) echo -u2 "Unknown option: $OPTARG";;
      esac
   done
   shift $((OPTIND-1))

   addressees="$1"

   # Get the body from the command-line
   read body

   echo "mailx SUBJECT:${subject} TO:${addressees} CC:${cc_addressees} BCC:${bcc_addressees} ATTACH:${attachments} RET:${return_address} BODY:${body}"

   unset subject
   unset addressees
   unset cc_addressees
   unset bcc_addressees
   unset attachments
   unset body
}


# file_substitute tests -------------------------------------------------------
test_file_substitute_substitutes_string_in_file()
{
   expected="The file contains the XYZ string."
   echo "The file contains the ABC string." > /var/tmp/temp_file_substitute_source.dat
   file_substitute_results=$(file_substitute ABC:XYZ /var/tmp/temp_file_substitute_source.dat /var/tmp/temp_file_substitute_target.dat)
   actual=$(cat /var/tmp/temp_file_substitute_target.dat)
   rm /var/tmp/temp_file_substitute_*.dat
   assertTrue "${expected} == ${actual}" '[[ ${expected} == ${actual} ]]'
}


test_file_substitute_substitutes_multiple_strings()
{
   expected="The file contains the XYZ string(456)."
   echo "The file contains the ABC string(123)." > /var/tmp/temp_file_substitute_source.dat
   file_substitute_results=$(file_substitute "ABC:XYZ;123:456" /var/tmp/temp_file_substitute_source.dat /var/tmp/temp_file_substitute_target.dat)
   actual=$(cat /var/tmp/temp_file_substitute_target.dat)
   rm /var/tmp/temp_file_substitute_*.dat
   assertTrue "${expected} == ${actual}" '[[ ${expected} == ${actual} ]]'
}



# upper tests ----------------------------------------------------------------
test_upper_returns_uppercase()
{
   expected="TEST"
   actual=$(upper "test")
   assertEquals "${expected} == ${actual}" "${expected}" "${actual}"
}



# is_number tests -_----------------------------------------------------------
test_is_number_returns_true_for_number()
{
   expected="Is a number"
   if is_number "1234"
   then
      actual="Is a number"
   else
      actual="Is NOT a number"
   fi
   assertEquals "${expected} == ${actual}" "${expected}" "${actual}"
}


test_is_number_returns_faluse_for_non-number()
{
   expected="Is NOT a number"
   if is_number "12x4"
   then
      actual="Is a number"
   else
      actual="Is NOT a number"
   fi
   assertEquals "${expected} == ${actual}" "${expected}" "${actual}"
}


# get_database tests ---------------------------------------------------------
test_get_database_gets_ahcdbvm1_database()
{
   expected="//**********:1521/AHCDB1_pdb1.dbnet.ahc.oraclevcn.com"
   actual=$(get_database ahcdbvm1)
   assertTrue "${expected} == ${actual}" '[[ ${expected} == ${actual} ]]'
}

test_get_database_gets_database_for_the_current_server()
{
   if [ "$(hostname)" = "ahcdbvm1" ]
   then
      expected="//**********:1521/AHCDB1_pdb1.dbnet.ahc.oraclevcn.com"
   else
      expected="orcl"
   fi
   actual=$(get_database)
   assertTrue "${expected} == ${actual}" '[[ ${expected} == ${actual} ]]'
}


# get_db_con tests -----------------------------------------------------------

test_get_db_con_finds_connect_file()
{
   typeset connect_file="/var/tmp/test_connect_exists.txt"
   expected="YES"

   rm $connect_file 2> /dev/null

   add_db_connection "test_user" "password" "testdb" $connect_file

   actual=$(db_connection_exists "test_user" "testdb" $connect_file)

   assertEquals "${expected} == ${actual}" "${expected}" "${actual}"

   rm $connect_file 2> /dev/null
   unset connect_file
}

test_get_db_con_retrieves_apps_connection()
{
   # Assuming "$SIMPLIPIED_HOME/.connect" exists and contains APPS 
   typeset db_con
   typeset found_user_id
   typeset found_database

   expected="apps@"
   db_con=$(get_db_con apps )
   found_user_id=$(echo $db_con | awk -F\/ '{print $1}')
   actual="${found_user_id}@"
   assertTrue "${expected} == ${actual}" '[[ ${expected} == ${actual} ]]'

   unset db_con
   unset found_user_id
   unset found_database
}

test_get_db_con_returns_error_if_the_user_was_not_found()
{
   expected="ERROR: Could not find the nonexisting_user ID for testdb."
   actual=$(echo $(get_db_con nonexisting_user testdb) | grep ERROR)
   assertTrue "${expected} == ${actual}" '[[ ${expected} == ${actual} ]]'
}

test_get_db_con_returns_error_if_the_database_was_not_found()
{
   expected="ERROR: Could not find the test_user ID for nonexisting_database."
   actual=$(echo $(get_db_con test_user nonexisting_database) | grep ERROR)
   assertTrue "${expected} == ${actual}" '[[ ${expected} == ${actual} ]]'
}

test_get_db_con_returns_connect_without_database_if_not_given()
{
   # Assuming "$SIMPLIPIED_HOME/.connect" contains TEST_USER for ORCL
   expected="test_user/orcl_pw"
   actual=$(echo $(get_db_con test_user))
   assertTrue "${expected} == ${actual}" '[[ ${expected} == ${actual} ]]'
}


# run_sql_script tests -------------------------------------------------------
test_run_sql_script_runs_a_sql_script()
{
   # This test relies on ${SIMPLIPIED_HOME}/sql/pi_test_connect.sql
   expected="APPS"
   actual="$(run_sql_script pi_test_connect.sql | grep USER | \
      awk '{print $2}')"
   assertTrue "${expected} == ${actual}" '[[ ${expected} == ${actual} ]]'
}


test_run_sql_script_override_default_user()
{
   # This test relies on ${SIMPLIPIED_HOME}/sql/pi_test_connect.sql
   expected="VND"
   actual="$(run_sql_script -uvnd pi_test_connect.sql | grep USER | \
      awk '{print $2}')"
   assertTrue "${expected} == ${actual}" '[[ ${expected} == ${actual} ]]'
}


test_run_sql_script_detects_missing_script_parameter()
{
   expected="ERROR: You need to specify a SQL script."
   actual="$(run_sql_script )"
   assertTrue "${expected} == ${actual}" '[[ ${expected} == ${actual} ]]'
}


test_run_sql_script_detects_missing_script()
{
   expected="ERROR: nonexisting.sql was not found in ${SIMPLIPIED_HOME}/sql"
   actual="$(run_sql_script nonexisting.sql)"
   assertTrue "${expected} == ${actual}" '[[ ${expected} == ${actual} ]]'
}


test_run_sql_script_override_default_directory()
{
   # This test relies on ${SIMPLIPIED_HOME}/sql/pi_test_connect.sql
   typeset temp_file_name=temp_$(date +%Y%m%d%H%M).sql

   cp ${SIMPLIPIED_HOME}/sql/pi_test_connect.sql /var/tmp/${temp_file_name}

   expected="APPS"
   actual="$(run_sql_script -d /var/tmp ${temp_file_name} | grep USER | \
      awk '{print $2}')"
   rm /var/tmp/${temp_file_name}
   unset temp_file_name

   assertTrue "${expected} == ${actual}" '[[ ${expected} == ${actual} ]]'
}



# run_sql_func tests ---------------------------------------------------------
test_run_sql_func_executes_a_stored_function()
{
   expected="apps"
   actual=$(run_sql_func "pi_test_connect('apps')")
   assertTrue "${expected} == ${actual}" '[[ ${expected} == ${actual} ]]'
}


test_run_sql_func_handles_parameter_with_imbedded_space()
{
   expected="Test spaces in parameter"
   actual=$(run_sql_func "pi_test_connect('${expected}')")
   assertTrue "${expected} == ${actual}" '[[ ${expected} == ${actual} ]]'

}


test_run_sql_func_handles_parameter_with_imbedded_ampersand()
{
   expected="Test & in parameter"
   actual=$(run_sql_func "pi_test_connect('${expected}')")
   assertTrue "${expected} == ${actual}" '[[ ${expected} == ${actual} ]]'

}



# run_sql_proc tests ---------------------------------------------------------
test_run_sql_proc_executes_a_stored_procedure()
{
   expected="in_parameter: PI_TEST PL/SQL procedure successfully completed."
   sql_proc_results=$(run_sql_proc -sn "pi_test_proc('PI_TEST')")
   actual=$(echo $sql_proc_results | grep PI_TEST)
   assertTrue "${expected} == ${actual}" '[[ ${expected} == ${actual} ]]'
}


test_run_sql_proc_handles_parameter_with_imbedded_space()
{
   expected="in_parameter: PI TEST PL/SQL procedure successfully completed."
   sql_proc_results=$(run_sql_proc -sy "pi_test_proc('PI TEST')")
   actual=$(echo $sql_proc_results | grep "PI TEST")
   assertTrue "${expected} == ${actual}" '[[ ${expected} == ${actual} ]]'
}

test_run_sql_proc_handles_parameter_with_imbedded_ampersand()
{
   expected="in_parameter: PI & TEST PL/SQL procedure successfully completed."
   sql_proc_results=$(run_sql_proc -sy "pi_test_proc('PI & TEST')")
   actual=$(echo $sql_proc_results | grep "PI & TEST")
   assertTrue "${expected} == ${actual}" '[[ ${expected} == ${actual} ]]'
}



# run_sql_loader tests -------------------------------------------------------
test_run_sql_loader_detects_missing_control_file_parameter()
{
   expected="ERROR: You need to specify a control file."
   sql_loader_results=$(run_sql_loader)
   actual=$(echo $sql_loader_results)
   assertTrue "${expected} == ${actual}" '[[ ${expected} == ${actual} ]]'
}


test_run_sql_loader_detects_missing_control_file()
{
   expected="ERROR: Unable to find the unknown.ctl control file in ${SIMPLIPIED_HOME}/ctl."
   sql_loader_results=$(run_sql_loader unknown.ctl)
   actual=$(echo $sql_loader_results)
   assertTrue "${expected} == ${actual}" '[[ ${expected} == ${actual} ]]'
}


test_run_sql_loader_finds_default_data_file()
{
   expected="data=/var/tmp/PI_TEST_DATA_FILE.dat"
   sql_loader_results=$(run_sql_loader pi_test_control.ctl)
   # Assuming "data=<file> will be in the third position
   actual=$(echo $sql_loader_results | awk '{print $3}')
   assertTrue "${expected} == ${actual}" '[[ ${expected} == ${actual} ]]'
}


test_run_sql_loader_can_override_default_data_file()
{
   expected="sqlldr userid=test_user/orcl_pw data=/var/tmp/data.csv log=/var/tmp/pi_test_control.log control=/var/tmp/pi_test_control.ctl"
   touch /var/tmp/data.csv
   touch /var/tmp/pi_test_control.log
   sql_loader_results=$(run_sql_loader -u test_user -d /var/tmp/data.csv pi_test_control.ctl)
   actual=$(echo $sql_loader_results)
   rm /var/tmp/data.csv
   rm /var/tmp/pi_test_control.log
   assertTrue "${expected} == ${actual}" '[[ ${expected} == ${actual} ]]'
}


test_run_sql_loader_detects_missing_data_file()
{
   expected="ERROR: Unable to find the /var/tmp/nonexisting_data.csv data file."
   sql_loader_results=$(run_sql_loader -u test_user -d /var/tmp/nonexisting_data.csv pi_test_control.ctl)
   actual=$(echo $sql_loader_results)
   assertTrue "${expected} == ${actual}" '[[ ${expected} == ${actual} ]]'
}


test_run_sql_loader_can_override_default_user()
{
   expected="sqlldr userid=test_user/orcl_pw data=/var/tmp/PI_TEST_DATA_FILE.dat log=/var/tmp/pi_test_control.log control=/var/tmp/pi_test_control.ctl"
   touch /var/tmp/pi_test_control.log
   sql_loader_results=$(run_sql_loader -u test_user pi_test_control.ctl)
   actual=$(echo $sql_loader_results)
   rm /var/tmp/pi_test_control.log
   assertTrue "${expected} == ${actual}" '[[ ${expected} == ${actual} ]]'
}


test_run_sql_loader_can_override_default_bad_file()
{
   expected="sqlldr userid=test_user/orcl_pw data=/var/tmp/PI_TEST_DATA_FILE.dat bad=/var/tmp/bad.log log=/var/tmp/pi_test_control.log control=/var/tmp/pi_test_control.ctl"
   touch /var/tmp/pi_test_control.log
   sql_loader_results=$(run_sql_loader -u test_user -b /var/tmp/bad.log pi_test_control.ctl)
   actual=$(echo $sql_loader_results)
   rm /var/tmp/pi_test_control.log
   assertTrue "${expected} == ${actual}" '[[ ${expected} == ${actual} ]]'
}


test_run_sql_loader_replaces_source_with_data_file()
{
   expected="   source         CONSTANT \"PI_TEST_1234.dat\","
   cp /var/tmp/PI_TEST_DATA_FILE.dat /var/tmp/PI_TEST_1234.dat
   sql_loader_results=$(run_sql_loader -u test_user -d /var/tmp/PI_TEST_1234.dat pi_test_control.ctl)
   actual=$(cat /var/tmp/pi_test_control.ctl | grep source)
   assertTrue "${expected} == ${actual}" '[[ ${expected} == ${actual} ]]'
}


test_run_sql_loader_replaces_multiple_substitutions()
{
   expected="   load_status         CONSTANT \"Loaded\","
   sql_loader_results=$(run_sql_loader -u test_user pi_test_control.ctl "NEW:Loaded;status:load_status")
   actual=$(cat /var/tmp/pi_test_control.ctl | grep status)
   assertTrue "${expected} == ${actual}" '[[ ${expected} == ${actual} ]]'
}


test_run_sql_loader_creates_log_file()
{
   expected="Log file created."
   sql_loader_results=$(run_sql_loader -u test_user pi_test_control.ctl)
   if [ "$(echo $sql_loader_results | grep /var/tmp/pi_test_control.log)" != "" ]
   then
      actual="Log file created."
   else
      actual=$(echo $sql_loader_results| grep /var/tmp/pi_test_control.log)
   fi
   assertTrue "${expected} == ${actual}" '[[ ${expected} == ${actual} ]]'
}


test_run_sql_loader_can_override_default_log_file()
{
   expected="sqlldr userid=test_user/orcl_pw data=/var/tmp/PI_TEST_DATA_FILE.dat bad=/var/tmp/bad.log log=/var/tmp/new_log.log control=/var/tmp/pi_test_control.ctl"
   touch /var/tmp/new_log.log
   sql_loader_results=$(run_sql_loader -u test_user -b /var/tmp/bad.log -l /var/tmp/new_log.log pi_test_control.ctl)
   actual=$(echo $sql_loader_results)
   rm /var/tmp/new_log.log
   assertTrue "${expected} == ${actual}" '[[ ${expected} == ${actual} ]]'
}





# move_inbound_file tests ----------------------------------------------------
test_move_inbound_file_moves_file()
{
   typeset moved_file
   touch ${SIMPLIPIED_INBOUND}/test_move_inbound_file.dat

   moved_file=$(move_inbound_file test_move_inbound_file.dat)

   expected="/var/tmp/test_move_inbound_file.dat"
   actual=$(ls /var/tmp/test_move_inbound_file.dat)
   assertTrue "${expected} == ${actual}" '[[ ${expected} == ${actual} ]]'

   rm /var/tmp/test_move_inbound_file.dat* 2> /dev/null
   unset moved_file
}


test_move_inbound_file_removes_the_file_from_the_inbound_directory()
{
   typeset moved_file
   touch ${SIMPLIPIED_INBOUND}/test_move_inbound_file.dat

   moved_file=$(move_inbound_file test_move_inbound_file.dat)

   expected=" No such file or directory"
   actual=$(ls ${SIMPLIPIED_INBOUND}/test_move_inbound_file.dat 2>&1)
   actual=$(echo $actual | awk -F: '{print $3}')
   assertTrue "${expected} == ${actual}" '[[ ${expected} == ${actual} ]]'

   rm /var/tmp/test_move_inbound_file.dat* 2> /dev/null
   unset moved_file
}


test_move_inbound_file_creates_archive_copy()
{
   typeset moved_file
   touch ${SIMPLIPIED_INBOUND}/test_move_inbound_file.dat

   moved_file=$(move_inbound_file test_move_inbound_file.dat)

   expected="1"
   actual=$(ls /var/tmp/test_move_inbound_file.dat_* | wc -l)
   assertTrue "${expected} == ${actual}" '[[ ${expected} == ${actual} ]]'

   rm /var/tmp/test_move_inbound_file.dat* 2> /dev/null
   unset moved_file
}


test_move_inbound_file_returns_file_not_found_for_nonexisting_file()
{
   expected="ERROR: No matching files found for test_move_inbound_file.dat in ${SIMPLIPIED_INBOUND}."
   actual=$(move_inbound_file test_move_inbound_file.dat)
   assertTrue "${expected} == ${actual}" '[[ ${expected} == ${actual} ]]'
}



test_move_inbound_file_finds_wildcard_file()
{
   typeset moved_file
   touch ${SIMPLIPIED_INBOUND}/test_move_inbound_file_123.dat

   moved_file=$(move_inbound_file test_move_inbound_file*.dat)

   expected="/var/tmp/test_move_inbound_file_123.dat"
   actual=$(ls /var/tmp/test_move_inbound_file_123.dat)
   assertTrue "${expected} == ${actual}" '[[ ${expected} == ${actual} ]]'

   rm /var/tmp/test_move_inbound_file_123.dat* 2> /dev/null
   unset moved_file
}


test_move_inbound_file_returns_first_if_multiple_matches()
{
   touch ${SIMPLIPIED_INBOUND}/test_move_inbound_file_123.dat
   touch ${SIMPLIPIED_INBOUND}/test_move_inbound_file_456.dat

   expected="/var/tmp/test_move_inbound_file_123.dat"
   actual=$(move_inbound_file test_move_inbound_file_*.dat) | cut -c1-39
   assertTrue "${expected} == ${actual}" '[[ ${expected} == ${actual} ]]'

   rm /var/tmp/test_move_inbound_file_*.dat* 2> /dev/null
   rm ${SIMPLIPIED_INBOUND}/test_move_inbound_file_456.dat 2> /dev/null
}



# mailx mock tests -----------------------------------------------------------
# mailx -s "${subject}" ${cc_addressees}${bcc_addressees}${attachments}${return_address}${addressees} < $temp_message

test_mailx_mock_returns_addressee()
{
   echo "This is the email body." > /var/tmp/mailx_mock_test_body.txt

   expected="mailx SUBJECT:Mock Test TO:<EMAIL> CC:<EMAIL> BCC:<EMAIL> ATTACH:attachment1.txt RET:<EMAIL> BODY:This is the email body."
   actual=$(mailx -s "Mock Test" -c <EMAIL> -b <EMAIL> -a attachment1.txt -r <EMAIL> <EMAIL> < /var/tmp/mailx_mock_test_body.txt)
   rm /var/tmp/mailx_mock_test_body.txt

   assertTrue "${expected} == ${actual}" '[[ ${expected} == ${actual} ]]'
}



# send_email tests -----------------------------------------------------------
test_send_email_calls_mailx()
{
   typeset email_body="Test."

   expected="mailx SUBJECT:Test Email TO:<EMAIL> CC: BCC: ATTACH: RET:<EMAIL> BODY:Test."
   actual=$(send_email -s "Test Email" -t <EMAIL> ${email_body})
   assertTrue "${expected} == ${actual}" '[[ ${expected} == ${actual} ]]'

   unset email_body
}


test_send_email_sets_subject()
{
   expected="New Subject"
   actual=$(send_email -s "New Subject" -t <EMAIL> "Test email body")
   actual="$(echo $actual | awk -F: '{print $2}' | sed 's/ TO//')"
   assertTrue "${expected} == ${actual}" '[[ ${expected} == ${actual} ]]'
}


test_send_email_sets_addresses()
{
   expected="<EMAIL>,<EMAIL>"
   actual=$(send_email -s "New Subject" -t "<EMAIL>,<EMAIL>" "Email body.")
   actual="$(echo $actual | awk -F: '{print $3}' | sed 's/ CC//')"
   assertTrue "${expected} == ${actual}" '[[ ${expected} == ${actual} ]]'
}


test_send_email_gets_interface_to_addresses()
{
   expected="<EMAIL>,<EMAIL>"
   actual=$(send_email -s "New Subject" -i "TEST_INTERFACE" "Email body.")
   actual="$(echo $actual | awk -F: '{print $3}' | sed 's/ CC//')"
   assertTrue "${expected} == ${actual}" '[[ ${expected} == ${actual} ]]'
}


test_send_email_sets_courtesy_copy_addresses()
{
   expected="<EMAIL>,<EMAIL>"
   actual=$(send_email -s "New Subject" -t <EMAIL> -c "<EMAIL>,<EMAIL>" "Email body.")
   actual="$(echo $actual | awk -F: '{print $4}' | sed 's/ BCC//')"
   assertTrue "${expected} == ${actual}" '[[ ${expected} == ${actual} ]]'
}


test_send_email_gets_interface_cc_addresses()
{
   expected="<EMAIL>"
   actual=$(send_email -s "New Subject" -i "TEST_INTERFACE" "Email body.")
   actual="$(echo $actual | awk -F: '{print $4}' | sed 's/ BCC//')"
   assertTrue "${expected} == ${actual}" '[[ ${expected} == ${actual} ]]'
}


test_send_email_sets_blind_courtesy_copy_addresses()
{
   expected="<EMAIL>,<EMAIL>"
   actual=$(send_email -s "New Subject" -t <EMAIL> -b "<EMAIL>,<EMAIL>" "Email body.")
   actual="$(echo $actual | awk -F: '{print $5}' | sed 's/ ATTACH//')"
   assertTrue "${expected} == ${actual}" '[[ ${expected} == ${actual} ]]'
}


test_send_email_gets_interface_bcc_addresses()
{
   expected="<EMAIL>,<EMAIL>"
   actual=$(send_email -s "New Subject" -i "TEST_INTERFACE" "Email body.")
   actual="$(echo $actual | awk -F: '{print $5}' | sed 's/ ATTACH//')"
   assertTrue "${expected} == ${actual}" '[[ ${expected} == ${actual} ]]'
}


test_send_email_handles_multiple_attachments()
{
   expected="file2.txt" # The mailx mock only returns the last "-a attachment".
   actual=$(send_email -s "New Subject" -t <EMAIL> -a "file1.dat,file2.txt" "Email body.")
   actual="$(echo $actual | awk -F: '{print $6}' | sed 's/ RET//')"
   assertTrue "${expected} == ${actual}" '[[ ${expected} == ${actual} ]]'
}


test_send_email_set_default_return_address()
{
   expected="<EMAIL>"
   actual=$(send_email -s "New Subject" -t <EMAIL> "Email body.")
   actual="$(echo $actual | awk -F: '{print $7}' | sed 's/ BODY//')"
   assertTrue "${expected} == ${actual}" '[[ ${expected} == ${actual} ]]'
}


test_send_email_sets_body_from_file()
{
   typeset body_file=/var/tmp/test_send_email_sets_body_from_file.txt

   expected="This is a test email using a file for the body."
   echo $expected > $body_file

   actual=$(send_email -s "New Subject" -t <EMAIL> ${body_file})
   actual="$(echo $actual | awk -F: '{print $8}')"

   assertTrue "${expected} == ${actual}" '[[ ${expected} == ${actual} ]]'

   rm $body_file 2> /dev/null
   unset body_file
}



# get_subject_prefix tests ---------------------------------------------------
test_get_subject_prefix_returns_error()
{
   expected="ERROR: "
   actual=$(get_subject_prefix 1)
   assertTrue "${expected} == ${actual}" '[[ ${expected} == ${actual} ]]'
}


test_get_subject_prefix_returns_blank_if_success()
{
   expected=""
   actual=$(get_subject_prefix 0)
   assertTrue "${expected} == ${actual}" '[[ ${expected} == ${actual} ]]'
}





# xxxxxxxxxx
# echo -e "$(cat /var/tmp/.netrc.json | jq -r '.password.encrypted_message')" | gpg --decrypt --batch --yes --passphrase $(get_passphrase) --local-user $(get_key_used PI_ADMIN)
# add_remote_connection tests ------------------------------------------------
test_add_remote_connection_creates_file_if_missing()
{
   typeset connection_file="/var/tmp/.netrc"

   expected="$connection_file"
   add_remote_connection "$(hostname)" "test_user" "test_password" $connection_file

   #actual="$(cat $connection_file | grep test_user)"
   actual="$(ls $connection_file)"

   assertEquals "${expected} == ${actual}" "${expected}" "${actual}"

   rm $connection_file 2> /dev/null
   unset connection_file
}


test_add_remote_connection_adds_site()
{
   typeset connection_file="/var/tmp/.netrc"

   expected="$(hostname)"
   add_remote_connection "$(hostname)" "test_user" "test_password" $connection_file

   actual="$(cat $connection_file | jq -r '.site')"

   assertEquals "${expected} == ${actual}" "${expected}" "${actual}"

   rm $connection_file 2> /dev/null
   unset connection_file
}


test_add_remote_connection_adds_user_id()
{
   typeset connection_file="/var/tmp/.netrc"

   expected="test_user"
   add_remote_connection "$(hostname)" "test_user" "test_password" $connection_file

   actual="$(cat $connection_file | jq -r '.user_id')"

   assertEquals "${expected} == ${actual}" "${expected}" "${actual}"

   rm $connection_file 2> /dev/null
   unset connection_file
}



test_add_remote_connection_adds_password()
{
   typeset connection_file="/var/tmp/.netrc"

   expected="-----BEGIN PGP MESSAGE-----"
   add_remote_connection "$(hostname)" "test_user" "test_password" $connection_file

   actual="$(cat $connection_file | jq -r '.password')"

   assertContains "${actual} CONTAINS ${expected}" "${actual}" "${expected}"

   rm $connection_file 2> /dev/null
   unset connection_file
}
   



# remote_connection_exists tests ------------------------------------------------
test_remote_connection_exists_detects_connection()
{
   typeset connection_file="/var/tmp/.netrc"

   expected="YES"
   add_remote_connection "$(hostname)" "test_user" "test_password" $connection_file

   actual="$(remote_connection_exists "$(hostname)" "test_user" $connection_file)"

   assertEquals "${expected} == ${actual}" "${expected}" "${actual}"

   rm $connection_file 2> /dev/null
   unset connection_file
}


test_remote_connection_exists_detects_missing_connection()
{
   typeset connection_file="/var/tmp/.netrc"

   expected="NO"
   add_remote_connection "$(hostname)" "test_user" "test_password" $connection_file

   actual="$(remote_connection_exists "$(hostname)" "non-existing_user" $connection_file)"

   assertEquals "${expected} == ${actual}" "${expected}" "${actual}"

   rm $connection_file 2> /dev/null
   unset connection_file
}




# update_remote_connection tests ---------------------------------------------
test_update_remote_connection_updates_connection_password()
{  
   typeset connection_file="/var/tmp/.netrc"
   
   expected="abc123"
   add_remote_connection "$(hostname)" "test_user" "test_password" $connection_file
   update_remote_connection "$(hostname)" "test_user" "${expected}" $connection_file
   
   actual="$(get_remote_connection_password "$(hostname)" "test_user" $connection_file)"
   
   assertEquals "${expected} == ${actual}" "${expected}" "${actual}"
   
   rm $connection_file 2> /dev/null
   unset connection_file
}




# get_remote_connection_password tests --------------------------------------------------
test_get_remote_connection_password_gets_password()
{
   typeset connection_file="/var/tmp/.netrc"

   expected="test_password"
   add_remote_connection "$(hostname)" "test_user" "test_password" $connection_file

   actual=$(get_remote_connection_password "$(hostname)" "test_user" $connection_file)

   assertEquals "${expected} == ${actual}" "${expected}" "${actual}"

   rm $connection_file 2> /dev/null
   unset connection_file
}


test_get_remote_connection_password_gets_first_user_if_multiple()
{
   typeset connection_file="/var/tmp/.netrc"

   expected="test_password"
   add_remote_connection "$(hostname)" "test_user" "test_password" $connection_file
   add_remote_connection "$(hostname)" "test_user2" "test_password2" $connection_file

   actual=$(get_remote_connection_password "$(hostname)" "" $connection_file)

   assertEquals "${expected} == ${actual}" "${expected}" "${actual}"

   rm $connection_file 2> /dev/null
   unset connection_file
}


test_get_remote_connection_password_gets_correct_user_if_multiple_matching_sites()
{
   typeset connection_file="/var/tmp/.netrc"

   expected="test_password2"
   add_remote_connection "$(hostname)" "test_user" "test_password" $connection_file
   add_remote_connection "$(hostname)" "test_user2" "test_password2" $connection_file

   actual=$(get_remote_connection_password "$(hostname)" "test_user2" $connection_file)

   assertEquals "${expected} == ${actual}" "${expected}" "${actual}"

   rm $connection_file 2> /dev/null
   unset connection_file
}


# get_remote_connection tests ------------------------------------------------
test_get_remote_connection_get_connection()
{
   typeset connection_file="/var/tmp/.netrc"

   expected="test_user@$(hostname)"
   add_remote_connection "$(hostname)" "test_user" "test_password" $connection_file

   actual=$(get_remote_connection "$(hostname)" "test_user" $connection_file)

   assertEquals "${expected} == ${actual}" "${expected}" "${actual}"

   #rm $connection_file 2> /dev/null
   unset connection_file
}


test_get_remote_connection_gets_first_connection_if_multiple()
{
   typeset connection_file="/var/tmp/.netrc"

   expected="test_user@$(hostname)"
   add_remote_connection "$(hostname)" "test_user" "test_password" $connection_file
   add_remote_connection "$(hostname)" "test_user2" "test_password2" $connection_file

   actual=$(get_remote_connection "$(hostname)" "" $connection_file)

   assertEquals "${expected} == ${actual}" "${expected}" "${actual}"

   #rm $connection_file 2> /dev/null
   unset connection_file
}





# get_remote_file tests ------------------------------------------------------
# Needs an account on the current host for SFTP testing. 
# Use pi_add_remote_connection.sh to add an existing user account.
test_get_remote_file_gets_remote_file()
{
   # Unable to sftp to another account on ahcdbvm1. Skip this test.
   if [ "$(hostname)" = "ahcdbvm1" ]
   then 
      return 0
   fi

   typeset results=""

   touch /var/tmp/test_file.dat_inbound # Permissions issue

   # Assumes an inbound directory
   expected="${SIMPLIPIED_INBOUND}/test_file.dat found"

   # SFTP to the remote server, get the remote file, and put it in the local 
   # inbound directory
   results=$(get_remote_file $(hostname) /var/tmp/test_file.dat_inbound ${SIMPLIPIED_INBOUND} 2> /dev/null)

   if [ -f ${SIMPLIPIED_INBOUND}/test_file.dat_inbound ]
   then
      actual="${SIMPLIPIED_INBOUND}/test_file.dat found"
   else
      actual="$results"
   fi

   assertTrue "${expected} == ${actual}" '[[ ${expected} == ${actual} ]]'

   rm ${SIMPLIPIED_INBOUND}/test_file.dat 2> /dev/null
   rm /var/tmp/test_file.dat_inbound 2> /dev/null


   unset results
}


test_get_remote_file_uses_given_account()
{
   # This test assumes multiple user accounts for the current server exist;
   # one of the being rp_migrateQA.

   # Unable to sftp to another account on ahcdbvm1. Skip this test.
   if [ "$(hostname)" = "ahcdbvm1" ]
   then
      return 0
   fi

   typeset results=""

   touch /var/tmp/test_file.dat_inbound # Permissions issue

   # Assumes an inbound directory
   expected="${SIMPLIPIED_INBOUND}/test_file.dat found"

   # SFTP to the remote server, get the remote file, and put it in the local 
   # inbound directory
   results=$(get_remote_file $(hostname) /var/tmp/test_file.dat_inbound ${SIMPLIPIED_INBOUND} rp_migrateQA 2> /dev/null)

   if [ -f ${SIMPLIPIED_INBOUND}/test_file.dat_inbound ]
   then
      # Owner will always be the local user. Can't check account used except 
      # successfully transfer.
      #actual="$(ls -al ${SIMPLIPIED_INBOUND}/test_file.dat_inbound | awk '{print $3}')" 
      actual="${SIMPLIPIED_INBOUND}/test_file.dat found"
   else
      actual="$results"
   fi

   assertTrue "${expected} == ${actual}" '[[ ${expected} == ${actual} ]]'

   rm ${SIMPLIPIED_INBOUND}/test_file.dat 2> /dev/null
   rm /var/tmp/test_file.dat_inbound 2> /dev/null


   unset results
}



# put_remote_file tests ------------------------------------------------------
# Needs an account on the current host for SFTP testing. 
# Use pi_add_remote_connection.sh to add an existing user account.
test_put_remote_file_transfers_file()
{
   # Unable to sftp to another account on ahcdbvm1. Skip this test.
   if [ "$(hostname)" = "ahcdbvm1" ]
   then 
      return 0
   fi

   typeset results=""

   touch ${SIMPLIPIED_OUTBOUND}/test_file.dat 

   # Assumes an outbound directory
   expected="Remote test_file.dat found"

   # SFTP to the remote server, get the outbound file, put it in the local
   # inbound directory
   results=$(put_remote_file $(hostname) ${SIMPLIPIED_OUTBOUND}/test_file.dat /var/tmp/test_file.dat_remote  2> /dev/null)
   if [ -f /var/tmp/test_file.dat_remote ]
   then
      actual="Remote test_file.dat found"
   else
      actual="$results"
   fi

   assertTrue "${expected} == ${actual}" '[[ ${expected} == ${actual} ]]'

   rm -f ${SIMPLIPIED_OUTBOUND}/test_file.dat 2> /dev/null
   
   results=$(remove_remote_file $(hostname) /var/tmp/test_file.dat_remote 2> /dev/null)

   unset results
}



test_put_remote_file_uses_given_account()
{  
   # This test assumes multiple user accounts for the current server exist;
   # one of the being rp_migrateQA.

   # Unable to sftp to another account on ahcdbvm1. Skip this test.
   if [ "$(hostname)" = "ahcdbvm1" ]
   then 
      return 0
   fi
   
   typeset results=""
   
   touch ${SIMPLIPIED_OUTBOUND}/test_file.dat
   
   # Assumes an outbound directory
   expected="rp_migrateQA"
   
   # SFTP to the remote server, get the outbound file, put it in the local
   # inbound directory
   results=$(put_remote_file $(hostname) ${SIMPLIPIED_OUTBOUND}/test_file.dat /var/tmp/test_file.dat_remote rp_migrateQA  2> /dev/null)
   if [ -f /var/tmp/test_file.dat_remote ]
   then
      actual="$(ls -al /var/tmp/test_file.dat_remote | awk '{print $3}')"
   else
      actual="$results"
   fi
   
   assertTrue "${expected} == ${actual}" '[[ ${expected} == ${actual} ]]'
   
   rm -f ${SIMPLIPIED_OUTBOUND}/test_file.dat 2> /dev/null
   
   results=$(remove_remote_file $(hostname) /var/tmp/test_file.dat_remote rp_migrateQA 2> /dev/null)
   
   unset results
}





# remove_remote_file tests ------------------------------------------------------
# Needs an account on the current host for SFTP testing. 
# Use pi_add_remote_connection.sh to add an existing user account.
test_remove_remote_file_deletes_remote_file()
{
   typeset results=""

   touch ${SIMPLIPIED_OUTBOUND}/test_file.dat

   # Assumes an outbound directory
   expected="Remote test_file.dat not found"

   # SFTP to the remote server, get the outbound file, put it in the local
   # inbound directory
   results=$(put_remote_file $(hostname) ${SIMPLIPIED_OUTBOUND}/test_file.dat /var/tmp/test_file.dat_remote  2> /dev/null)

   # Now remove the remote file
   results=$(remove_remote_file $(hostname) /var/tmp/test_file.dat_remote 2> /dev/null)


   if [ ! -f /var/tmp/test_file.dat_remote ]
   then
      actual="Remote test_file.dat not found"
   else
      actual="$results"
   fi

   assertTrue "${expected} == ${actual}" '[[ ${expected} == ${actual} ]]'

   rm -f ${SIMPLIPIED_OUTBOUND}/test_file.dat 2> /dev/null

   unset results
}


test_remove_remote_uses_given_account()
{
   # This test assumes multiple user accounts for the current server exist;
   # one of the being rp_migrateQA.
   typeset results=""

   touch ${SIMPLIPIED_OUTBOUND}/test_file.dat

   # Assumes an outbound directory
   expected="Remote test_file.dat not found"

   # SFTP to the remote server, get the outbound file, put it in the local
   # inbound directory
   results=$(put_remote_file $(hostname) ${SIMPLIPIED_OUTBOUND}/test_file.dat /var/tmp/test_file.dat_remote rp_migrateQA 2> /dev/null)

   # Now remove the remote file
   results=$(remove_remote_file $(hostname) /var/tmp/test_file.dat_remote rp_migrateQA 2> /dev/null)


   if [ ! -f /var/tmp/test_file.dat_remote ]
   then
      actual="Remote test_file.dat not found"
   else
      actual="$results"
   fi

   assertTrue "${expected} == ${actual}" '[[ ${expected} == ${actual} ]]'

   rm -f ${SIMPLIPIED_OUTBOUND}/test_file.dat 2> /dev/null

   unset results
}




# Encryption test assume a $HOME/.gnupg directory ----------------------------

# get_passphrase tests -------------------------------------------------------
test_get_passphrase_gets_passphrase_for_default_environment()
{
   typeset -u local_host=$(hostname | awk -F. '{print $1}')

   expected="$local_host"
   actual="$(get_passphrase)"
   assertTrue "${expected} == ${actual}" '[[ ${expected} == ${actual} ]]'
}


# get_recipient tests ---------------------------------------------------------
test_get_recipient_gets_email_from_config_file()
{
   expected="<EMAIL>"
   echo $expected > $HOME/.gnupg/.recipient_TEST

   actual="$(get_recipient Test)"

   assertTrue "${expected} == ${actual}" '[[ ${expected} == ${actual} ]]'

   rm $HOME/.gnupg/.recipient_TEST 2> /dev/null
}


test_get_recipient_returns_error_if_no_config()
{
   expected="ERROR: Unable to read recipient config file"
   actual="$(get_recipient Test)"
   assertTrue "${expected} == ${actual}" '[[ ${expected} == ${actual} ]]'
}



# update_recipient tests ---------------------------------------------------------
test_update_recipient_creates_config_file_if_missing()
{
   expected="<EMAIL>"
   update_<NAME_EMAIL>

   actual="$(cat  ~/.gnupg/.recipient_TEST)"
   rm ~/.gnupg/.recipient_TEST 2> /dev/null

   assertTrue "${expected} == ${actual}" '[[ ${expected} == ${actual} ]]'
}



# update_key_used tests ------------------------------------------------------
test_update_key_used_creates_config_file_if_missing()
{
   expected="<EMAIL>"
   update_key_<NAME_EMAIL>

   actual="$(cat  ~/.gnupg/.key_used_TEST)"
   rm ~/.gnupg/.key_used_TEST 2> /dev/null

   assertTrue "${expected} == ${actual}" '[[ ${expected} == ${actual} ]]'
}



# get_key_used tests ---------------------------------------------------------
test_get_key_used_gets_email_from_config_file()
{
   expected="<EMAIL>"
   update_key_<NAME_EMAIL>

   actual="$(get_key_used Test)"
   rm ~/.gnupg/.key_used_TEST 2> /dev/null

   assertTrue "${expected} == ${actual}" '[[ ${expected} == ${actual} ]]'
}

test_get_key_used_returns_error_if_no_config()
{
   expected="ERROR: Unable to read key config file"
   actual="$(get_key_used Test)"
   assertTrue "${expected} == ${actual}" '[[ ${expected} == ${actual} ]]'
}



# encrypt_file tests ---------------------------------------------------------
test_encrypt_file_calls_gpg()
{
   # Local stub so it doesn't affect other gpg-based tests
   gpg()
   {
      echo "gpg: $@"
   }

   touch /var/tmp/test_unencrypted_file.txt
   update_<NAME_EMAIL>
   update_key_<NAME_EMAIL>

   expected="gpg: --recipient <EMAIL> --armor --batch --passphrase $(get_passphrase) --output /var/tmp/test_unencrypted_file.txt.asc --sign --local-user <EMAIL> --pinentry-mode loopback --encrypt /var/tmp/test_unencrypted_file.txt"
   actual=$(encrypt_file test /var/tmp/test_unencrypted_file.txt /var/tmp/test_unencrypted_file.txt.asc)
   assertTrue "${expected} == ${actual}" '[[ ${expected} == ${actual} ]]'

   unset -f gpg
   rm $HOME/.gnupg/.key_used_TEST 2> /dev/null
   rm $HOME/.gnupg/.recipient_TEST 2> /dev/null
   rm /var/tmp/test_unencrypted_file.txt 2> /dev/null
}


test_encrypt_file_creates_default_encrypted_file()
{
   # Local stub so it doesn't affect other gpg-based tests
   gpg()
   {
      echo "gpg: $@"
   }

   touch /var/tmp/test_unencrypted_file.txt
   update_<NAME_EMAIL>
   update_key_<NAME_EMAIL>

   expected="gpg: --recipient <EMAIL> --armor --batch --passphrase $(get_passphrase) --output /var/tmp/test_unencrypted_file.txt.asc --sign --local-user <EMAIL> --pinentry-mode loopback --encrypt /var/tmp/test_unencrypted_file.txt"
   actual=$(encrypt_file test /var/tmp/test_unencrypted_file.txt)
   assertTrue "${expected} == ${actual}" '[[ ${expected} == ${actual} ]]'

   unset -f gpg
   rm $HOME/.gnupg/.key_used_TEST 2> /dev/null
   rm $HOME/.gnupg/.recipient_TEST 2> /dev/null
   rm /var/tmp/test_unencrypted_file.txt 2> /dev/null
}


test_encrypt_file_encrypts_a_file()
{
   typeset unencrypted_file=/var/tmp/test_unencrypted_file.txt

   expected="Unencrypted test"

   echo $expected > $unencrypted_file
   update_<NAME_EMAIL>
   update_key_<NAME_EMAIL>

   encrypt_file TEST $unencrypted_file 2> /dev/null

   actual=$(cat ${unencrypted_file}.asc)
   assertNotContains "${actual} DOES NOT CONTAIN ${expected}" "${actual}" "${expected}"

   rm ${unencrypted_file}* 2> /dev/null
   unset unencrypted_file
}


# decrypt_file tests ---------------------------------------------------------
test_decrypt_file_calls_gpg()
{
   # Local stub so it doesn't affect other gpg-based tests
   gpg()
   {
      echo "gpg: $@"
   }

   update_key_<NAME_EMAIL>

   expected="gpg: --batch --passphrase $(get_passphrase) --no-verbose --quiet --local-user <EMAIL> --output test.txt --pinentry-mode loopback --decrypt test.asc"
   actual="$(decrypt_file Test test.asc test.txt)"
   assertTrue "${expected} == ${actual}" '[[ ${expected} == ${actual} ]]'

   unset -f gpg
   rm $HOME/.gnupg/.key_used_TEST 2> /dev/null
}


test_decrypt_file_creates_default_decrypted_file()
{
   # Local stub so it doesn't affect other gpg-based tests
   gpg()
   {
      echo "gpg: $@"
   }

   update_key_<NAME_EMAIL>

   expected="gpg: --batch --passphrase $(get_passphrase) --no-verbose --quiet --local-user <EMAIL> --output test.txt --pinentry-mode loopback --decrypt test.txt.asc"
   actual="$(decrypt_file Test test.txt.asc)"
   assertTrue "${expected} == ${actual}" '[[ ${expected} == ${actual} ]]'

   unset -f gpg
   rm $HOME/.gnupg/.key_used_TEST 2> /dev/null
}


test_decrypt_file_decrypts_a_file()
{
   typeset unencrypted_file=/var/tmp/test_unencrypted_file.txt
   typeset decrypted_file=/var/tmp/test_decrypted_file.txt

   expected="Unencrypted test"

   echo $expected > $unencrypted_file
   update_<NAME_EMAIL>
   update_key_<NAME_EMAIL>

   encrypt_file TEST $unencrypted_file 2> /dev/null
   decrypt_file TEST ${unencrypted_file}.asc $decrypted_file 2> /dev/null

   actual=$(cat ${decrypted_file})
   assertContains "${actual} CONTAINS ${expected}" "${actual}" "${expected}"

   rm ${unencrypted_file}* 2> /dev/null
   rm ${decrypted_file}* 2> /dev/null
   unset unencrypted_file
   unset decrypted_file
}




# generate_gpg_key tests -----------------------------------------------------

#   generate_gpg_key "Dummy User" <EMAIL> "Dummy comment" 1d
test_generate_gpg_key_uses_name()
{
   expected="Dummy User"
   actual=$(gpg --list-keys <EMAIL> 2>&1 /dev/null)
   assertContains "${actual} CONTAINS ${expected}" "${actual}" "${expected}"
}


test_generate_gpg_key_returns_error_if_no_name_given()
{
   typeset results=""

   expected="ERROR: Name must be provided for the key"
   results=$(generate_gpg_key)
   actual=$(echo $results | grep "$expected")
   assertContains "${actual} CONTAINS ${expected}" "${actual}" "${expected}"

   unset results
}


test_generate_gpg_key_uses_email()
{
   expected="<EMAIL>"
   actual=$(gpg --list-keys <EMAIL> 2>&1 /dev/null)
   assertContains "${actual} CONTAINS ${expected}" "${actual}" "${expected}"
}


test_generate_gpg_key_returns_error_if_no_email_given()
{
   typeset results=$(generate_gpg_key "Test User")

   expected="ERROR: Email must be provided for the key"
   actual=$(echo $results | grep "$expected")
   assertContains "${actual} CONTAINS ${expected}" "${actual}" "${expected}"

   unset results
}


test_generate_gpg_key_uses_comment()
{
   expected="Dummy comment"
   actual=$(gpg --list-keys <EMAIL> 2>&1 /dev/null)
   assertContains "${actual} CONTAINS ${expected}" "${actual}" "${expected}"
}


test_generate_gpg_key_uses_given_expire_date()
{
   typeset fingerprint=$(get_gpg_fingerprint <EMAIL> 2>&1 /dev/null)
  
   expected="expires: $(date -d "+1 days" +%Y-%m-%d)" 
   actual=$(gpg --list-signatures $fingerprint 2>&1 /dev/null)
   assertContains "${actual} CONTAINS ${expected}" "${actual}" "${expected}"

   unset fingerprint
}


test_generate_gpg_key_uses_never_expire_date()
{
   typeset results=$(generate_gpg_key "Test User" "<EMAIL>" "This is a test user" Never 2> /dev/null)
   typeset fingerprint=$(get_gpg_fingerprint "<EMAIL>" 2> /dev/null)

   expected="\[expires:"
   actual=$(gpg --list-keys $fingerprint 2> /dev/null)
   assertNotContains "${actual} CONTAINS ${expected}" "${actual}" "${expected}"

   rm $HOME/.gnupg/openpgp-revocs.d/${fingerprint}.rev
   results=$(delete_gpg_key "<EMAIL>" 2>&1 /dev/null)
   unset results
   unset fingerprint
}


test_generate_gpg_key_defaults_expire_date_to_one_year()
{
   typeset results=$(generate_gpg_key "Test User" "<EMAIL>" "This is a test user" "" 2> /dev/null)
   typeset fingerprint=$(get_gpg_fingerprint "<EMAIL>" 2> /dev/null)

   expected="expires: $(date -d "+1 years" +%Y-%m-%d)" 
   actual=$(gpg --list-keys $fingerprint 2> /dev/null)
   assertContains "${actual} CONTAINS ${expected}" "${actual}" "${expected}"

   rm $HOME/.gnupg/openpgp-revocs.d/${fingerprint}.rev
   results=$(delete_gpg_key "<EMAIL>" 2>&1 /dev/null)
   unset results
   unset fingerprint
}


test_generate_gpg_key_create_new_gpg_key()
{
   typeset results=$(generate_gpg_key "Test User" "<EMAIL>" "This is a test user" 1d 2> /dev/null)
   typeset fingerprint=$(get_gpg_fingerprint "<EMAIL>" 2> /dev/null)

   expected="<EMAIL>"
   actual=$(gpg --list-keys $fingerprint 2>&1 /dev/null)
   assertContains "${actual} CONTAINS ${expected}" "${actual}" "${expected}"


   rm $HOME/.gnupg/openpgp-revocs.d/${fingerprint}.rev
   results=$(delete_gpg_key "<EMAIL>" 2> /dev/null)
   unset results
   unset fingerprint
}


# get_gpg_fingerprint tests --------------------------------------------------
test_get_gpg_fingerprint_returns_fingerprint()
{
   typeset results=$(generate_gpg_key "Test User" "<EMAIL>" "This is a test user" 2d 2>&1 /dev/null)

   expected="$(gpg --list-secret-keys --with-colons --fingerprint "<EMAIL>" 2>&1 /dev/null | grep fpr | awk -F: '{print $10}')"
   actual=$(get_gpg_fingerprint "<EMAIL>" 2>&1 /dev/null)
   assertContains "${actual} CONTAINS ${expected}" "${actual}" "${expected}"

   rm $HOME/.gnupg/openpgp-revocs.d/${actual}.rev
   results=$(delete_gpg_key "<EMAIL>" 2>&1 /dev/null)
   unset results
}


# get_gpg_key_expiration tests -----------------------------------------------
test_get_gpg_key_expiration_gets_expiration_date()
{
   typeset results=$(generate_gpg_key "Test User" "<EMAIL>" "This is a test user" 2d 2>&1 /dev/null)
   typeset fingerprint=$(get_gpg_fingerprint "<EMAIL>" 2> /dev/null)

   expected="$(date +%Y-%m-%d -d "+2 days")" # Use date to get the test value
   actual=$(get_gpg_key_expiration $fingerprint 2> /dev/null)
   assertTrue "${expected} == ${actual}" '[[ ${expected} == ${actual} ]]'

   rm $HOME/.gnupg/openpgp-revocs.d/${fingerprint}.rev
   results=$(delete_gpg_key "<EMAIL>" 2>&1 /dev/null)

   unset results
   unset fingerprint
}


# delete_gpg_key tests -------------------------------------------------------
test_delete_gpg_key_deletes_key()
{
   typeset results=$(generate_gpg_key "Test User" "<EMAIL>" "This is a test user" 2y 2> /dev/null)
   typeset fingerprint=$(get_gpg_fingerprint "<EMAIL>" 2> /dev/null)

   expected="<EMAIL>"
   results=$(delete_gpg_key "<EMAIL>" 2> /dev/null)
   actual=$(gpg --list-keys 2> /dev/null)
   assertNotContains "${actual} DOES NOT CONTAIN ${expected}" "${actual}" "${expected}"

   rm $HOME/.gnupg/openpgp-revocs.d/${fingerprint}.rev
   results=$(delete_gpg_key "<EMAIL>" 2> /dev/null)
   unset results
   unset fingerprint
}



# sign_gpg_file tests -------------------------------------------------------
test_sign_gpg_file_uses_given_key_to_sign()
{

   typeset file_to_sign="/var/tmp/file_to_sign.txt"
   typeset temporary_decrypt_file="/var/tmp/temp_decrypt.txt"

   touch $file_to_sign

   expected="Dummy User (Dummy comment) <<EMAIL>>"
   
   results=$(sign_gpg_file "<EMAIL>" $file_to_sign)

   #Use "gpg --decrypt" to the the "Good signature" output`
   gpg --passphrase $(get_passphrase) --decrypt ${file_to_sign}.gpg 2> $temporary_decrypt_file

   actual=$(cat $temporary_decrypt_file | grep "Good signature" 2> /dev/null)

   assertContains "${actual} CONTAINs ${expected}" "${actual}" "${expected}"

   rm ${file_to_sign}.gpg 2> /dev/null
   rm ${file_to_sign} 2> /dev/null
   rm ${temporary_decrypt_file} 2> /dev/null
   unset file_to_sign
   unset temporary_decrypt_file
}



# export_public_key tests ---------------------------------------------------
test_export_public_key_exports_file()
{

   expected="temp_external_pub.gpg exists"

   # Moved to OneTimeSetup
   #results=$(export_public_key "<EMAIL>" /var/tmp/temp_external_pub.gpg)

   if [ -f /var/tmp/temp_external_pub.gpg ]
   then 
      actual="temp_external_pub.gpg exists"
   else
      actual="$results"
   fi

   assertTrue "${expected} == ${actual}" '[[ ${expected} == ${actual} ]]'

}



# export_private_key tests --------------------------------------------------
test_export_private_key_exports_file()
{

   expected="temp_external_private.gpg exists"

   results=$(export_private_key "<EMAIL>" /var/tmp/temp_external_private.gpg)

   if [ -f /var/tmp/temp_external_private.gpg ]
   then
      actual="temp_external_private.gpg exists"
   else
      actual="$results"
   fi

   assertTrue "${expected} == ${actual}" '[[ ${expected} == ${actual} ]]'

   rm /var/tmp/temp_external_private.gpg 2> /dev/null

}



# import_public_key tests ---------------------------------------------------
test_import_public_key_imports_file()
{
   expected="<EMAIL>"

   # Public file creation moved to oneTimeSetUp()

   # Import the key file
   import_public_key /var/tmp/temp_external_pub.gpg 2> /dev/null

   # Use --list-keys to get the actual
   actual=$(gpg --list-keys <EMAIL> 2> /dev/null)

   assertContains "${actual} CONTAINs ${expected}" "${actual}" "${expected}"

   delete_gpg_key $(get_gpg_fingerprint <EMAIL>) 2> /dev/null

}



# trust_gpg_key tests -------------------------------------------------------
test_trust_gpg_key_trusts_key()
{
   typeset results
   expected="[  full  ] External User"

   # Import a public key file (Created in the oneTimeSetUp()
   import_public_key /var/tmp/temp_external_pub.gpg 2> /dev/null

   results="$(trust_gpg_key <EMAIL> "" 2>&1 /dev/null)"

   actual=$(gpg --list-keys <EMAIL> 2> /dev/null)
   assertContains "${actual} CONTAINS ${expected}" "${actual}" "${expected}"

   delete_gpg_key <EMAIL> 2> /dev/null
   unset results
}




test_trust_gpg_key_uses_given_trusted_by_key()
{
   typeset results
   expected="<EMAIL>"

   # Import a public key file (Created in the oneTimeSetUp()
   import_public_key /var/tmp/temp_external_pub.gpg 2> /dev/null

   results="$(trust_gpg_key <EMAIL> <EMAIL> 2>&1 /dev/null)"

   actual=$(echo $results 2> /dev/null)
   assertContains "${actual} CONTAINS ${expected}" "${actual}" "${expected}"

   delete_gpg_key <EMAIL> 2> /dev/null
   unset results
}



# get_email_addresses tests --------------------------------------------------
test_get_email_addresses_gets_interface_to_addresses()
{
   # Records added in oneTimeSetUp()
   expected="<EMAIL>,<EMAIL>"
   actual=$(get_email_addresses test_interface to)
   assertTrue "${expected} == ${actual}" '[[ ${expected} == ${actual} ]]'
}


test_get_email_addresses_gets_interface_cc_addresses()
{
   # Records added in oneTimeSetUp()
   expected="<EMAIL>"
   actual=$(get_email_addresses test_interface CC)
   assertTrue "${expected} == ${actual}" '[[ ${expected} == ${actual} ]]'
}


test_get_email_addresses_gets_interface_bcc_addresses()
{
   # Records added in oneTimeSetUp()
   expected="<EMAIL>,<EMAIL>"
   actual=$(get_email_addresses test_interface bcc)
   assertTrue "${expected} == ${actual}" '[[ ${expected} == ${actual} ]]'
}


test_get_email_addresses_defaults_to_all_addresses()
{
   # Records added in oneTimeSetUp()
   expected="<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>"
   actual=$(get_email_addresses test_interface)
   assertTrue "${expected} == ${actual}" '[[ ${expected} == ${actual} ]]'
}





# get_aop_directory tests ----------------------------------------------------
test_get_aop_directory_gets_development_directory()
{
   expected="/opt/aopdocs"
   actual=$(get_aop_directory apex-app2)
   assertTrue "${expected} == ${actual}" '[[ ${expected} == ${actual} ]]'
}


test_get_aop_directory_gets_production_directory()
{
   expected="/u01/aopdir"
   actual=$(get_aop_directory ahcdbvm1)
   assertTrue "${expected} == ${actual}" '[[ ${expected} == ${actual} ]]'
}


test_get_aop_directory_gets_directory_for_default_host()
{
   if [ "$(hostname)" = "ahcdbvm1" ]
   then
      expected="/u01/aopdir"
   else
      expected="/opt/aopdocs"
   fi

   actual=$(get_aop_directory)
   assertTrue "${expected} == ${actual}" '[[ ${expected} == ${actual} ]]'
}



test_encrypt_encrypts_string()
{
   expected="Unencrypted_text_string"
   actual=$(encrypt $expected TEST)

   assertTrue "${expected} != ${actual}" '[[ ${expected} != ${actual} ]]'
}


test_encrypt_uses_default_key()
{  
   # The default key is PI_ADMIN
   expected="Unencrypted_text_string"
   actual=$(decrypt "$(encrypt $expected)" PI_ADMIN)
   
   assertTrue "${expected} == ${actual}" '[[ ${expected} == ${actual} ]]'
}



# cat secret.json
# {"encrypted_message": "-----BEGIN PGP MESSAGE-----\n\njA0EBwMC2S+AZC9vuRP70kcB+okWfXOQciQV0ZA5sG4og3gzGCV4HgVA/FGPvLSv\ndZ0zPUdSh4CVSEzTOisxH8cZYMFMqSy/D4pqR4s1F2FAC5l/yicX4A==\n=KvDt\n-----END PGP MESSAGE-----\n"}
test_encrypt_string_encrypts_string()
{  
   expected="BEGIN PGP MESSAGE"
   actual=$(encrypt_string "encrypted_message")
   
   assertContains "${actual} CONTAINS ${expected}" "${actual}" "${expected}"
}


test_decrypt_string_decrypts_encrypted_string()
{
   typeset encrypted_text

   expected="Unencrypted_text_string"

   encrypted_text=$(encrypt_string $expected)

   actual=$(decrypt_string "$encrypted_text")
   assertTrue "${expected} == ${actual}" '[[ ${expected} == ${actual} ]]'

   unset encrypted_text
}


test_decrypt_string_can_override_key()
{
   typeset encrypted_text

   expected="Unencrypted_text_string"

   encrypted_text=$(encrypt_string $expected test)

   actual=$(decrypt_string "$encrypted_text" Test)
   assertTrue "${expected} == ${actual}" '[[ ${expected} == ${actual} ]]'

   unset encrypted_text
}



test_decrypt_decrypts_encrypted_string()
{
   typeset encrypted_text

   expected="Unencrypted_text_string"
   encrypted_text=$(encrypt $expected test)
   actual=$(decrypt "$encrypted_text" TEST)

   assertTrue "${expected} == ${actual}" '[[ ${expected} == ${actual} ]]'

   unset encrypted_text
}


test_decrypt_uses_default_key()
{
   # The default key is PI_ADMIN
   typeset encrypted_text

   expected="Unencrypted_text_string"
   encrypted_text=$(encrypt $expected PI_ADMIN)

   actual=$(decrypt "$encrypted_text")

   assertTrue "${expected} == ${actual}" '[[ ${expected} == ${actual} ]]'

   unset encrypted_text
}



test_add_db_connection_creates_connect_file_if_missing()
{
   typeset connect_file="/var/tmp/test_connect1.txt"
   expected="File exists"

   rm $connect_file 2> /dev/null
   
   add_db_connection "test_id" "abc123" "testdb" $connect_file

   if [ -r $connect_file ]
   then
      actual="File exists"
   else
      actual="File does not exist"
   fi

   assertTrue "${expected} == ${actual}" '[[ ${expected} == ${actual} ]]'

   rm $connect_file 2> /dev/null
   unset connect_file
}


test_add_db_connection_encrypts_password()
{
   typeset connect_file="/var/tmp/test_connect2.txt"
   expected="unencrypted_password"

   rm $connect_file 2> /dev/null

   add_db_connection "test_id" "$expected" "testdb" $connect_file

   # Use get_db_password to retrieve and decrypt the password
   actual=$(get_db_password test_id testdb $connect_file)

   assertEquals "${expected} == ${actual}" "${expected}" "${actual}"

   rm $connect_file 2> /dev/null
   unset connect_file
}


test_add_db_connection_updates_existing_connection()
{
   typeset connect_file="/var/tmp/test_connect3.txt"
   expected="klm101112"

   rm $connect_file 2> /dev/null

   add_db_connection "test_id1" "abc123" "testdb" $connect_file
   add_db_connection "test_id2" "def456" "testdb" $connect_file
   add_db_connection "test_id3" "hij789" "testdb" $connect_file

   add_db_connection "test_id2" "$expected" "testdb" $connect_file


   actual=$(get_db_password "test_id2" "testdb" $connect_file)
   assertEquals "${expected} == ${actual}" "${expected}" "${actual}"

   rm $connect_file 2> /dev/null
   unset connect_file
}


test_update_db_connection_updates_existing_connection_password()
{
   typeset connect_file="/var/tmp/test_connect4.txt"
   expected="klm101112"

   rm $connect_file 2> /dev/null

   add_db_connection "test_id1" "abc123" "testdb" $connect_file
   add_db_connection "test_id2" "def456" "testdb" $connect_file
   add_db_connection "test_id3" "hij789" "testdb" $connect_file

   update_db_connection "test_id2" "$expected" "testdb" $connect_file

   
   actual=$(get_db_password "test_id2" "testdb" $connect_file)
   assertEquals "${expected} == ${actual}" "${expected}" "${actual}"

   rm $connect_file 2> /dev/null
   unset connect_file
}


test_get_db_password_gets_and_decrypts_password()
{
   typeset connect_file="/var/tmp/test_connect5.txt"
   expected="abc123"

   rm $connect_file 2> /dev/null

   add_db_connection "test_id" "abc123" "testdb" $connect_file
   actual=$(get_db_password "test_id" "testdb" $connect_file)

   assertTrue "${expected} == ${actual}" '[[ ${expected} == ${actual} ]]'

   rm $connect_file 2> /dev/null
   unset connect_file
}


test_get_db_password_gets_correct_user()
{
   typeset connect_file="/var/tmp/test_connect6.txt"
   expected="def456"

   rm $connect_file 2> /dev/null

   add_db_connection "test_id1" "abc123" "testdb" $connect_file
   add_db_connection "test_id2" "def456" "testdb" $connect_file
   add_db_connection "test_id3" "hij789" "testdb" $connect_file

   actual=$(get_db_password "test_id2" "testdb" $connect_file)

   assertTrue "${expected} == ${actual}" '[[ ${expected} == ${actual} ]]'

   rm $connect_file 2> /dev/null
   unset connect_file
}



test_get_db_password_gets_correct_database()
{
   typeset connect_file="/var/tmp/test_connect7.txt"
   expected="hij789"

   rm $connect_file 2> /dev/null

   add_db_connection "test_id" "abc123" "testdb1" $connect_file
   add_db_connection "test_id" "def456" "testdb2" $connect_file
   add_db_connection "test_id" "hij789" "testdb3" $connect_file

   actual=$(get_db_password "test_id" "testdb3" $connect_file)

   assertTrue "${expected} == ${actual}" '[[ ${expected} == ${actual} ]]'

   rm $connect_file 2> /dev/null
   unset connect_file
}


test_db_connection_exists_detects_existing_record()
{
   typeset connect_file="/var/tmp/test_connect8.txt"
   expected="YES"

   rm $connect_file 2> /dev/null

   add_db_connection "test_id" "abc123" "testdb1" $connect_file
   add_db_connection "test_id" "def456" "testdb2" $connect_file
   add_db_connection "test_id" "hij789" "testdb3" $connect_file

   actual=$(db_connection_exists "test_id" "testdb2" $connect_file)

   #assertTrue "${expected} == ${actual}" '[[ ${expected} == ${actual} ]]'
   assertEquals "${expected} == ${actual}" "${expected}" "${actual}"

   rm $connect_file 2> /dev/null
   unset connect_file
}


test_db_connection_exists_detects_missing_record()
{
   typeset connect_file="/var/tmp/test_connect9.txt"
   expected="NO"

   rm $connect_file 2> /dev/null

   add_db_connection "test_id1" "abc123" "testdb1" $connect_file
   add_db_connection "test_id2" "def456" "testdb2" $connect_file
   add_db_connection "test_id3" "hij789" "testdb3" $connect_file

   actual=$(db_connection_exists "test_id1" "testdb2" $connect_file)

   #assertTrue "${expected} == ${actual}" '[[ ${expected} == ${actual} ]]'
   assertEquals "${expected} == ${actual}" "${expected}" "${actual}"

   rm $connect_file 2> /dev/null
   unset connect_file
}




# Source shunit2 (run the tests) ---------------------------------------------
. shunit2


# Complete
