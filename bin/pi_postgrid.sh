#!/bin/bash
#-----------------------------------------------------------------------------
#
#   PROCEDURE:    pi_postgrid.sh
#
#   PURPOSE:      This script contains the PostGrid functions.
#
#   PARAMETERS:   None.
#
#   USAGE:        From the command-line, type:
#                 . ./pi_postgrid.sh
#
#   AUTHOR:       <PERSON>
#
#
# Copyright (c) 2025 Kevin <PERSON>
# All rights reserved.
#
# This software is proprietary and confidential. Unauthorized copying of this
# file, via any medium, is strictly prohibited without the express permission
# of the author or owning organization.
#
#-----------------------------------------------------------------------------

# Mark the common functions as defined
# To force this script to re-execute, use:
#    unset PI_POSTGRID_FUNCTIONS
if [ "${PI_POSTGRID_FUNCTIONS:=Undefined}" = "Defined" ]
then
   echo "The PostGrid functions are already defined."
else
   PI_POSTGRID_FUNCTIONS="Defined"
   export PI_POSTGRID_FUNCTIONS
fi

# PostGrid Support -----------------------------------------------------------
function get_aop_letter_number
{
   typeset file_name=${1%.pdf}

   echo $(run_sql_func "letters.get_letter_number_from_filename('$file_name')")

   unset file_name

} # End - get_aop_letter_number


function get_postgrid_api_key
{
   typeset -u transfer_mode=${1}

   if [ "$transfer_mode" = "" ]
   then
      transfer_mode=TEST
   fi

   echo $(run_sql_func "letters.get_postgrid_api_key('$transfer_mode')")

   unset transfer_mode

} # End - get_postgrid_api_key




function create_postgrid_contact
{
   # Build and execute the PostGrid curl command to create a contact
   #
   # Example:
   # curl --location 'https://api.postgrid.com/print-mail/v1/contacts' \
   #    --header "Content-Type: application/x-www-form-urlencoded" \
   #    --header 'x-api-key: test_sk_aEDYn7sZmXgGqvAa6KqXA2' \
   #    --data-urlencode 'firstName=REFUND UNIT' 
   #    --data-urlencode 'companyName=BEVERLY RADIOLOGY MEDICAL GROUP' 
   #    --data-urlencode 'addressLine1=PO BOX 101418' 
   #    --data-urlencode 'city=PASADENA' 
   #    --data-urlencode 'provinceOrState=CA' 
   #    --data-urlencode 'postalOrZip=*********' 
   #    --data-urlencode 'countryCode=US'

   typeset letter_number=${1}
   typeset -u transfer_mode=${2} # Used for creating Test or Production contacts
   typeset contact_data=$(run_sql_func "letters.get_postgrid_to_contact('$letter_number')")
   typeset api_key=$(get_postgrid_api_key ${transfer_mode})
   typeset temp_curl_file="/var/tmp/temp_create_contact_${letter_number}.sh"
   typeset temp_curl_log="/var/tmp/temp_create_contact_${letter_number}.log"
   

   # Execute the curl command
   # (The only way I got this to work was to write the command to a file and
   # then execute the file.)
   echo "curl --location 'https://api.postgrid.com/print-mail/v1/contacts' --header \"Content-Type: application/x-www-form-urlencoded\" --header 'x-api-key: ${api_key}' ${contact_data} " > $temp_curl_file

   chmod +x $temp_curl_file
   $temp_curl_file > $temp_curl_log
   
   contact_id=$(jq '.id' $temp_curl_log | tr -d \")

   echo $contact_id

   # Clean up
   rm $temp_curl_file 2> /dev/null
   rm $temp_curl_log 2> /dev/null

   unset letter_number
   unset transfer_mode
   unset contact_data
   unset api_key
   unset temp_curl_file
   unset temp_curl_log

} # End - create_postgrid_contact


function create_postgrid_from_contact
{
   # Build and execute the PostGrid curl command to create a contact
   #
   # Example:
   # curl --location 'https://api.postgrid.com/print-mail/v1/contacts' \
   #    --header "Content-Type: application/x-www-form-urlencoded" \
   #    --header 'x-api-key: test_sk_aEDYn7sZmXgGqvAa6KqXA2' \
   #    --data-urlencode 'firstName=REFUND UNIT'
   #    --data-urlencode 'companyName=BEVERLY RADIOLOGY MEDICAL GROUP'
   #    --data-urlencode 'addressLine1=PO BOX 101418'
   #    --data-urlencode 'city=PASADENA'
   #    --data-urlencode 'provinceOrState=CA'
   #    --data-urlencode 'postalOrZip=*********'
   #    --data-urlencode 'countryCode=US'

   typeset letter_number=${1}
   typeset -u transfer_mode=${2} # Used for creating Test or Production contacts
   typeset contact_data=$(run_sql_func "letters.get_postgrid_from_contact('ALIGNMENT HEALTH PLAN')")
   typeset api_key=$(get_postgrid_api_key ${transfer_mode})
   typeset temp_curl_file="/var/tmp/temp_create_contact_alignment_health_plan.sh"
   typeset temp_curl_log="/var/tmp/temp_create_contact_alignment_health_plan.log"


   # Execute the curl command
   # (The only way I got this to work was to write the command to a file and
   # then execute the file.)
   echo "curl --location 'https://api.postgrid.com/print-mail/v1/contacts' --header \"Content-Type: application/x-www-form-urlencoded\" --header 'x-api-key: ${api_key}' ${contact_data} " > $temp_curl_file

   chmod +x $temp_curl_file
   $temp_curl_file > $temp_curl_log

   contact_id=$(jq '.id' $temp_curl_log | tr -d \")

   echo $contact_id

   # Clean up
   rm $temp_curl_file 2> /dev/null
   rm $temp_curl_log 2> /dev/null

   unset letter_number
   unset transfer_mode
   unset contact_data
   unset api_key
   unset temp_curl_file
   unset temp_curl_log

} # End - create_postgrid_from_contact



function delete_postgrid_contact
{
   # Build and execute the PostGrid curl command to create a contact
   #
   # Example:
   # curl --request DELETE --url https://api.postgrid.com/print-mail/v1/contacts/contact_qU4vBoSChKNdR1mAjjMNre --header 'accept: application/json' --header 'x-api-key: test_sk_aEDYn7sZmXgGqvAa6KqXA2'

   typeset contact_id=${1}
   typeset -u transfer_mode=${2} # Used for creating Test or Production contacts
   typeset api_key=$(get_postgrid_api_key ${transfer_mode})
   typeset temp_curl_file="/var/tmp/temp_delete_contact_${contact_id}.sh"
   typeset temp_curl_log="/var/tmp/temp_delete_contact_${contact_id}.log"

   # Execute the curl command
   echo "curl --request DELETE --url https://api.postgrid.com/print-mail/v1/contacts/${contact_id} --header 'accept: application/json' --header 'x-api-key: $api_key' " > $temp_curl_file

   chmod +x $temp_curl_file
   $temp_curl_file > $temp_curl_log

   cat $temp_curl_log # Parse and clean-up later

   # Clean up
   rm $temp_curl_file 2> /dev/null
   rm $temp_curl_log 2> /dev/null

   unset contact_id
   unset transfer_mode
   unset api_key

} # End - delete_postgrid_contact


function create_postgrid_letter
{
   # Build and execute the PostGrid curl command to create a contact
   #
   # Example:
   # curl --location 'https://api.postgrid.com/print-mail/v1/letters' \
   #   --header 'x-api-key: test_sk_aEDYn7sZmXgGqvAa6KqXA2' \
   #   --form to="contact_th6gti46bsWkTmk3n9efz6" \
   #   --form from="contact_1b1gh33j5QSUf8h11YmcCQ" \
   #   --form addressPlacement=insert_blank_page \
   #   --form pdf=@"/opt/aopdocs/CCHP_951684089_SCRIPPSMER_20241028.pdf"


   typeset to_contact=${1}
   typeset from_contact=${2}
   typeset letter_file=${3}
   typeset -u transfer_mode=${4} # Used for creating Test or Production contacts
   typeset api_key=$(get_postgrid_api_key ${transfer_mode})
   typeset temp_curl_file="/var/tmp/temp_create_postgrid_letter.sh"
   typeset temp_curl_log="/var/tmp/temp_create_postgrid_letter.log"
   typeset letter_id


   # Execute the curl command
   echo "curl --location 'https://api.postgrid.com/print-mail/v1/letters' --header 'x-api-key: ${api_key}' --form to=\"${to_contact}\"  --form from=\"${from_contact}\" --form forceVerifiedStatus=true --form addressPlacement=insert_blank_page --form pdf=@\"${letter_file}\" " > $temp_curl_file

   chmod +x $temp_curl_file
   $temp_curl_file > $temp_curl_log

   letter_id=$(jq '.id' $temp_curl_log | tr -d \")

   echo $letter_id

   # Clean up
   rm $temp_curl_file 2> /dev/null
   rm $temp_curl_log 2> /dev/null

   unset to_contact
   unset from_contact
   unset letter_file
   unset transfer_mode
   unset contact_data
   unset api_key
   unset temp_curl_file
   unset temp_curl_log
   unset letter_id

} # End - create_postgrid_letter


# End - pi_postgrid.sh

