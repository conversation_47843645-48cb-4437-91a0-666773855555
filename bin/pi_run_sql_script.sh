#!/bin/bash
#-----------------------------------------------------------------------------
#
#   PROCEDURE:    pi_run_sql_script.sh
#
#   PURPOSE:      This script executes the given SQL script. It's a wrapper
#                 for the run_sql_script function.
#
#   PARAMETERS:   sql_script
#
#   USAGE:        From the command-line, type:
#                    ./pi_run_sql_script.sh [-d <directory>] [-u <user_id>] \
#                       [-f] [-s] <sql_script>
#
#   AUTHOR:       <PERSON>
#
#
# Copyright (c) 2025 <PERSON>
# All rights reserved.
#
# This software is proprietary and confidential. Unauthorized copying of this
# file, via any medium, is strictly prohibited without the express permission
# of the author or owning organization.
#
#-----------------------------------------------------------------------------


# Include the "common" functions
unset PI_COMMON_FUNCTIONS
. $(dirname $0)/pi_common.sh

typeset command_line_args="$@"

echo command_line_args=$@

# Call the common function
run_sql_script $command_line_args


# Complete
