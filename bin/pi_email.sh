#!/bin/bash
#-----------------------------------------------------------------------------
#
#   PROCEDURE:    pi_email.sh
#
#   PURPOSE:      This script contains the email processing functions.
#
#   PARAMETERS:   None.
#
#   USAGE:        From the command-line, type:
#                 . ./pi_email.sh
#
#   AUTHOR:       <PERSON>
#
#
# Copyright (c) 2025 Kevin <PERSON>re
# All rights reserved.
#
# This software is proprietary and confidential. Unauthorized copying of this
# file, via any medium, is strictly prohibited without the express permission
# of the author or owning organization.
#
#-----------------------------------------------------------------------------

# Mark the email functions as defined
# To force this script to re-execute, use:
#    unset PI_EMAIL_FUNCTIONS
if [ "${PI_EMAIL_FUNCTIONS:=Undefined}" = "Defined" ]
then
   echo "The email processing functions are already defined."
else
   PI_EMAIL_FUNCTIONS="Defined"
   export PI_EMAIL_FUNCTIONS
fi




# Email connection Support ----------------------------------------------------

function get_mailbox
{
   #------------------------------------------ Add encryption to the file
   typeset credential_file=${1:-"$SIMPLIPIED_HOME/.mail_connect"}
   typeset mailbox

   if [ -r $credential_file ]
   then
      mailbox="$(cat $credential_file | jq -r ".mailbox")"
      echo $mailbox
   else
      echo "ERROR: Mail connect file (${credential_file}) not found."
   fi

   unset credential_file
   unset mailbox

} # End - get_mailbox



function get_mailbox_user
{
   #------------------------------------------ Add encryption to the file
   typeset credential_file=${1:-"$SIMPLIPIED_HOME/.mail_connect"}
   typeset mailbox_user

   if [ -r $credential_file ]
   then
      mailbox_user="$(cat $credential_file | jq -r ".mailbox_user")"
      echo $mailbox_user
   else
      echo "ERROR: Mail connect file (${credential_file}) not found."
   fi

   unset credential_file
   unset mailbox_user

} # End - get_mailbox_user



function get_mailbox_password
{
   #------------------------------------------ Add encryption to the file
   # Assumption: There is only one mailbox configured in the file
   typeset credential_file=${1:-"$SIMPLIPIED_HOME/.mail_connect"}
   typeset encrypted_password
   typeset mailbox_password

   if [ -r $credential_file ]
   then
      encrypted_password="$(cat $credential_file | jq -r ".mailbox_password")"
      mailbox_password=$(decrypt_string "${encrypted_password}")
      echo $mailbox_password
   else
      echo "ERROR: Mail connect file (${credential_file}) not found."
   fi

   unset credential_file
   unset encrypted_password
   unset mailbox_password

} # End - get_mailbox_password





function list_email_headers
{
   execute_mailx_command ""

} # - list_email_headers



function get_email_subject
{
   typeset message_number=$1

   execute_mailx_command $message_number | grep "Subject: " | cut -c10-

   unset message_number

} # - get_email_subject



function get_email_sender
{
   typeset message_number=$1

   execute_mailx_command $message_number | grep "From: " | cut -c7-

   unset message_number

} # - get_email_sender



function get_email_date
{
   typeset message_number=$1

   execute_mailx_command $message_number | grep "Date: " | cut -c7- 

   unset message_number

} # - get_email_date




function execute_mailx_command
{
   # The mailx command is one that would be issued in the interactive 
   # interface (e.g. "2" to read message 2).
   typeset mailx_command
   typeset credential_file
   typeset MAILBOX
   typeset MAILUSER
   typeset MAILPASS
   typeset email_date

   # Get the options
   local OPTIND
   while getopts ":s:" option
   do
      case $option in
         s)  typeset -u save_changes=${OPTARG};;
         :)  echo "Option -$OPTARG requires a value";;
         \?) echo -u2 "Unknown option: $OPTARG";;
      esac
   done
   shift $((OPTIND-1))

   if [ "$save_changes" = "Y" ]
   then
      exit_command="quit"
   else
      exit_command="exit"
   fi

   # Get the parameters (after the command-line options)
   mailx_command=${1}
   credential_file=${2:-"$SIMPLIPIED_HOME/.mail_connect"}
   MAILBOX="$(get_mailbox $credential_file)"
   MAILUSER="$(get_mailbox_user $credential_file)"
   MAILPASS="$(get_mailbox_password $credential_file)"


   # Build the mailx command to display the email headers
   # "-H"        - Print header summaries for all messages and exit.
   # "-f [file]" - Read in the contents of the user's mbox
   if [ "$mailx_command" = "" ]
   then
      echo "mailx -H -f $MAILBOX <<-EOF" > /var/tmp/temp_mailx_command.sh
      echo $MAILUSER >>  /var/tmp/temp_mailx_command.sh
      echo $MAILPASS >> /var/tmp/temp_mailx_command.sh
   else
      echo "mailx -f $MAILBOX <<-EOF" > /var/tmp/temp_mailx_command.sh
      echo $MAILUSER >>  /var/tmp/temp_mailx_command.sh
      echo $MAILPASS >> /var/tmp/temp_mailx_command.sh
      echo "$mailx_command" >> /var/tmp/temp_mailx_command.sh
   fi

   echo "$exit_command" >> /var/tmp/temp_mailx_command.sh
   echo "EOF" >> /var/tmp/temp_mailx_command.sh

   chmod +x /var/tmp/temp_mailx_command.sh
   /var/tmp/temp_mailx_command.sh


   # Clean-up
   rm  /var/tmp/temp_mailx_command.sh 2> /dev/null

   unset credential_file
   unset message_number
   unset MAILBOX
   unset MAILUSER
   unset MAILPASS

} # - execute_mailx_command




function get_email_message_number
{
   typeset target_subject="$1"
   typeset found_message="0"
   typeset email_record
   typeset email_header
   typeset found_subject

   # Loop though the headers to find the message
   # (Note: Because the "list_email_headers | while read..." executes in a 
   # subshell, variables set don't retain their value. Use the HERE Doc
   # instead.)
   while read email_record
   do
      if is_number "$(echo $email_record | cut -c1-2 | tr -d ' ')"
      then
         email_header=$(echo "$email_record")
      else
         # Remove the first two characters
         email_header=$(echo "$email_record" | cut -c3-)
      fi

      found_subject=$(echo $email_header | awk -F\" '{print $2}')

      if [ "$found_subject" = "$target_subject" ]
      then
         found_message="$(echo $email_header | awk '{print $1}')"
         break
      fi

   done <<< $(list_email_headers)
   
   echo "${found_message}"

   unset target_subject
   unset found_message
   unset email_record
   unset email_header
   unset found_subject

} # - get_email_message_number


function move_email
{
   typeset message_number
   typeset target_folder
   typeset save_option

   # Get the options
   local OPTIND
   while getopts ":s:" option
   do
      case $option in
         s)  typeset -u save_changes=${OPTARG};;
         :)  echo "Option -$OPTARG requires a value";;
         \?) echo -u2 "Unknown option: $OPTARG";;
      esac
   done
   shift $((OPTIND-1))

   if [ "$save_changes" = "Y" ]
   then
      save_option="Y"
   else
      save_option="N"
   fi


   # Get the parameters (after the command-line options)
   message_number=$1
   target_folder=$2

   execute_mailx_command -s${save_option} "move $message_number +${target_folder}"

   unset message_number
   unset target_folder
   unset save_option

} # - get_email_date



function add_email_connection
{
   typeset mailbox=${1}
   typeset mailbox_user=${2}
   typeset mailbox_password="${3}"
   typeset connection_file=${4:-"${SIMPLIPIED_HOME}/.mail_connect"}
   typeset encrypted_password=$(encrypt_string ${mailbox_password})
   
#   if [ "$(remote_connection_exists $site_name $user_id $connection_file)" = "YES" ]
#   then
#      update_remote_connection $site_name $user_id $connection_password $connection_file
#   else
      echo "{" >> ${connection_file}
      echo "  \"mailbox\":\"${mailbox}\"," >> ${connection_file}
      echo "  \"mailbox_user\":\"${mailbox_user}\"," >> ${connection_file}
      echo "  \"mailbox_password\":${encrypted_password}" >> ${connection_file}
      echo "}" >> ${connection_file}
#   fi
   
   unset mailbox
   unset mailbox_user
   unset mailbox_password
   unset connection_file
   unset encrypted_password

} # - add_email_connection



function email_connection_exists
{
   typeset mailbox=${1}
   typeset mailbox_user=${2}
   typeset connection_file=${3:-"${SIMPLIPIED_HOME}/.mail_connect"}

   if [ ! -r ${connection_file} ]
   then
      echo "NO"
   else
      if [ "$(jq "select(.mailbox == \"${mailbox}\" and .mailbox_user == \"${mailbox_user}\")" ${connection_file})" == "" ]
      then
         echo "NO"
      else
         echo "YES"
      fi
   fi

} # - remote_connection_exists





# End - pi_email.sh
