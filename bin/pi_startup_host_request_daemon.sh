p!/bin/bash
#-----------------------------------------------------------------------------
#
#   PROCEDURE:    pi_startup_host_request_daemon.sh
#
#   PURPOSE:      This script is used to startup (or shutdown and restart if
#                 it's running) the host request script as a daemon process.
#
#   PARAMETERS:   None.
#
#   USAGE:        At the LINUX prompt, type:
#                    ./pi_startup_host_request_daemon.sh
#
#   AUTHOR:       <PERSON>
#
#
# Copyright (c) 2025 <PERSON>re
# All rights reserved.
#
# This software is proprietary and confidential. Unauthorized copying of this
# file, via any medium, is strictly prohibited without the express permission
# of the author or owning organization.
#
#-----------------------------------------------------------------------------


# Include the "common" functions
. $(dirname $0)/pi_common.sh

# Local variables
typeset result


# The host request daemon process looks for the pi_stop_host_request_daemon
# file in the /var/tmp directory. If it finds it, it shuts down gracefully.
# The daemon process will remove the file as it shuts down.

# Check if the daemon is currently running.  If so shut it down.
result=$(ps -ef | grep pi_startup_host_request_daemon.sh | grep -v grep)

if [ "$result" != "" ]
then
   echo "Process: $result"
   $(dirname $0)/pi_shutdown_host_request_daemon.sh
fi

# Start the program as a daemon process
printf "%s\n" "Starting up the Host Request daemon ..."
nohup $(dirname $0)/pi_host_request_daemon.sh &
printf "%s\n\n" "...started."

unset result

# Complete
