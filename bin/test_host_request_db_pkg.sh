#!/bin/bash
#-----------------------------------------------------------------------------
#
#   PROCEDURE:    test_host_request_db_pkg.sh
#
#   PURPOSE:      This script executes the HOST_REQUEST database package unit
#                 tests.
#
#   PARAMETERS:   None.
#
#   USAGE:        From the command-line, type:
#                    test_host_request_db_pkg.sh
#
#   AUTHOR:       <PERSON>
#
#
# Copyright (c) 2025 Kevin <PERSON>re
# All rights reserved.
#
# This software is proprietary and confidential. Unauthorized copying of this
# file, via any medium, is strictly prohibited without the express permission
# of the author or owning organization.
#
#-----------------------------------------------------------------------------


# Include the "common" functions
unset PI_COMMON_FUNCTIONS
. $(dirname $0)/pi_common.sh


# Call the utPLSQL test package
run_sql_proc "ut.run('test_host_request')"


# Complete
