#!/bin/bash
#-----------------------------------------------------------------------------
#
#   PROCEDURE:    pi_common.sh
#
#   PURPOSE:      This script contains the application common functions.
#
#   PARAMETERS:   None.
#
#   USAGE:        From the command-line, type:
#                 . ./pi_common.sh
#
#   AUTHOR:       <PERSON>
#
#
# Copyright (c) 2025 Kevin <PERSON>re
# All rights reserved.
#
# This software is proprietary and confidential. Unauthorized copying of this
# file, via any medium, is strictly prohibited without the express permission
# of the author or owning organization.
#
#-----------------------------------------------------------------------------

# Mark the common functions as defined
# To force this script to re-execute, use:
#    unset PI_COMMON_FUNCTIONS
if [ "${PI_COMMON_FUNCTIONS:=Undefined}" = "Defined" ]
then
   echo "The common functions are already defined."
else
   export PI_COMMON_FUNCTIONS="Defined"
fi

# Set the Simplipied source
if [ "${SIMPLIPIED_HOME}" = "" ]
then
   export SIMPLIPIED_HOME="$HOME/simplipied"
else
   echo "Using $SIMPLIPIED_HOME"
fi

# Set the Simplipied inbound directory
if [ "${SIMPLIPIED_INBOUND}" = "" ]
then
   export SIMPLIPIED_INBOUND="$HOME/inbound"
else
   echo "Inbound: $SIMPLIPIED_INBOUND"
fi

# Set the Simplipied outbound directory
if [ "${SIMPLIPIED_OUTBOUND}" = "" ]
then
   export SIMPLIPIED_OUTBOUND="$HOME/outbound"
else
   echo "Outbound: $SIMPLIPIED_OUTBOUND"
fi



# General support ------------------------------------------------------------

function file_substitute
#
# This function parses the given file and substitutes the key/values pairs and
# writes the new file to the given target file.
#
#   Usage:
#      file_substitute <key1:value1[;key2;value2[...]]> <source_file> \
#         <target_file>
#
#   Where:
#      <key1:value1[;key2;value2[...]]> is a list of substitution key/value
#      pairs separated by a semicolon (;) if multiple substitutions are needed.
#      The key (string to replace) is separated from the (replacement) value
#      by a colon (:).
#
#      <source_file> is the file to be read.
#
#      <target_file> is the resulting file with the "key" strings replaced by
#      the given values.
#
{
   # Get the parameter(s)
   typeset substitutions=${1}
   typeset source_file=${2}
   typeset target_file=${3}


   # Local variables
   typeset pair=""
   typeset key=""
   typeset value=""
   typeset cmd_file="/var/tmp/sed_commands_for_${source_file##*/}"


   # Initialize the file
   echo "" > $cmd_file
   chmod 777 $cmd_file > /dev/null 2>&1

    # Process each of the key/value pairs
   while [ "$(echo $substitutions | awk -F\; '{print $1}')" != "" ]
   do
      pair=$(echo $substitutions | awk -F\; '{print $1}')
      key=$(echo $pair | awk -F\: '{print $1}')
      value=$(echo $pair | awk -F\: '{print $2}')

      # debugging
      # echo "pair: $pair"
      # echo "key: $key"
      # echo "value: $value"

      # Write the sed substitutions to the temp command file
      echo "s/${key}/${value}/g" >> $cmd_file

      # Remove the pair
      substitutions=${substitutions#${pair};}
      substitutions=${substitutions#${pair}}
   done


   # Use sed to substitute the keys with the values in the target file
   sed -f $cmd_file $source_file > $target_file
   chmod 777 $target_file > /dev/null 2>&1

   rm $cmd_file 2> /dev/null

} # End - file_substitute


function upper
# This function returns the uppercase value of the given string.
{
   typeset -u in_string=${1}

   echo $in_string
}


function is_number
# This function returns True (1) if the given string is a number (all digits)
{
   typeset in_string=${1}

   # if [[ "$in_string" =~ ^[0-9]+$ ]]; then echo "is a number";else echo "is NOT a number"; fi

   if [[ $in_string =~ ^[0-9]+$ ]] 
   then
      # True = 0
      return 0
   else
      # False = 1
      return 1
   fi

   unset $in_string
}


# Database support -----------------------------------------------------------

function get_database
# This function returns the database string for the given host. 
#
#   Usage:
#      get_database [host]
#
#   Example:
#      database=$(get_database [host])
#
#   Where:
#      host is the server name for the database (defaults to the current host).
#
#   Returns:
#      The database string (e.g. ORCL or //**********:1521/AHCDB1_pdb1.dbnet.ahc.oraclevcn.com)
#
{

   # Get the parameters
   typeset -l server_name=${1:-"$(hostname)"}

   if [ "$server_name" = "ahcdbvm1" ]
   then
      echo "//**********:1521/AHCDB1_pdb1.dbnet.ahc.oraclevcn.com"
   else
      echo "orcl"
   fi

} # End - get_database



function get_db_con
# This function returns the database connection string (user_id/password@DB)
#
#   Usage:
#      get_db_con <user_id> [database] [connect_file]
#
#   Example:
#      db_conn=$(get_db_con <user_id> [database] [connect_file])
#
#   Where:
#      db_conn is your local variable that will contain the returned database
#      connection string. 
#         <user_id>         - the database user_id (schema) 
#         [database]        - the optional target database (defaults to the 
#                             local database)
#         [connection_file] - The option file containing the connection 
#                             definitions (defaults to $SIMPLIPIED_HOME/.connect)
#
#   Returns:
#      The database connection string (user_id/password@DB)
#
{

   # Get the parameters
   typeset -l user_id=${1:-""}
   typeset -l database=${2:-"$(get_database)"}
   typeset connection_file=${3:-"$SIMPLIPIED_HOME/.connect"}

   if [ "$(db_connection_exists $user_id $database $connection_file)" = "YES" ]
   then
      #echo "${user_id}/$(get_db_passwordi ${user_id} ${database})@${database}"
      echo "${user_id}/$(get_db_password ${user_id} ${database})" # Adding "@database" hangs sqlplus???
   else
      echo "ERROR: Could not find the ${user_id} ID for ${database}."
   fi

} # End - get_db_con




function run_sql_script
#
# This function executes the given SQL script. 
#
#   Usage:
#      run_sql_script [-d <directory>] [-u <user_id>] [-f] [-s] <sql_script>
#
#   Where:
#      [-d <directory>] is the script location (defaults to ${SIMPLIPIED_HOME}/sql),
#      [-u user_id] is the database ID (defaults to apps),
#      [-f] is used to force the script to continue if there's a 
#         error (useful for things like "DROP" statements if the object 
#         doesn't exist yet), 
#      [-s] is used to run SQL*Plus in "silent" mode, and
#      <sql_script> is the script to execute. 
#
#   Returns:
#      0 - Success
#      1 - Failure - The script was not provided
#      2 - Failure - Unable to get the database connect string
#      3 - Failure - An Oracle error occurred
#      4 - Failure - The script was not found
#
{

   # Declare the local variables
   typeset override_user=""
   typeset override_directory=""
   typeset force="N"
   typeset silent=""
   typeset user_id=""
   typeset directory=""
   typeset db_con=""
   typeset sqlerror=""
   typeset result_line=""


   # Get the options
   local OPTIND 
   while getopts ":u:d:fs" option
   do
      case $option in
         u)  override_user=${OPTARG};;
         d)  override_directory=${OPTARG};;
         f)  force="Y";;
         s)  silent="-s";;
         :)  echo "Option -$OPTARG requires a value";;
         \?) echo -u2 "Unknown option: $OPTARG";;
      esac
   done
   shift $((OPTIND-1))



   # Get the parameters
   sql_script=${1:-""}


   # Check the parameters
   if [ "$sql_script" = "" ]
   then
      echo "ERROR: You need to specify a SQL script."
      return 1
   fi

   if [ "${override_user:-NULL}" = "NULL" ]
   then
      user_id="apps"
   else
      user_id="$override_user"
   fi

   if [ "${override_directory:-NULL}" = "NULL" ]
   then
      directory="${SIMPLIPIED_HOME}/sql"
   else
      directory="$override_directory"
   fi

   
   if [ "${force:=N}" = "Y" ]
   then
      sqlerror="WHENEVER SQLERROR CONTINUE;"
   fi



   # Get the database connection information
   db_con=$(get_db_con $user_id)
   if [ "$?" != "0" ]
   then
      echo "ERROR: Unable to find the database connection information for" \
        "${user_id}."
      return 2
   fi


   # Execute the script
   if [ -f "${directory}/${sql_script}" ]
   then
      # Run the script
      sqlplus $silent -l $db_con <<-EOF
         $sqlerror
         @${directory}/${sql_script}
         EXIT
	EOF
      return_status="$?"


      # Check for Oracle errors
      echo $sql_result | while read result_line
      do
         # echo "...SQL*Plus result:$result_line" 
         if [ "$(echo $result_line | grep ORA-)" != "" ]
         then
            # There was an Oracle error
            echo $result_line
            return 3
         fi
      done

   else
      echo "ERROR: $sql_script was not found in ${directory}"
      return 4 
   fi

   # Everything executed correctly.
   return 0

} # End - run_sql_script



function run_sql_func
# 
# This function executes the given SQL stored function.
#
# NOTE: All functions are executed from APPS.
#
#   Usage:
#      run_sql_func <sql_func>
#
#   Where:
#      <sql_func> is the stored function to execute. 
#
#   Returns:
#      0 - Success
#      1 - Failure - The function was not provided
#      2 - Failure - Unable to get the database connect string
#      3 - Failure - An Oracle error occurred
#      4 - Failure - The function was not found
{

   # Get the parameter(s)
   sql_function=${1:-""}


   # Declare the local variables
   typeset user_id="apps"
   typeset db_con=""
   typeset sqlerror=""
   typeset sql_result=""


   # Check the parameters
   if [ "$sql_function" = "" ]
   then
      echo "ERROR: You need to specify a stored function."
      return 1
   fi


   # Get the database connection information
   db_con=$(get_db_con $user_id)
   if [ $? -ne 0 ]
   then
       echo "ERROR: Unable to find the database connection information for" \
         "${user_id}."
      return 2
   fi


   # Run the stored function
   sql_result=$(sqlplus -l -s $db_con <<-ENDSQL
      SET HEADING OFF
      SET LINESIZE 400
      SET SCAN OFF
      SELECT $sql_function FROM dual;
      EXIT
	ENDSQL
)

      # Check for Oracle errors
      echo $sql_result | while read result_line
      do
         # echo "...SQL*Plus result:$result_line"
         if [ "$(echo $result_line | grep ORA-00904)" != "" ]
         then
            # The stored function was not found.
            echo "ERROR: The function, ${sql_function}, does not exist."
            return 4
         elif [ "$(echo $result_line | grep ORA-)" != "" ]
         then
            # There was an Oracle error
            echo "ERROR: An Oracle error occurred ($result_line)."
            return 3
         fi
      done

   # No errors
   echo $sql_result
   return 0

} # End - run_sql_func


function run_sql_proc
#
# This function executes the given SQL stored procedure.
#
# NOTE: All stored procedures are executed from APPS.
#
#   Usage:
#      run_sql_proc [-s y/n] <sql_proc>
#
#   Where:
#      [-s y/n] is the optional spooling (defaults to "y"),
#      <sql_proc> is the stored procedure to execute. 
#
#   Returns:
#      0 - Success
#      1 - Failure - The procedure was not provided
#      2 - Failure - Invalid spooling option 
#      3 - Failure - Unable to get the database connect string
#      4 - Failure - An Oracle error occurred
#      5 - Failure - The procedure was not found
{

   # Get the options
   local OPTIND 
   while getopts :s: option
   do
      case $option in
         s)  typeset -u spool=$OPTARG;;
         :)  echo "Option -$OPTARG requires a value";;
         \?) echo -u2 "Unknown option: $OPTARG";;
      esac
   done
   shift $((OPTIND-1))


   # If not given, set the spooling flag
   if [ "$spool" = "" ]
   then
      spool="Y"
   else
      if [ "$spool" = "Y" -o "$spool" = "N" ]
      then
         :
      else
         echo "ERROR: Invalid Spooling (-r) option: ${spool}"
         echo "Valid options are \"y\" or \"n\"."
         return 2
      fi
   fi


   # Get the parameter(s)
   sql_procedure=${1:-""}

   # Declare the local variables
   typeset user_id="apps"
   typeset db_con=""
   typeset sql_result=""
   typeset result_line=""
   typeset proc_name



   # Check the parameters
   if [ "$sql_procedure" = "" ]
   then
      echo "ERROR: You need to specify a stored procedure."
      return 1
   else
      proc_name=$(echo "$sql_procedure" | awk -F\( '{print $1}')
   fi

   if [ "$spool" = "Y" ]
   then
      typeset temp_spool="/var/tmp/temp_${proc_name}"
   else
      typeset temp_spool="/var/tmp/temp_run_sql_proc_$(date +%Y%m%d%H%M)"
   fi



   # Get the database connection information
   db_con=$(get_db_con $user_id)
   if [ $? -ne 0 ]
   then
      echo "ERROR: Unable to find the database connection information for" \
         "${user_id}."
      return 3
   fi

   # Run the stored procedure
   sql_result=$(sqlplus -l -s $db_con <<-EOF
      SET SERVEROUTPUT ON SIZE 1000000;
      SET ECHO ON
      SET SCAN OFF
      SPOOL ${temp_spool}.lst
      EXECUTE $sql_procedure
      SPOOL OFF
      EXIT
	EOF
)

   # Add the spool file
   cat ${temp_spool}.lst
   chmod 777 ${temp_spool}.lst > /dev/null 2>&1

   # Check for Oracle errors
   echo $sql_result | while read result_line
   do
      #echo "...SQL*Plus result:$result_line"
      if [ "$(echo $result_line | grep PLS-00201)" != "" ]
      then
         # The stored procedure was not found.
         echo "ERROR: The procedure, ${proc_name}, does not exist."
         return 5
      elif [ "$(echo $result_line | grep PLS-00302)" != "" ]
      then
         # The stored package procedure was not found.
         echo "ERROR: The package procedure, ${proc_name}, does not exist."
         return 5
      elif [ "$(echo $result_line | grep ORA-)" != "" ]
      then
         # There was an Oracle error
         echo "ERROR: An Oracle error occurred ($result_line)."
         return 4
      fi
   done

   # No errors
   return 0

} # End - run_sql_proc



function run_sql_loader 
#
# This function calls SQL*Loader with the given control file.
#
#   Usage:
#      run_sql_loader [-u <user_id>] [-d <data_file>] \
#         [-b <bad_file>] [-l <log_file) <control_file> [substitutions]
#
#   Where:
#      [-u <user_id>] is the optional database ID (defaults to apps),
#      [-d <data_file>] is the optional data file (if overridding the control 
#         file),
#      [-b <bad_file>] is the optional bad file (if overridding the default), 
#      [-l <log_file>] is the optional log file (if overridding the default), 
#      <control_file> is the required SQL*Loader control file name,
#      and
#      [substitutions] is for optional control file substitutions in the form
#      "Replacement string:New value[;string:value]". 
#
#   Returns:
#      0 - Success
#      1 - Failure - The control file was not provided
#      2 - Failure - The control file was not found
#      3 - Failure - The data file was not found
#      6 - Failure - The generated log file was not found
#      7 - Failure - There were record errors during the load
#
# NOTES:
#   The default location for control files is ${SIMPLIPIED_HOME}/ctl.
#
#   "DATA_FILE_NAME" is replaced with the actual file name if it appears in the
#   control file (e.g. source CONSTANT "DATA_FILE_NAME").
#
{

   # Local variables
   typeset user_id="apps"
   typeset data_file=""
   typeset bad_file=""
   typeset log_file=""
   typeset control_file_path=${SIMPLIPIED_HOME}/ctl
   typeset result_line


   # Get the options
   local OPTIND 
   while getopts :u:d:b:l: option
   do
      case $option in
         u)  user_id=$OPTARG;;
         d)  data_file=$OPTARG;;
         b)  bad_file=$OPTARG;;
         l)  log_file=$OPTARG;;
         :)  echo "Option -$OPTARG requires a value";;
         \?) echo -u2 "Unknown option: $OPTARG";;
      esac
   done
   shift $((OPTIND-1))


   # Get the parameter(s)
   control_file=${1:-""}
   substitutions=${2:-""}


   # Check the parameters
   if [ "$control_file" = "" ]
   then
      echo "ERROR: You need to specify a control file."
      return 1
   else
      if [ "$log_file" = "" ]
      then
         # The SQL*Loader log file is based on the control file name
         log_file=/var/tmp/${control_file%.ctl}.log
      fi

      # Check for control file
      if [ ! -r ${control_file_path}/${control_file} ]
      then
         echo "ERROR: Unable to find the ${control_file} control file in " \
            "${control_file_path}."
         return 2
      fi
   fi


   # If the data file was not given, parse from control file and get it
   if [ "$data_file" = "" ]
   then
      while read result_line
      do
         if [ "$(echo $result_line | grep 'INFILE ')" != "" ]
         then
            data_file=$(echo $result_line | awk '{print $2}' | tr -d "\'")
            break
         fi
      done < ${control_file_path}/${control_file}
   fi


   # Check for data file        
   if [ ! -r $data_file ]
   then
      echo "ERROR: Unable to find the ${data_file} data file."
      return 3
   fi


   # If the bad file was not given, use the default
   if [ "${bad_file}" = "" ]
   then
      bad_file=""
   else
      bad_file="bad=${bad_file} "
   fi


   # Add the DATA_FILE_NAME to the substitution key/value sets
   if [ "$substitutions" = "" ]
   then
      substitutions="DATA_FILE_NAME:${data_file##*/}"
   else
      substitutions="DATA_FILE_NAME:${data_file##*/};$substitutions"
   fi

   # Call the function to substitute the variables in the control file.
   file_substitute $substitutions ${control_file_path}/${control_file} \
     /var/tmp/${control_file}


   # Call SQL*Loader
   sqlldr "userid=$(get_db_con ${user_id}) data=${data_file} ${bad_file} log=${log_file} control=/var/tmp/${control_file}"


   # Check the log file for errors
   if [ ! -r $log_file ]
   then
      echo "ERROR: Unable to find the ${log_file} log file."
      return 6
   fi

   # No errors
   return 0

} # End - run_sql_loader


# Interface support ----------------------------------------------------------
function move_inbound_file
# This function moves the given file from the ${SIMPLIPIED_INBOUND} directory to
# the /var/tmp directory and creates a backup with a date/time suffix.
#
# It returns the file name with the timestamp suffix.
#
#   Usage:
#      move_inbound_file <data_file>
#   Or:
#      new_file=$(move_inbound_file <data_file>)
#
#   Where:
#      <data_file> is the data file to move. It can include wildcard (*) 
#      characters.
#
{
   typeset inbound_file=${1:-"no_file_given"}
   typeset target_file
   typeset time_stamp="$(date +%Y%m%d%H%M%S)"
   typeset return_value
   typeset -i matching_files=0
   typeset -i error_status=0

   matching_files=$(ls ${SIMPLIPIED_INBOUND}/${inbound_file} 2> /dev/null | wc -l)

   if [[ matching_files -eq 0 ]]
   then
      return_value="ERROR: No matching files found for ${inbound_file} in "
      return_value="${return_value} ${SIMPLIPIED_INBOUND}."
      error_status=1
   else
      # Return the first matching file
      for target_file in ${SIMPLIPIED_INBOUND}/$inbound_file 
      do 
         cp $target_file /var/tmp/
         mv $target_file /var/tmp/${target_file##*/}_${time_stamp}
         return_value="/var/tmp/${target_file##*/}_${time_stamp}"
         break 
       done
   fi

   echo $return_value
   return $error_status

   unset inbound_file
   unset target_file
   unset time_stamp
   unset return_value
   unset matching_files

} # End - move_inbound_file




# Email support --------------------------------------------------------------
function send_email
#
# This function sends an email.
#
#   Usage:
#      send_email -i <interface> | -t <to_address> [-c <cc_address>] \
#         [-b <bcc_address>] [-s <subject>] [-a <attachments>] <body>
#
#   Where:
#      -i <interface> is use to retrieve the addresses from 
#         PI_INTERFACE_EMAIL_ADDRESSES. ------------------------------------- Not implemented yet
#      -t <to_address> is the required recipient's email address. Multiple 
#         addresses are enclosed in quotes and separated by commas.
#      (MUST use either -i or -t, but not both).
#      [-c <cc_address>] is the optional "courtesy copy" recipient's email 
#         address. 
#      [-b <bcc_address>] is the optional "blind courtesy copy" recipient's 
#         email address. 
#      [-s <subject>] is the Subject line of the email.
#      [-a <attachments>] is the optional attachment file names. Multiple
#         files are enclosed in quotes and separated by spaces.
#      <body> is the body of the email message (either a text string or
#         a file).
#
#
{

   typeset subject=""
   typeset -u interface=""
   typeset addressees=""
   typeset cc_addressees=""
   typeset bcc_addressees=""
   typeset attachments=""
   typeset verbose="N"
   typeset return_address="-r <EMAIL> "
   typeset temp_message="/var/tmp/temp_msg_$(date +%Y%m%d%H%M%S).txt"
   typeset disclaimer="*** This is an automated email. Please do not reply to the sender. ***"

   # Get the options
   local OPTIND 
   while getopts ":s:i:t:c:b:a:v" option
   do
      case $option in
         s)  subject="${OPTARG}";;
         i)  interface=${OPTARG};;
         t)  addressees=${OPTARG};;
         c)  cc_addressees="-c ${OPTARG} ";;
         b)  bcc_addressees="-b ${OPTARG} ";;
         a)  attachments="-a ${OPTARG} ";;
         v)  verbose="Y";;
         :)  echo "Option -$OPTARG requires a value";;
         \?) echo -u2 "Unknown option: $OPTARG";;
      esac
   done
   shift $((OPTIND-1))


   #Get the parameter
   body=${1:-""}

   # mailx handles multiple attachments with separate "-a" option parameters
   # (i.e. -a file1.txt -a file2.txt)
   # the attachments option is passed as "-a file1.txt,file2.txt".
   if [ "$attachments" != "" ]
   then
      attachments=$(echo "$attachments" | sed 's/,/ -a /g')
   fi

   # Interface overrides To, CC, and BCC parameters/options
   if [ "$interface" != "" ]
   then
      addressees="$(get_email_addresses $interface "TO") "
      cc_addressees="-c $(get_email_addresses $interface "CC") "
      bcc_addressees="-b $(get_email_addresses $interface "BCC") "
   fi


   if [ "$verbose" = "Y" ]
   then
      echo "send_email Parameters"
      echo "   Interface:  $interface"
      echo "   To:         $addressees"
      echo "   CC:         $cc_addressees"
      echo "   BCC:        $bcc_addressees"
      echo "   Subject:    $subject"
      echo "   Attachment: $attachments"
      echo "   Body:       $body"
   fi

   if [ -r "$body" ]
   then 
      cat $body > $temp_message
   else
      echo $body > $temp_message
   fi
   
   # Add the disclaimer (add a couple of blank lines for spacing)
   echo >> $temp_message
   echo >> $temp_message
   echo "$disclaimer" >> $temp_message


   # Finally, send the email
   if [ "$verbose" = "Y" ]
   then
      echo "mailx -s \"${subject}\" ${cc_addressees}${bcc_addressees}${attachments}${return_address}${addressees} < $temp_message"
   fi
   mailx -s "${subject}" ${cc_addressees}${bcc_addressees}${attachments}${return_address}${addressees} < $temp_message

   rm $temp_message 2> /dev/null
   unset subject
   unset addressees
   unset cc_addressees
   unset bcc_addressees
   unset attachments
   unset return_address
   unset temp_message
   unset disclaimer

} # End - send_email



function get_subject_prefix
#
# This function returns the string "ERROR: " if the error status is anything
# but zero (0). It's used when sending emails from automated processes.
#
#   Usage:
#      get_subject_prefix <error_status>
#
#   Where:
#      <error_status> is an integer indicating the calling program's status
#      (0 - success; else error).
#
{
   # Get the parameter(s)
   typeset -i error_status=${1}

   if [ "$error_status" -eq 0 ]
   then
      echo ""
   else
      echo "ERROR: "
   fi

} # End - get_subject_prefix



function get_email_addresses
#
# This function retrieves the email distribution of the given type (TO, CC, or 
# BCC) for the given interface.
#
#   Usage:
#      get_email_addresses <interface> [<address_type>]
#
#   Where:
#      <interface> is use to retrieve the addresses from the
#          PI_INTERFACE_EMAIL_ADDRESSES database table.
#      <address_type> is either TO, CC, BCC, or ALL (default). 
#
#   Multiple addresses are separated by commas.
#
{
   typeset email_list=${1}
   typeset list_type=${2:-All}

   # Call SQL package function:
   email_list=$(run_sql_func "common.get_email_addresses('$email_list', '$list_type')")

   echo $email_list

   unset email_list
   unset list_type

} # - get_email_addresses


# SFTP support ---------------------------------------------------------------
function add_remote_connection
{
   typeset site_name=${1}
   typeset user_id=${2}
   typeset connection_password="${3}"
   typeset connection_file=${4:-"${SIMPLIPIED_HOME}/.netrc"}
   typeset encrypted_password=$(encrypt_string ${connection_password})

   if [ "$(remote_connection_exists $site_name $user_id $connection_file)" = "YES" ]
   then
      update_remote_connection $site_name $user_id $connection_password $connection_file
   else
      echo "{" >> ${connection_file}
      echo "  \"site\": \"${site_name}\"," >> ${connection_file}
      echo "  \"user_id\": \"${user_id}\"," >> ${connection_file}
      echo "  \"password\": ${encrypted_password}" >> ${connection_file}
      echo "}" >> ${connection_file}
   fi

   unset site_name
   unset user_id
   unset connection_password
   unset connection_file
   unset encrypted_password

} # - add_remote_connection



function remote_connection_exists
{
   typeset site_name=${1}
   typeset user_id=${2}
   typeset connection_file=${3:-"${SIMPLIPIED_HOME}/.netrc"}

   if [ ! -r ${connection_file} ]
   then
      echo "NO"
   else
      if [ "$(jq "select(.site == \"${site_name}\" and .user_id == \"${user_id}\")" ${connection_file})" == "" ]
      then
         echo "NO"
      else
         echo "YES"
      fi
   fi

} # - remote_connection_exists


function update_remote_connection
{
   typeset site_name=${1}
   typeset user_id=${2}
   typeset connection_password="${3}"
   typeset connection_file=${4:-"${SIMPLIPIED_HOME}/.netrc"}
   typeset encrypted_password=$(encrypt_string ${connection_password})
   typeset temp_json=/var/tmp/temp_remote_connection.json


   # Delete the record and re-add it with the new password
   if [ -r ${connection_file} ]
   then
      jq "del(select(.site == \"${site_name}\" and .user_id == \"${user_id}\"))" ${connection_file} > $temp_json
      mv $temp_json $connection_file
   fi
   echo "{" >> ${connection_file}
   echo "  \"site\": \"${site_name}\"," >> ${connection_file}
   echo "  \"user_id\": \"${user_id}\"," >> ${connection_file}
   echo "  \"password\": ${encrypted_password}" >> ${connection_file}
   echo "}" >> ${connection_file}

   unset site_name
   unset user_id
   unset connection_password
   unset connection_file
   unset encrypted_password

} # - update_remote_connection



function get_remote_connection_password
{
   typeset site_name=${1}
   typeset user_id=${2:-"ANY"}
   typeset connection_file=${3:-"${SIMPLIPIED_HOME}/.netrc"}
   typeset encrypted_password
   typeset connection_password

   
   if [ -r ${connection_file} ]
   then
      # Try to get the site/user_id first (could be the same site withdifferent users)
      encrypted_password=$(jq -r "select(.site == \"${site_name}\" and .user_id == \"${user_id}\") | .password" $connection_file) 
      if [ "${encrypted_password}" = "" ]
      then
         # Try without the user_id (if blank)
         if [ "$user_id" = "ANY" ]
         then
            # This returns multiple passwords, but still works because i
            # decrypt_string only processes the first one
            encrypted_password=$(jq -r "select(.site == \"${site_name}\") | .password" $connection_file) 
         fi
      fi
         
      connection_password=$(decrypt_string "${encrypted_password}")
      echo $connection_password
   fi

   unset site_name
   unset user_id
   unset connection_file
   unset encrypted_password
   unset connection_password

} # End - get_remote_connection_password




function get_remote_connection
#
# This function gets the connection (user@host) for the given site name.
#
#   NOTE: If the site has multiple entries, this function will return the
#         matching record unless the user_id is specified.
#
#   Usage:
#      get_remote_connection <site_name> [user_id] [connection_file]
#
#   Where:
#      <site_name> is the identifier for the remote server
#
{
   typeset site_name=${1}
   typeset user_id=${2:-"ANY"}
   typeset connection_file=${3:-"${SIMPLIPIED_HOME}/.netrc"}
   typeset remote_user=""
   typeset host_name=""

   if [ -r ${connection_file} ]
   then
      # Try to get the site/user_id first (could be the same site withdifferent users)
      host_name=$(jq -r "select(.site == \"${site_name}\" and .user_id == \"${user_id}\") | .site" $connection_file)
      remote_user=$(jq -r "select(.site == \"${site_name}\" and .user_id == \"${user_id}\") | .user_id" $connection_file)
      if [ "${host_name}" = "" ]
      then
         # Try without the user_id (if blank)
         if [ "$user_id" = "ANY" ]
         then
            while read record
            do
               if [ "$(echo $record | grep $site_name)" != "" ]
               then
                  host_name=$record
                  break;
               fi
            done <<< $(jq -r "select(.site == \"${site_name}\") | .site" $connection_file)

            while read record
            do
               remote_user=$record
               break;
            done <<< $(jq -r "select(.site == \"${site_name}\") | .user_id" $connection_file)
         fi
      fi

      if [ "${remote_user}@${host_name}" != "@" ]
      then
         echo ${remote_user}@${host_name}
      fi
   fi

   unset site_name
   unset connection
   unset connect_line
   unset user_id
   unset host_name

} # End - get_remote_connection




function get_remote_file
#
# This function SFTPs to the given site and copies files from the
# given remote source to the local target.
#
#   Usage:
#      get_remote_file <site_name> <from_pattern> [<to_pattern>]
#
#   Where:
#      <site_name> is the remote connection identifier
#      <from_pattern> is the remote file(s) (directory/file pattern)
#      <to_pattern> is the local target (directory/file pattern)
{
   typeset site_name=${1}
   typeset from_pattern=${2}
   typeset to_pattern=${3}
   typeset user_id=${4}
   typeset sftp_results=""
   typeset connection="$(get_remote_connection $site_name $user_id)"

   export SSHPASS="$(get_remote_connection_password $site_name $user_id)"

   sftp_results=$(sshpass -e sftp $connection  <<-EOF
      get $from_pattern $to_pattern
      quit
EOF
)

   echo $sftp_results

   unset site_name
   unset from_pattern
   unset to_pattern
   unset sftp_results
   unset connection

} # End - get_remote_file



function put_remote_file
#
# This function SFTPs to the given site and copies any files from the
# given local source to the target.
#
#   Usage:
#      put_remote_file <site_name> <from_pattern> [<to_pattern>]
#
#   Where:
#      <site_name> is the remote connection identifier
#      <from_pattern> is the local file(s) (directory/file pattern)
#      <to_pattern> is the remote target (directory/file pattern)
#
{
   typeset site_name=${1}
   typeset from_pattern=${2}
   typeset to_pattern=${3}
   typeset user_id=${4}
   typeset sftp_results=""
   typeset connection="$(get_remote_connection $site_name $user_id)"

   export SSHPASS="$(get_remote_connection_password $site_name $user_id)"

   sftp_results=$(sshpass -e sftp $connection  <<-EOF
      put $from_pattern $to_pattern
      quit
EOF
)

   echo $sftp_results

   unset site_name
   unset from_pattern
   unset to_pattern
   unset user_id
   unset sftp_results
   unset connection

} # End - put_remote_file



function remove_remote_file
#
# This function SFTPs to the given site and removes the given file.
# given local source to the target.
#
#   Usage:
#      remove_remote_file <site_name> <file_name>
#
#   Where:
#      <site_name> is the remote connection identifier
#      <file_name> is the remove file(s) (directory/file pattern) to delete
#
{
   typeset site_name=${1}
   typeset file_name=${2}
   typeset user_id=${3}
   typeset sftp_results=""
   typeset connection="$(get_remote_connection $site_name $user_id)"

   export SSHPASS="$(get_remote_connection_password $site_name $user_id)"

   sftp_results=$(sshpass -e sftp $connection  <<-EOF
      rm $file_name
      quit
EOF
)

   echo $sftp_results

   unset site_name
   unset file_name
   unset sftp_results
   unset connection

} # End - remove_remote_file




# Encryption Support ---------------------------------------------------------
function get_passphrase
{
   typeset -u passphrase

   passphrase=$(hostname | awk -F. '{print $1}')

   echo $passphrase

   unset passphrase

} # End - get_passphrase



function get_recipient
{
   typeset -u recipient_code=$1
   typeset recipient=""

   if [ ! -r ~/.gnupg/.recipient_${recipient_code} ]
   then
      recipient="ERROR: Unable to read recipient config file"
   else
      recipient="$(cat ~/.gnupg/.recipient_${recipient_code})" 
   fi 
   
   echo $recipient

   unset recipient_code
   unset recipient

} # End - get_recipient



function update_recipient
{
   typeset -u recipient="$1"
   typeset new_email="$2"
   
   # The current PGP email identifier for the given recipient will be specified
   # in the ~/.gnupg/.recipient_<recipient> file.
   echo $new_email > ~/.gnupg/.recipient_${recipient}

   unset recipient
   unset new_email

} # End - update_recipient



function update_key_used
{
   typeset -u sender="$1"
   typeset new_email="$2"

   # The current PGP email identifier used for the given sender will be 
   # specified in the ~/.gnupg/.key_used_<sender> file.
   echo $new_email > ~/.gnupg/.key_used_${sender}

} # End - update_key_used


function get_key_used
{
   typeset -u sender=$1
   typeset key_used=""


   if [ ! -r ~/.gnupg/.key_used_${sender} ]
   then
      key_used="ERROR: Unable to read key config file"
   else
      key_used="$(cat ~/.gnupg/.key_used_${sender})"
   fi


   echo $key_used

   unset sender
   unset key_used

} # End - get_key_used


function encrypt_file
{
   typeset -u recipient=$1
   typeset file_to_encrypt=$2
   typeset encryted_file_name=${3:-${file_to_encrypt}.asc}

   gpg --recipient $(get_recipient ${recipient}) --armor --batch --passphrase "$(get_passphrase)" --output $encryted_file_name --sign --local-user $(get_key_used ${recipient}) --pinentry-mode loopback --encrypt $file_to_encrypt

} # End - encrypt_file


function decrypt_file
{
   typeset use_key=${1}
   typeset file_to_decrypt=$2
   typeset unencryted_file_name=${3:-${file_to_decrypt%%.asc}}

   gpg --batch --passphrase "$(get_passphrase)" --no-verbose --quiet --local-user $(get_key_used ${use_key}) --output $unencryted_file_name --pinentry-mode loopback --decrypt $file_to_decrypt

} # End - decrypt_file



function encrypt
{
   typeset string_to_encrypt=$1
   typeset -u recipient=${2:-PI_ADMIN}
   typeset encrypted_string
   
   echo "$string_to_encrypt" | \
      gpg --armor --batch --passphrase "$(get_passphrase)" \
          --recipient $(get_recipient ${recipient}) --pinentry-mode loopback \
          --local-user $(get_key_used ${recipient}) --encrypt

   unset recipient
   unset string_to_encrypt
   unset encrypted_string

} # End - encrypt



function decrypt
{
   typeset string_to_decrypt="$1"  # Calling program must add quotes also
   typeset -u use_key=${2:-PI_ADMIN}

   printf "%s\n" "$string_to_decrypt" | \
      gpg --batch --passphrase "$(get_passphrase)" --no-verbose --quiet \
          --local-user $(get_key_used ${use_key}) --pinentry-mode loopback \
          --decrypt

   unset use_key
   unset string_to_decrypt

} # End - decrypt


function encrypt_string
{
   typeset string_to_encrypt="${1}"
   typeset -u use_key=${2:-PI_ADMIN}
   typeset encrypted_string
   typeset escaped_string

   # Step 1: Encrypt the string and capture output
   encrypted_string=$(echo "${string_to_encrypt}" | \
      gpg --symmetric --armor --batch --yes --passphrase '$(get_passphrase)' \
          --local-user $(get_key_used $use_key))

   # Step 2: Escape newlines and quotes for JSON
   escaped_string=$(echo "$encrypted_string" | \
      python3 -c 'import json,sys; print(json.dumps(sys.stdin.read()))')

   # Step 3: Write to a JSON file
   echo "{\"encrypted_message\": $escaped_string}" 


   unset string_to_encrypt
   unset use_key
   unset encrypted_string
   unset escaped_string

} # End - encrypt_string



function decrypt_string
{
   typeset encrypted_string="${1}"
   typeset -u use_key=${2:-PI_ADMIN}
   typeset extracted_string

   # Extract the encrypted string from JSON
   extracted_string=$(echo "${encrypted_string}" | \
      jq -r --args '.encrypted_message')

   # Decrypt it
   echo "$extracted_string" | \
      gpg --decrypt --batch --yes --passphrase '$(get_passphrase)' \
          --local-user $(get_key_used $use_key) 2> /dev/null

   unset encrypted_string
   unset use_key
   unset extracted_string

} # End - decrypt_string



function generate_gpg_key
{
   typeset real_name=${1}
   typeset email=${2}
   typeset comment=${3}
   typeset expire=${4}
   typeset time_stamp="$(date +%Y%m%d%H%M%S)"

   if [ "$real_name" = "" ] 
   then
      echo "ERROR: Name must be provided for the key"
      return 1
   fi

   if [ "$email" = "" ] 
   then
      echo "ERROR: Email must be provided for the key"
      return 1
   fi

   if [ "$(upper $expire)" = "NEVER" ]
   then
      expire="0"
   elif [ "$expire" = "" ]
   then
      expire="1y"
   fi


   echo "Key-Type: RSA" > /var/tmp/test_pgp_key_${time_stamp}.dat
   echo "Key-Length: 2048" >> /var/tmp/test_pgp_key_${time_stamp}.dat
   echo "Subkey-Type: RSA" >> /var/tmp/test_pgp_key_${time_stamp}.dat
   echo "Subkey-Length: 2048" >> /var/tmp/test_pgp_key_${time_stamp}.dat
   echo "Name-Real: $real_name" >> /var/tmp/test_pgp_key_${time_stamp}.dat 
   echo "Name-Comment: $comment" >> /var/tmp/test_pgp_key_${time_stamp}.dat 
   echo "Name-Email: $email" >> /var/tmp/test_pgp_key_${time_stamp}.dat 
   echo "Expire-Date: $expire" >> /var/tmp/test_pgp_key_${time_stamp}.dat 
   echo "Passphrase: $(get_passphrase)" >> /var/tmp/test_pgp_key_${time_stamp}.dat

   gpg --batch --generate-key /var/tmp/test_pgp_key_${time_stamp}.dat

   rm /var/tmp/test_pgp_key_${time_stamp}.dat 2> /dev/null
   unset real_name
   unset email
   unset comment
   unset expire
   unset time_stamp

} # End - generate_gpg_key



function get_gpg_fingerprint
{
   typeset key_id=${1}
   typeset fingerprint=""
   typeset time_stamp="$(date +%Y%m%d%H%M%S)"

   gpg --list-keys --with-colons --fingerprint "$key_id" | grep fpr | awk -F: '{print $10}' > /var/tmp/gpg_keys_${time_stamp}.txt

   # Only return the first matching fingerprint
   while read result_line
   do
      fingerprint=$(echo $result_line)
      break
   done < /var/tmp/gpg_keys_${time_stamp}.txt

   echo $fingerprint

   rm /var/tmp/gpg_keys_${time_stamp}.txt 
   unset key_id
   unset fingerprint
   unset time_stamp

} # End - get_gpg_fingerprint



function get_gpg_key_expiration
{
   typeset fingerprint=${1}

   echo "$(gpg --list-keys ${fingerprint} | grep "expires:" | grep SCEA | awk -F: '{print $2}' | cut -c2-11)"

   unset fingerprint

} # End - get_gpg_key_expiration



function delete_gpg_key
{
   typeset key_id=${1}

   gpg --delete-secret-and-public-key --yes --batch $(get_gpg_fingerprint "${key_id}")


   unset key_id

} # End - delete_gpg_key



function sign_gpg_file
{
   typeset use_key=${1}
   typeset file_to_sign=${2}

   gpg --passphrase "$(get_passphrase)" --local-user ${use_key} --sign $file_to_sign

   unset use_key
   unset file_to_sign

} # End - sign_gpg_file



function export_public_key
{
   typeset recipient=${1}
   typeset key_file=${2}

   gpg --output $key_file --armor --export $recipient

   unset recipient
   unset key_file

} # End - export_public_key



function export_private_key
{
   typeset recipient=${1}
   typeset key_file=${2}

   gpg  --output $key_file --armor --batch --passphrase "$(get_passphrase)" --pinentry-mode loopback --export-secret-keys $(get_gpg_fingerprint "${recipient}")

   unset recipient
   unset key_file

} # End - export_private_key



function import_public_key
{
   typeset key_file=${1}

   gpg --import $key_file 

   unset key_file

} # End - import_public_key


function trust_gpg_key
{
   typeset key_id=${1}
   typeset use_key=${2}

   if [ "${use_key}" != "" ]
   then
      gpg --sign-key --batch --yes --passphrase "$(get_passphrase)" --pinentry-mode loopback --local-user $(get_gpg_fingerprint "${use_key}") $(get_gpg_fingerprint "${key_id}") 
   else
      gpg --sign-key --batch --yes --passphrase "$(get_passphrase)" --pinentry-mode loopback $(get_gpg_fingerprint "${key_id}") 
   fi

   unset key_id
   unset use_key

} # End - trust_gpg_key


function get_aop_directory
{

   # Get the parameters
   typeset -l server_name=${1:-"$(hostname)"}

   if [ "$server_name" = "ahcdbvm1" ]
   then
      echo "/u01/aopdir"
   else
      echo "/opt/aopdocs"
   fi

} # End - get_aop_directory



function add_db_connection
{
   # Get the parameters
   typeset -u db_user=${1}
   typeset db_password="${2}"
   typeset -u db_sid=${3:-ORCL}
   typeset connection_file=${4:-"${SIMPLIPIED_HOME}/.connect"}
   typeset encrypted_password="$(encrypt $db_password)"  # Multi-line

   # Record format
   # database_user: <db_user>
   # database_password:
   # -----BEGIN PGP MESSAGE-----
   #
   # hQEMA8izGvBxlvwfAQgAt.................R76RBJ6zqEvkB8lpgZSr86pcGH
   # -----END PGP MESSAGE-----
   # database_sid: <db_sid)
   #

   if [ "$(db_connection_exists $db_user $db_sid $connection_file)" = "YES" ] 
   then
      update_db_connection $db_user $db_password $db_sid $connection_file

   else
      # If the file does not exist, a new one will be created
      echo "database_user: $db_user" >> $connection_file
      echo "database_password:" >> $connection_file
      echo "$encrypted_password" >> $connection_file
      echo "database_sid: $db_sid" >> $connection_file
      echo "-----------------------------------------------------------------" \
         >> $connection_file
   fi


   unset db_user
   unset db_password
   unset db_sid
   unset connection_file
   unset encrypted_password

} # End - add_db_connection



function update_db_connection
{
   # Get the parameters
   typeset -u db_user=${1}
   typeset db_password="${2}"
   typeset -u db_sid=${3:-ORCL}
   typeset connection_file=${4:-"${SIMPLIPIED_HOME}/.connect"}
   typeset working_file="/var/tmp/temp_connect.txt"
   typeset users_file="/var/tmp/temp_connect_${db_user}.txt"
   typeset encrypted_password="$(encrypt $db_password)"  # Multi-line
   typeset -i correct_user=0

   # Record format
   # database_user: <db_user>
   # database_password:
   # -----BEGIN PGP MESSAGE-----
   #
   # hQEMA8izGvBxlvwfAQgAt.................R76RBJ6zqEvkB8lpgZSr86pcGH
   # -----END PGP MESSAGE-----
   # database_sid: <db_sid)
   #

   echo > $working_file
   echo > $users_file

   cat $connection_file | while read db_record
   do
      # Check if we've found the requested user ID
      if [ "$(echo $db_record | grep "database_user: ${db_user}")" != "" ]
      then
         correct_user=1
      fi

      # If not reading the requested user record, just copy it to the 
      # working file
      if [ $correct_user -eq 0 ]
      then
         echo $db_record >> $working_file
      else
         echo $db_record >> $users_file
      fi
      
      # If it's the correct user and we found the requested database, write the
      # user id, the new encrypted password and the database to the working 
      # file.
      #
      # If it wasn't the correct database, write the whole user file to the
      # to the working file.
      if [ "$(echo $db_record | grep "database_sid:")" != "" ]
      then
         if [ "$(echo $db_record | grep "database_sid: ${db_sid}")" != "" ]
         then
            echo "database_user: $db_user" >> $working_file
            echo "database_password:" >> $working_file
            echo "$encrypted_password" >> $working_file
            echo "database_sid: $db_sid" >> $working_file
            echo "-----------------------------------------------------------------" \
               >> $working_file
         else
            # Not the right record; put it back
            cat $users_file >> $working_file
         fi
         # Reset for the next record
         correct_user=0
         echo "-----------------------------------------------------------------" \
            > $users_file
      fi
   done

   mv $working_file $connection_file

   rm $users_file 2> /dev/null

   unset db_user
   unset db_password
   unset db_sid
   unset connection_file
   unset working_file
   unset users_file
   unset encrypted_password
   unset correct_user

} # End - update_db_connection


function db_connection_exists
{
   # Get the parameters
   typeset -u db_user=${1}
   typeset -u db_sid=${2:-ORCL}
   typeset connection_file=${3:-"${SIMPLIPIED_HOME}/.connect"}
   typeset -i correct_user=0
   typeset connection_exists="NO"

   # Record format
   # database_user: <db_user>
   # database_password:
   # -----BEGIN PGP MESSAGE-----
   #
   # hQEMA8izGvBxlvwfAQgAt.................R76RBJ6zqEvkB8lpgZSr86pcGH
   # -----END PGP MESSAGE-----
   # database_sid: <db_sid)
   #


   if [ -r $connection_file ]
   then
      while read db_record
      do
         # Check if we've found the requested user ID
         if [ "$(echo $db_record | grep "database_user:")" != "" ]
         then
            if [ "$(echo $db_record | grep "database_user: ${db_user}")" != "" ]
            then
               correct_user=1
            else
               correct_user=0
            fi
         fi

         # If it's the correct user, keep looking until we found the requested database.
         #
         # If it wasn't the correct database, reset correct_user
         if [ "$(echo $db_record | grep "database_sid:")" != "" ]
         then
            if [ "$(echo $db_record | grep "database_sid: ${db_sid}")" != "" ] && \
               [ $correct_user -eq 1 ]
            then
               connection_exists="YES"
               break
            else
               correct_user=0
            fi
         fi
      done <<< "$(cat $connection_file)"
   fi

   echo $connection_exists

   unset db_user
   unset db_sid
   unset connection_file
   unset correct_user
   unset connection_exists

} # End - db_connection_exists




function get_db_password
{
   # Get the parameters
   typeset -u db_user=${1}
   typeset -u db_sid=${2:-ORCL}
   typeset connection_file=${3:-"${SIMPLIPIED_HOME}/.connect"}
   typeset db_password
   typeset -i reading_password=0
   typeset -i password_found=0
   typeset -i correct_user=0
   typeset -i correct_database=0
   typeset working_file

   # Record format
   # database_user: <db_user>
   # database_password:
   # -----BEGIN PGP MESSAGE-----
   #
   # hQEMA8izGvBxlvwfAQgAt..........
   #.......R76RBJ6zqEvkB8lpgZSr86pcGH
   # -----END PGP MESSAGE-----
   # database_sid: <db_sid)
   #
   cat $connection_file | while read db_record
      do
         # If we have the correct user ID and have started reading the PGP 
         # encrypted password lines, add them to the file
         if [ $reading_password -eq 1 ] && [ $correct_user -eq 1 ]
         then
            echo $db_record >> $working_file
         fi

         # Start reading the PGP encrypted password (if this is the requested 
         # user ID)
         if [ "$db_record" = "-----BEGIN PGP MESSAGE-----" ] && [ $correct_user -eq 1 ]
         then
            reading_password=1
            working_file=/var/tmp/temp_db_password_${db_user}_${db_sid}
            echo $db_record > $working_file   # Initialize the file
         fi 

         # Stop reading the PGP encrypted password (if this was for the
         # requested user ID). 
         if [ "$db_record" = "-----END PGP MESSAGE-----" ] && \
            [ $correct_user -eq 1 ]
         then
            reading_password=0
            password_found=1
         fi

         # Check if we've found the requested user ID
         if [ "$(echo $db_record | awk -F: '{print $2}' | xargs)" = "$db_user" ]
         then
            correct_user=1
         fi 

         # Check if this is the requested database (if we're processing the
         # requested user ID)
         if [ "$(echo $db_record | awk -F: '{print $2}' | xargs)" = "$db_sid" ] && \
            [ $correct_user -eq 1 ]
         then
            correct_database=1
         fi

         # If we loaded the PGP encrypted password for the requested user ID
         # and database, decrypt it and stop looking.
         if [ $password_found -eq 1 ] && [ $correct_user -eq 1 ] && [ $correct_database -eq 1 ]
         then
            db_password=$(cat $working_file) # Works using temp file
            echo $(decrypt "$db_password")
            break
         fi

      done

   rm $working_file 2> /dev/null
} # End - get_db_password


# End - pi_common.sh
