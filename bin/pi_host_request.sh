#!/bin/bash
#-----------------------------------------------------------------------------
#
#   PROCEDURE:    pi_host_request.sh
#
#   PURPOSE:      This script contains the Host Request functions.
#
#   PARAMETERS:   None.
#
#   USAGE:        From the command-line, type:
#                 . ./pi_host_request.sh
#
#   AUTHOR:       <PERSON>re
#
#
# Copyright (c) 2025 Kevin <PERSON>re
# All rights reserved.
#
# This software is proprietary and confidential. Unauthorized copying of this
# file, via any medium, is strictly prohibited without the express permission
# of the author or owning organization.
#
#-----------------------------------------------------------------------------

# Mark the common functions as defined
# To force this script to re-execute, use:
#    unset PI_HOST_REQUEST_FUNCTIONS
if [ "${PI_HOST_REQUEST_FUNCTIONS:=Undefined}" = "Defined" ]
then
   echo "The Host Request functions are already defined."
else
   PI_HOST_REQUESTD_FUNCTIONS="Defined"
   export PI_HOST_REQUEST_FUNCTIONS
fi

. $(dirname $0)/pi_common.sh


# Host Request Support ---------------------------------------------------------
function get_new_host_requests
{

   echo $(run_sql_func "host_request.get_new_requests") 

} # End - get_new_host_requests


function get_script_name
{
   typeset request_id=$1
   typeset json_string=$2

   echo $json_string | jq -r  ".host_requests[] | select(.id | . and contains(${request_id})) | .script_name"

   unset request_id
   unset json_string

} # End - get_script_name


function get_parameters
{
   typeset request_id=$1
   typeset json_string=$2
   typeset parameters


   parameters=$(echo $json_string | jq -r  ".host_requests[] | select(.id | . and contains(${request_id})) | .parameter_string")

   if [ "$parameters" = "null" ]
   then
      echo 
   else
      echo $parameters
   fi

   unset request_id
   unset json_string
   unset parameters

} # End - get_parameters



function mark_request_processing
{
   typeset request_id=$1
   typeset sql_results

   sql_results=$(run_sql_proc "host_request.mark_status(${request_id}, 'PROCESSING')")

   if [ "$(echo $sql_results | grep 'PL/SQL procedure successfully completed.')" = "" ]
   then
      echo $sql_results
   fi

   unset request_id
   unset sql_results

} # End - mark_request_processing



function mark_request_complete
{
   typeset request_id=$1
   typeset sql_results

   # Add check for current "PROCESSING" status?
   sql_results=$(run_sql_proc "host_request.mark_status(${request_id}, 'COMPLETE')")

   if [ "$(echo $sql_results | grep 'PL/SQL procedure successfully completed.')" = "" ]
   then
      echo $sql_results
   fi

   unset request_id
   unset sql_results

} # End - mark_request_complete


function load_log_file
{
   typeset request_id=$1
   typeset log_file=$2
   typeset instruction_file="/var/tmp/temp_pi_host_request_log_stg_$(date +%Y%m%d%H%M%S).dat"
   typeset sql_results

   if [ -r $log_file ]
   then
      # Creating the staging instruction file
      echo "${request_id},${log_file}" > $instruction_file

      # Run SQL*Loader
      sql_results=$(run_sql_loader -d $instruction_file pi_host_request_log_stg.ctl)
      if [ "$(echo $sql_results | grep '1 Row successfully loaded.')" = "" ]
      then
         # Show the SQL*Loader log file
         cat /var/tmp/pi_host_request_log_stg.log
      fi


      # Copy the log file contents into the execution_results field
      sql_results=$(run_sql_proc "host_request.copy_staged_results(${request_id})")

      if [ "$(echo $sql_results | grep 'PL/SQL procedure successfully completed.')" = "" ]
      then
         echo $sql_results
      fi
   else
      echo "ERROR: $log_file not found or not readable."
   fi


   rm $instruction_file 2> /dev/null
   unset request_id
   unset instruction_file
   unset log_file
   unset sql_results

} # End - mark_request_complete


#get_parameters 6 "$(get_new_host_requests)"
#get_parameters 77 "$(get_new_host_requests)"
#get_script_name 59 '{"host_requests":[{"id":59,"script_name":"pi_test_dummy.sh","parameter_string":null}]}'

#mark_request_processing 8
#mark_request_complete 8

#load_log_file 1 /var/tmp/pi_run_host_command_20250326093022.log

# End - pi_host_request.sh
