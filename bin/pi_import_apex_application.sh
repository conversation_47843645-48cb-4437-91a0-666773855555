#!/bin/bash
#-----------------------------------------------------------------------------
#
#   PROCEDURE:    pi_import_apex_application.sh
#
#   PURPOSE:      This script is used to import an APEX application. 
#
#   PARAMETERS:   import_file      - The APEX application export file to import
#                 application_id   - The optional target application ID
#
#   USAGE:        From the command-line, type:
#                    ./pi_import_apex_application.sh <import_file> [<application_id>]
#
#   NOTES:        The script imports from the apex git subdirectory.
#                 if not specified, the application file name will be imported 
#                 to the existing matching application ID.
#
#   AUTHOR:       <PERSON>
#
#
# Copyright (c) 2025 Kevin LeQuire
# All rights reserved.
#
# This software is proprietary and confidential. Unauthorized copying of this
# file, via any medium, is strictly prohibited without the express permission
# of the author or owning organization.
#
#-----------------------------------------------------------------------------


# Include the "common" functions
. $(dirname $0)/pi_common.sh


typeset import_file=$1
typeset application_id=$2
typeset response
typeset application_name
typeset -i error_status=0

if [ "$import_file" = "" ]
then
   echo "ERROR: APEX application file provided."
   echo
   echo "USAGE: "
   echo "   ./pi_import_apex_application.sh <import_file> [<application_id>]"
   echo
   error_status=1
elif [ ! -r $(dirname $0)/../apex/${import_file} ]
then
   echo "ERROR: APEX application file ${import_file} not found in $(dirname $0)/../apex/."
   echo
   echo "USAGE: "
   echo "   ./pi_import_apex_application.sh <import_file> [<application_id>]"
   echo
   error_status=1
fi

if [ $error_status -eq 0 ]
then
   # If the application ID was not given, get it from the import file 
   if [ "$application_id" = "" ]
   then

      application_id=$(grep p_default_application_id $(dirname $0)/../apex/${import_file} | awk -F\> '{print $2}')
      if [ "$application_id" = "" ]
      then
         echo "ERROR: Default APEX application ID was not found in $(dirname $0)/../apex/${import_file}."
         echo
         error_status=1
      fi
   fi
fi



if [ $error_status -eq 0 ]
then

   # Import the application
   application_name=$(grep get_application_name $(dirname $0)/../apex/${import_file} | awk -F\' '{print $2}')
   echo "Importing $(dirname $0)/../apex/${import_file} into application $application_name (${application_id})..."
   echo

   curl -X PUT -u "$(get_db_con apps | tr "\/" "\:")" -H "Content-Type:application/sql" --data-binary @$(dirname $0)/../apex/${import_file} http://apex-app.rapidpie.com:8080/ords/rapidpie/_/db-api/stable/apex/workspaces/rapidpie/applications/${application_id} 2> /dev/null

   echo
   echo "...Done."
fi


# NOTES:
# List of workspace applications
#response=$(curl -X GET -u "$(get_db_con apps | tr "\/" "\:")" http://apex-app.rapidpie.com:8080/ords/rapidpie/_/db-api/stable/apex/workspaces/rapidpie/applications/)

# Look at importing individual components
# https://docs.oracle.com/en/database/oracle/oracle-rest-data-services/24.4/orrst/op-apex-applications-application_id-post.html
# {
#     "components": [
#         "LOV:123",
#         "PAGE:321"
#     ]
# }
# Look at 
# https://blogs.oracle.com/apex/post/oracle-apex-app-deployments-made-easy-use-the-ords-rest-apis
# for an example.




# Clean up
unset import_file
unset application_id
unset response
unset application_name
unset error_status

# Complete
