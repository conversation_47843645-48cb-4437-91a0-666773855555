#!/bin/bash
#-----------------------------------------------------------------------------
#
#   PROCEDURE:    pi_host_request_daemon.sh
#
#   PURPOSE:      This script continuously checks the PI_HOST_REQUESTS table
#                 for host command request. If found, the request is executed 
#                 and then the table is updated with the status and log file 
#                 contents.
#
#   PARAMETERS:   None.
#
#   USAGE:        From the command-line, type:
#                    ./pi_host_request_daemon.sh 
#                 Or, to run as a daemon process:
#                    nohup ./pi_host_request_daemon.sh &
#
#   AUTHOR:       <PERSON>
#
#
# Copyright (c) 2025 Kevin LeQuire
# All rights reserved.
#
# This software is proprietary and confidential. Unauthorized copying of this
# file, via any medium, is strictly prohibited without the express permission
# of the author or owning organization.
#
#-----------------------------------------------------------------------------


# Include the "common" functions
unset PI_COMMON_FUNCTIONS
unset PI_HOST_REQUEST_FUNCTIONS
. $(dirname $0)/pi_common.sh
. $(dirname $0)/pi_host_request.sh

# Local variables
typeset timestamp="$(date +%Y%m%d%H%M%S)"
typeset log_file=/var/tmp/pi_host_request_daemon_${timestamp}.log
typeset new_requests
typeset request_id
typeset script_name
typeset parameters
typeset run_results
typeset command_log

# Add the standard header information to the log file
printf "%-10s %s\n" "Program:" "$(basename $0)" | tee $log_file
printf "%-10s %s\n" "Log File:" "$log_file" | tee -a $log_file
printf "%-10s %s\n\n\n" "Execution:" "$(date)" | tee -a $log_file


until [ -r /var/tmp/pi_stop_host_request_daemon ]
do

   # Get a list of "new" requests from the database
   new_requests=$(get_new_host_requests)

   # Process each request
   #echo $new_requests | jq .
   # echo $new_requests | jq '.host_requests[]'
   for request_id in $(echo $new_requests | jq '.host_requests[].id')
   do
      echo "Request ID: $request_id" | tee -a $log_file
      script_name=$(get_script_name $request_id "${new_requests}")
      parameters=$(get_parameters $request_id "${new_requests}")
      echo "Running ${script_name} ${parameters}" | tee -a $log_file

      # a. Mark as running (Status)
      mark_request_processing $request_id

      # b. Execute (call pi_run_host_command.sh)
      # The shUnit2 call in the test scripts doesn't handle parameters (even "").
      if [ "$parameters" = "" ]
      then
         run_results=$($(dirname $0)/pi_run_host_command.sh "${script_name}")
      else
         run_results=$($(dirname $0)/pi_run_host_command.sh "${script_name} \"${parameters}\"")
      fi
      echo ${run_results} | tee -a $log_file
      sleep 2s


      # c. Get the log file name and add the contents to the pi_host_requests table
      command_log=$(echo $run_results | grep "Check " | awk '{print $2}')
      cat $command_log | tee -a $log_file
      load_log_file $request_id $command_log


      # d. Update PI_HOST_REQUESTS with the status (success/error) and the 
      #    contents of the log file.
      mark_request_complete $request_id

      echo "----------------------------" | tee -a $log_file
      echo | tee -a $log_file

   done




   echo "... Done processing new requests (if any). $(date)"
   sleep 20s #1m

done


printf "\n\n%s   %s\n\n\n" "pi_host_request_daemon.sh shutting down.:" "$(date)" | tee -a $log_file

# Clean-up
rm /var/tmp/pi_stop_host_request_daemon
unset timestamp
unset log_file
unset new_requests
unset request_id
unset script_name
unset parameters
unset run_results
unset command_log

# Complete
