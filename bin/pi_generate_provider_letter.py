#!/bin/python3
#-----------------------------------------------------------------------------
#
#   Script:      pi_generate_provider_letter.py
#
#   Description: This script is used to generate the PDF Provider letter file
#                for the given letter.
#
#   Parameters:  letter_number - The NotificationRefNumber from 
#                                NTF_NotificationDeliveries. 
#
#   Usage:       python3 pi_generate_provider_letter.py --letter_number <letter_number>
#                or
#                ./pi_generate_provider_letter.py --letter_number <letter_number>
#
#   Author:      <PERSON>
#
#
# Copyright (c) 2025 <PERSON>
# All rights reserved.
#
# This software is proprietary and confidential. Unauthorized copying of this
# file, via any medium, is strictly prohibited without the express permission
# of the author or owning organization.
#
#-----------------------------------------------------------------------------

from docxtpl import DocxTemplate
import subprocess
import os
import cx_Oracle
import argparse
import csv



def parse_args():
    parser = argparse.ArgumentParser(description="Generate provider letter.")
    parser.add_argument(
        "--letter_number",
        required=True,
        help="Letter number to generate (e.g., 2025033000170)"
    )
    return parser.parse_args()




def fill_provider_template(letter_data, output_dir="/opt/aopdocs"):
    home_directory = os.getenv("HOME")
    template_path = f"{home_directory}/rapidpie/Reports/Provider_Overpayment_Template.docx"
    os.makedirs(output_dir, exist_ok=True)

    for letter in letter_data:
        doc = DocxTemplate(template_path)
        doc.render(letter)

        base_filename = f"{letter['sourcesystemcode']}_{letter['providercode']}_{letter['provider_short_name']}_{letter['letternumber']}"
        output_docx = os.path.join(output_dir, base_filename + ".docx")
        output_pdf = os.path.join(output_dir, base_filename + ".pdf")

        doc.save(output_docx)

        try:
            subprocess.run([
                "libreoffice",
                "--headless",
                "--convert-to", "pdf",
                "--outdir", output_dir,
                output_docx
            ], check=True)
            print(f"PDF generated: {output_pdf}")
        except subprocess.CalledProcessError as e:
            print("Failed to convert DOCX to PDF:", e)

        if os.path.exists(output_docx):
            os.remove(output_docx)



def fetch_letter_data(letter_number):
    """
    Connects to Oracle DB, retrieves the Provider letter information and the
    associated Claims.

    Args:
        letter_number (str)     : The Provider letter number to fetch.

    Returns:
        letter_data (List[dict]): The letter attributes including an 
                                  imbedded list of claims.
    """
    # Oracle DB connection details
    dsn = cx_Oracle.makedsn("apex-app.rapidpie.com", 1521, service_name="orcl")
    username = "apps"
    password = get_db_password("apps")

    connection = cx_Oracle.connect(user=username, password=password, dsn=dsn)
    cursor = connection.cursor()


    # Call the PL/SQL function to get the returned sys_refcursor
    letter_cursor = cursor.callfunc("letters.get_provider_letter_cursor", cx_Oracle.CURSOR, [letter_number])

    letter_data = []

    for letter_row in letter_cursor:
        letter = {
            "letternumber": letter_row[0],
            "sourcesystemcode": letter_row[1],
            "letterdate": letter_row[2],       
            "providertin": letter_row[3],
            "providerattention": letter_row[4],
            "providergroupname": letter_row[5],
            "provideraddressline1": letter_row[6],
            "provideraddressline2": letter_row[7],
            "provideraddressline2_available": letter_row[8],
            "providercity": letter_row[9],
            "providerstatecode": letter_row[10],
            "providerzip": letter_row[11],
            "totaloverpayment": letter_row[12],
            "appealpobox": letter_row[13],
            "appealaddressline1": letter_row[14],
            "appealaddressline2": letter_row[15],
            "appealaddressline2_available": letter_row[16],
            "appealcity": letter_row[17],
            "appealstatecode": letter_row[18],
            "appealzipcode": letter_row[19],
            "blurb": letter_row[20],
            "providercode": letter_row[21],
            "provider_short_name": letter_row[22],
            "claims": []
        }

        # Fetch the claims for the letter
        claims_cursor = cursor.callfunc("letters.get_provider_letter_details_cursor", cx_Oracle.CURSOR, [letter["letternumber"]])
        for claim_row in claims_cursor:
            letter["claims"].append({
                "notificationdeliveryid": claim_row[0],
                "claimnumber": claim_row[1],
                "claimlinenumber": claim_row[2],
                "membername": claim_row[3],
                "memberid": claim_row[4],
                "patientaccountnumber": claim_row[5],
                "dateofservicefrom": claim_row[6],
                "dateofserviceto": claim_row[7],
                "totalbillamount": claim_row[8],
                "paidbyplan": claim_row[9],
                "claimpaiddate": claim_row[10],
                "checknumber": claim_row[11],
                "overpaidamount": claim_row[12],
                "overpaymentdescription": claim_row[13],
                "overpaymentconcept": claim_row[14],
                "submitdate": claim_row[15],
                "statuscode": claim_row[16],
                "hspceffdate": claim_row[17],
                "hspcenddate": claim_row[18]
            })
        claims_cursor.close()

        letter_data.append(letter)


    letter_cursor.close()
    cursor.close()
    connection.close()
    return letter_data



def get_db_password(user_id):
    user_list = []
    home_directory = os.getenv("HOME")
    filepath = f"{home_directory}/.connect.txt"
    db_password = None

    with open(filepath, mode='r', newline='') as file:

        # Filter out comment lines
        filtered_lines = (line for line in file if not line.strip().startswith('#'))

        reader = csv.DictReader(filtered_lines, delimiter=':')
        for row in reader:
            user_list.append(row)

        for entry in user_list:
            if entry.get("test_user") == user_id:
                db_password = entry.get("password")

    return db_password




if __name__ == "__main__":

    args = parse_args()
    letter_number = args.letter_number

    data = fetch_letter_data(letter_number)
    
    fill_provider_template(data)


# Complete
