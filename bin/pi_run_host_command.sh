#!/bin/bash
#-----------------------------------------------------------------------------
#
#   PROCEDURE:    pi_run_host_command.sh
#
#   PURPOSE:      This script is a wrapper for running the given host command.
#                 It is used to support running host commands from PL/SQL.
#
#   PARAMETERS:   host_command - The command/script to execute. (If parameters
#                                are needed, enclose the command within quotes.
#
#   USAGE:        From the command-line, type:
#                    ./pi_run_host_command.sh "<host_command>"
#
#   AUTHOR:       <PERSON>
#
#
# Copyright (c) 2025 <PERSON>
# All rights reserved.
#
# This software is proprietary and confidential. Unauthorized copying of this
# file, via any medium, is strictly prohibited without the express permission
# of the author or owning organization.
#
#-----------------------------------------------------------------------------


# Include the "common" functions
#unset PI_COMMON_FUNCTIONS
#. $(dirname $0)/pi_common.sh

# Local variables
typeset host_command="${1}"
typeset timestamp="$(date +%Y%m%d%H%M%S)"
typeset log_file=/var/tmp/pi_run_host_command_${timestamp}.log
typeset command_file=/var/tmp/pi_run_host_command_${timestamp}.sh

if [ "${host_command}" = "" ]
then
   host_command="echo 'ERROR: No command/script given'"
fi


# Add the standard header information to the log file
printf "%-10s %s\n" "Program:" "$(basename $0)" > $log_file
printf "%-10s %s\n" "Log File:" "$log_file" >> $log_file
printf "%-10s %s\n" "Execution:" "$(date)" >> -a $log_file

printf "\n%s\n" "Parameters:" >> $log_file
printf "   %-18s %s\n\n\n" "Command:" "${host_command}" >> $log_file

echo "-- Start ---------------------------------------------------" >> $log_file

# Run the host command
echo "${host_command}" > $command_file
chmod +x $command_file

. $command_file >> $log_file


echo "-- Complete ------------------------------------------------" >> $log_file

echo "Check $log_file for results."


# Clean-up
rm $command_file 2> /dev/null
unset host_command
unset timestamp
unset log_file
unset command_file

# Complete

