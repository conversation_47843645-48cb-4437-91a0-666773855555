#!/bin/bash
#-----------------------------------------------------------------------------
#
#   PROCEDURE:    pi_test.sh
#
#   PURPOSE:      This script is used to support testing.
#
#   PARAMETERS:   script_name - The script_name to report
#                 parameters  - The parameter string for the script
#
#   USAGE:        From the command-line, type:
#                    ./pi_test.sh <script_name> <parameters>
#
#   AUTHOR:       <PERSON>
#
#
# Copyright (c) 2025 <PERSON>
# All rights reserved.
#
# This software is proprietary and confidential. Unauthorized copying of this
# file, via any medium, is strictly prohibited without the express permission
# of the author or owning organization.
#
#-----------------------------------------------------------------------------



# Get the parameter(s)
typeset script_name=${0}
typeset parameters="${1:-""}"

echo "Script Name: $script_name"
echo "Parameters:  $parameters"

unset script_name
unset parameters

# Complete
