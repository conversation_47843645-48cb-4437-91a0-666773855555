#!/bin/bash
#-----------------------------------------------------------------------------
#
#   PROCEDURE:    test_common_db_pkg.sh
#
#   PURPOSE:      This script executes the COMMON database package unit
#                 tests.
#
#   PARAMETERS:   None.
#
#   USAGE:        From the command-line, type:
#                    test_common_db_pkg.sh
#
#   AUTHOR:       <PERSON>
#
#
# Copyright (c) 2025 <PERSON>re
# All rights reserved.
#
# This software is proprietary and confidential. Unauthorized copying of this
# file, via any medium, is strictly prohibited without the express permission
# of the author or owning organization.
#
#-----------------------------------------------------------------------------


# Include the "common" functions
unset PI_COMMON_FUNCTIONS
. $(dirname $0)/pi_common.sh


# Call the utPLSQL test package
run_sql_proc "ut.run('test_common')"


# Complete
