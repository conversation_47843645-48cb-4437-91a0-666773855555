#!/bin/python3
#-----------------------------------------------------------------------------
#
#   Script:      pi_generate_claim_letter.py
#
#   Description: This script is used to generate the PDF Provider letter file
#                for the given letter.
#
#   Parameters:  letter_number - The NotificationRefNumber from 
#                                NTF_NotificationDeliveries. 
#
#   Usage:       python3 pi_generate_claim_letter.py --letter_number <letter_number> \
#                   --claim_number <claim_number>
#                or
#                ./pi_generate_claim_letter.py --letter_number <letter_number> \
#                   --claim_number <claim_number>
#
#   Author:      <PERSON>
#
#
# Copyright (c) 2025 Kevin <PERSON>
# All rights reserved.
#
# This software is proprietary and confidential. Unauthorized copying of this
# file, via any medium, is strictly prohibited without the express permission
# of the author or owning organization.
#
#-----------------------------------------------------------------------------

from docxtpl import DocxTemplate
import subprocess
import os
import cx_Oracle
import argparse
import csv



def parse_args():
    parser = argparse.ArgumentParser(description="Generate claim letter.")
    parser.add_argument(
        "--letter_number",
        required=True,
        help="Letter number to generate (e.g., 2025033000170)"
    )
    parser.add_argument(
        "--claim_number",
        required=True,
        help="Claim number to used to generate the letter (e.g., 20241029821052100079 )"

    )
    return parser.parse_args()




def fill_claims_template(letter_data, output_dir="/opt/aopdocs"):
    home_directory = os.getenv("HOME")
    template_path = f"{home_directory}/rapidpie/Reports/Claim_Overpayment_Template.docx"
    os.makedirs(output_dir, exist_ok=True)

    for letter in letter_data:
        doc = DocxTemplate(template_path)
        doc.render(letter)

        base_filename = f"{letter['sourcesystemcode']}_{letter['claimnumber']}_{letter['membershortname']}"
        print(f"base_filename: {base_filename}")
        output_docx = os.path.join(output_dir, base_filename + ".docx")
        output_pdf = os.path.join(output_dir, base_filename + ".pdf")

        doc.save(output_docx)

        try:
            subprocess.run([
                "libreoffice",
                "--headless",
                "--convert-to", "pdf",
                "--outdir", output_dir,
                output_docx
            ], check=True)
            print(f"PDF generated: {output_pdf}")
        except subprocess.CalledProcessError as e:
            print("Failed to convert DOCX to PDF:", e)

        if os.path.exists(output_docx):
            os.remove(output_docx)



def fetch_letter_data(letter_number, claim_number):
    """
    Connects to Oracle DB, retrieves the Provider letter information and the
    associated Claims.

    Args:
        letter_number (str)     : The Provider letter number to fetch.

    Returns:
        letter_data (List[dict]): The letter attributes including an 
                                  imbedded list of claim details.
    """
    # Oracle DB connection details
    dsn = cx_Oracle.makedsn("apex-app.rapidpie.com", 1521, service_name="orcl")
    username = "apps"
    password = get_db_password("apps")

    connection = cx_Oracle.connect(user=username, password=password, dsn=dsn)
    cursor = connection.cursor()


    # Call the PL/SQL function to get the returned sys_refcursor
    letter_cursor = cursor.callfunc("letters.get_claim_letter_cursor", cx_Oracle.CURSOR, [letter_number, claim_number])

    letter_data = []

    for letter_row in letter_cursor:
        letter = {
            "letternumber": letter_row[0],
            "sourcesystemcode": letter_row[1],
            "letterdate": letter_row[2],
            "providertin": letter_row[3],
            "providerattention": letter_row[4],
            "providergroupname": letter_row[5],
            "provideraddressline1": letter_row[6],
            "provideraddressline2": letter_row[7],
            "providercity": letter_row[8],
            "providerstatecode": letter_row[9],
            "providerzip": letter_row[10],
            "appealpobox": letter_row[11], 
            "appealaddressline1": letter_row[12], 
            "appealaddressline2": letter_row[13], 
            "appealcity": letter_row[14], 
            "appealstatecode": letter_row[15], 
            "appealzipcode": letter_row[16], 
            "blurb": letter_row[17], 
            "membername": letter_row[18], 
            "memberid": letter_row[19], 
            "memberdob": letter_row[20], 			
            "claimnumber": letter_row[21], 			
            "patientaccountnumber": letter_row[22], 
            "dateofservicefrom": letter_row[23], 
            "dateofserviceto": letter_row[24], 
            "totalbillamount": letter_row[25], 
            "paidbyplan": letter_row[26], 
            "claimpaiddate": letter_row[27], 
            "checknumber": letter_row[28], 
            "overpaidamount": letter_row[29], 
            "hspceffdate": letter_row[30], 
            "hspcenddate": letter_row[31],
            "membershortname": letter_row[32],
            "details": []
        }

        # Fetch the details for the letter
        details_cursor = cursor.callfunc("letters.get_claim_letter_details_cursor", cx_Oracle.CURSOR, [letter_number, claim_number])
        for detail_row in details_cursor:
            letter["details"].append({
                "overpaymentdescription": detail_row[0]
            })
        details_cursor.close()

        letter_data.append(letter)

    letter_cursor.close()
    cursor.close()
    connection.close()
    return letter_data



def get_db_password(user_id):
    user_list = []
    home_directory = os.getenv("HOME")
    filepath = f"{home_directory}/.connect.txt"
    db_password = None

    with open(filepath, mode='r', newline='') as file:

        # Filter out comment lines
        filtered_lines = (line for line in file if not line.strip().startswith('#'))

        reader = csv.DictReader(filtered_lines, delimiter=':')
        for row in reader:
            user_list.append(row)

        for entry in user_list:
            if entry.get("test_user") == user_id:
                db_password = entry.get("password")

    return db_password




if __name__ == "__main__":

    args = parse_args()
    letter_number = args.letter_number
    claim_number = args.claim_number

    print(f"Letter Number: {letter_number}")
    print(f"Claim Number:  {claim_number}")

    data = fetch_letter_data(letter_number, claim_number)
    print(data) 
    fill_claims_template(data)


# Complete
