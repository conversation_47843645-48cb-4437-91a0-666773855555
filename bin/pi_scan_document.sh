#!/bin/bash
#-----------------------------------------------------------------------------
#
#   PROCEDURE:    pi_scan_document.sh
#
#   PURPOSE:      This script is used to upload the given document file and
#                 it's text lines to the database (PI_SCANNED_DOCUMENTS and 
#                 PI_SCANNED_DOCUMENT_LINES).  
#
#   NOTES:        The script uses the Tasseract OCR application to 
#                 scan the image files (PNG, JPEG, or TIFF) and pdfinfo for i
#                 PDF files.
#
#   PARAMETERS:   document_file   - Supports PDF and PNG, JPEG, or TIFF image 
#                                   files.
#
#   USAGE:        From the command-line, type:
#                    ./pi_scan_document.sh <document_file>
#
#   AUTHOR:       <PERSON>
#
#
# Copyright (c) 2025 Kevin <PERSON>re
# All rights reserved.
#
# This software is proprietary and confidential. Unauthorized copying of this
# file, via any medium, is strictly prohibited without the express permission
# of the author or owning organization.
#
#-----------------------------------------------------------------------------


# Include the "common" functions
unset PI_COMMON_FUNCTIONS
. $(dirname $0)/pi_common.sh


typeset document_file=$1
typeset output_file
typeset -u file_extension=${document_file##*.}
typeset output_directory="/opt/inbound"
typeset -i document_id
typeset document_record
typeset scrubbed_record
typeset -i line_number=0


if [ "$document_file" = "" ]
then
   echo "ERROR: No document provided."
   echo
   echo "USAGE: "
   echo "   pi_scan_document.sh <document_file>"
   echo
   exit 1
fi


# Set the output file name to the document_file without the path or extension
output_file=${document_file##*/}
output_file=${output_file%.*}



# Check if the document file exists
if [ -r $document_file ]
then

   case $file_extension in

      JPG | JPEG | TFF | PNG)

         # Create a PDF file of the image file
         tesseract $document_file /var/tmp/$output_file --psm 11 -c preserve_interword_spaces=1 pdf 
   
         mv /var/tmp/${output_file}.pdf $output_directory
         chmod 666 ${output_directory}/${output_file}.pdf

         # Upload the PDF version
         run_sql_proc "letters.load_scanned_file('${document_file##*/}', '${output_file}.pdf')"


         # Get the resulting Document ID
         document_id=$(run_sql_func "letters.get_scanned_document_id('${document_file##*/}', '${output_file}.pdf')")

         echo "Document ID: $document_id"
         echo

         # Scan the file and write the text to the database
         tesseract $document_file - -c preserve_interword_spaces=1 | while read document_record
         do
            line_number=line_number+1
            # Remove any single quotes
            scrubbed_record=$(echo $document_record | tr -d \')
            echo "$line_number ->: $scrubbed_record"
            run_sql_proc "letters.load_scanned_line($document_id, $line_number, '$scrubbed_record')"
         done
      ;;

      PDF)
         # Copy the file to the Inbound directory
         cp $document_file $output_directory

         # Upload the PDF
         run_sql_proc "letters.load_scanned_file('${document_file##*/}', '${document_file##*/}')"

         # Get the resulting Document ID
         document_id=$(run_sql_func "letters.get_scanned_document_id('${document_file##*/}', '${document_file##*/}')")
         echo "Document ID: $document_id"
         echo

         # Use "pdfinfo -struct-text" to strip-out the quoted text lines from 
         # the PDF document and write the text to the database.

         # First check if pdfinfo will find any text. If not, it was probably 
         # scanned to a pdf. Use Tessarack (OCR) to scan it to a temporary pdf 
         # and then get the text.
         if [ "$(pdfinfo -struct-text $document_file)" = "" ] 
         then
            echo "No plain text found in ${document_file}. Converting to an image file."

            # Convert the original PDF file to and image file
            pdftoppm -jpeg -r 300 $document_file ${output_directory}/${output_file}

            
            # Scan the file and write the text to the database
            tesseract ${output_directory}/${output_file}-1.jpg - -c preserve_interword_spaces=1 | while read document_record
            do
               line_number=line_number+1
               # Remove any single quotes
               scrubbed_record=$(echo $document_record | tr -d \')
               echo "$line_number ->: $scrubbed_record"
               run_sql_proc "letters.load_scanned_line($document_id, $line_number, '$scrubbed_record')"
            done


         else
            echo "${document_file} contains plain text. Loading it."

            pdfinfo -struct-text ${document_file} | grep \" | while read document_record
            do
               line_number=line_number+1
               # Remove any single quotes
               scrubbed_record=$(echo $document_record | tr -d \')
               echo "$line_number ->: $scrubbed_record"
               run_sql_proc "letters.load_scanned_line($document_id, $line_number, '$scrubbed_record')"
            done
         fi


      ;;
      *) echo "ERROR: $file_extension files are not supported."
         ;;
   esac

else
   echo "ERROR: $document_file not found or is not readable."
   exit 1
fi

# Clean up
unset document_file
unset output_file
unset file_extension
unset output_directory
unset document_id
unset document_record
unset scrubbed_record
unset line_number


# Complete
