#!/bin/bash
#-----------------------------------------------------------------------------
#
#   PROCEDURE:    pi_export_apex_application.sh
#
#   PURPOSE:      This script is used to export an APEX application. 
#
#   PARAMETERS:   application_id   - The APEX application ID to export
#                 export_file      - The optional export file name.
#
#   USAGE:        From the command-line, type:
#                    ./pi_export_apex_application.sh <application_id> [<export_file>]
#
#   NOTES:        The script exports to the apex subdirectory so it can be 
#                 loaded into git.
#                 if not specified, the export file name will be:
#                    application_alias.sql
#
#   AUTHOR:       <PERSON>
#
#
# Copyright (c) 2025 Kevin LeQuire
# All rights reserved.
#
# This software is proprietary and confidential. Unauthorized copying of this
# file, via any medium, is strictly prohibited without the express permission
# of the author or owning organization.
#
#-----------------------------------------------------------------------------


# Include the "common" functions
. $(dirname $0)/pi_common.sh


typeset application_id=$1
typeset export_file=$2
typeset response
typeset application_name
typeset application_alias

if [ "$application_id" = "" ]
then
   echo "ERROR: No Application ID provided."
   echo
   echo "USAGE: "
   echo "   ./pi_export_apex_application.sh <application_id> [<export_file>]"
   echo
   exit 1
fi



# Check if the APEX application exists
response=$(curl -X GET -u "$(get_db_con apps | tr "\/" "\:")" http://apex-app.rapidpie.com:8080/ords/rapidpie/_/db-api/stable/apex/applications/${application_id} 2> /dev/null) 
application_name=$(echo $response | jq '.items | .[] | .application_name' | tr -d \")

if [ "$application_name" = "" ]
then
   echo "ERROR: APEX application $application_id was not found."
   echo $response
else

   # If the export file name was not given, set it to the Application Alias
   if [ "$export_file" = "" ]
   then
      application_alias=$(echo $response | jq '.items | .[] | .application_alias' | tr -d \")
      export_file="${application_alias}.sql"
   fi

   echo "Exporting Application $application_name (${application_id}) to $(dirname $0)/../apex/${export_file}..."


   # Export the APEX application
   curl -X GET -u "$(get_db_con apps | tr "\/" "\:")" http://apex-app.rapidpie.com:8080/ords/rapidpie/_/db-api/stable/apex/applications/${application_id}?export_format=SQL_SCRIPT >  $(dirname $0)/../apex/${export_file} 2> /dev/null

   echo "...Done."

fi

# Look at exporting individual components
# https://docs.oracle.com/en/database/oracle/oracle-rest-data-services/24.4/orrst/op-apex-applications-application_id-post.html
# {
#     "components": [
#         "LOV:123",
#         "PAGE:321"
#     ]
# }
# Look at 
# https://blogs.oracle.com/apex/post/oracle-apex-app-deployments-made-easy-use-the-ords-rest-apis
# for an example.


# Import the application with a different ID
#curl -X PUT -u "$(get_db_con apps | tr "\/" "\:")" -H "Content-Type:application/sql" --data-binary @$(dirname $0)/../apex/RP_RapidPie_OLD.sql http://apex-app.rapidpie.com:8080/ords/rapidpie/_/db-api/stable/apex/applications/199
#curl -X PUT -u "$(get_db_con apps | tr "\/" "\:")" -H "Content-Type:application/sql" --data-binary @$(dirname $0)/../apex//../apex/RP_Interface_Maintenance.sql http://apex-app.rapidpie.com:8080/ords/rapidpie/_/db-api/stable/apex/workspaces/rapidpie/applications/299


# Clean up
unset application_id
unset export_file
unset response
unset application_name
unset application_alias

# Complete
