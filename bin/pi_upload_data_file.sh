#!/bin/bash
#-----------------------------------------------------------------------------
#
#   PROCEDURE:    pi_upload_data_file.sh
#
#   PURPOSE:      This script is used to upload the given data file to the 
#                 database (PI_DATA_FILES).
#
#   NOTES:        The data file will be moved to the Oracle PI_INBOUND file
#                 directory (/opt/inbound) to allow it to be uploaded.
#
#   PARAMETERS:   data_file   - Supports XLSX (Excel), CSV (comma-delimited), 
#                               XML, and JSON files.
#
#   USAGE:        From the command-line, type:
#                    ./pi_upload_data_file.sh <data_file>
#
#   AUTHOR:       <PERSON>
#
#
# Copyright (c) 2025 Kevin <PERSON>re
# All rights reserved.
#
# This software is proprietary and confidential. Unauthorized copying of this
# file, via any medium, is strictly prohibited without the express permission
# of the author or owning organization.
#
#-----------------------------------------------------------------------------


# Include the "common" functions
. $(dirname $0)/pi_common.sh


typeset data_file=$1
typeset -u file_extension=${data_file##*.}
typeset output_directory="/opt/inbound"
typeset output_file


if [ "$data_file" = "" ]
then
   echo "ERROR: No file provided."
   echo
   echo "USAGE: "
   echo "   pi_upload_data_file.sh <data_file>"
   echo
   exit 1
fi


# Set the output file name to the data_file without the path 
output_file=${data_file##*/}



# Check if the document file exists
if [ -r $data_file ]
then

   case $file_extension in

      XLSX | CSV | XML | JSON)

   
         mv ${data_file} $output_directory/${output_file}
         chmod 666 ${output_directory}/${output_file}

         # Upload the file
         run_sql_proc "spreadsheet.load_data_file('${output_file}')"

         ;;

      *) echo "ERROR: $file_extension files are not supported."
         ;;
   esac

else
   echo "ERROR: $data_file not found or is not readable."
   exit 1
fi

# Clean up
unset data_file
unset file_extension
unset output_directory
unset output_file


# Complete
