#!/bin/bash
#-----------------------------------------------------------------------------
#
#   PROCEDURE:    pi_load_correspondence_file.sh
#
#   PURPOSE:      This script is used to upload the given "correspondence" 
#                 document and it's text lines to the database (FND_ATTACHMENTS 
#                 and FND_ATTACHMENT_LINES).  
#
#   PARAMETERS:   document_file   - The file to upload (only supports PDF)
#
#   USAGE:        From the command-line, type:
#                    ./pi_load_correspondence_file.sh <document_file>
#
#   NOTES:        The document name format consists of:
#                 - Source Systemm Code
#                 - Claim Number
#                 - Document ID
#                 - Extension PDF
#                 Ex. AZHMO_20231101821019500006_APD243582436100154_ML.PDF
#                
#                 If the document does not contain text, it was probably 
#                 scanned to a PDF file. Convert it to an JPEG image file 
#                 (pdftoppm) and use the Tasseract OCR application to 
#                 scan that file and read the resulting PDF (pdfinfo) to get 
#                 ithe text lines.
#
#   AUTHOR:       <PERSON>
#
#
# Copyright (c) 2025 <PERSON>
# All rights reserved.
#
# This software is proprietary and confidential. Unauthorized copying of this
# file, via any medium, is strictly prohibited without the express permission
# of the author or owning organization.
#
#-----------------------------------------------------------------------------


# Include the "common" functions
unset PI_COMMON_FUNCTIONS
. $(dirname $0)/pi_common.sh



function load_lines_from_image_files
{
   typeset document_id=$1
   typeset -i line_number=0
   typeset -i page_number=0
   typeset -i page_line=0
 
   for file_name in "$@"
   do
      if [ "${file_name##*.}" = "jpg" ]  # The first parameter is the Document ID
      then
         echo "$file_name"

         page_number=$(echo ${file_name##*/} | awk -F- '{print $2}' | awk -F. '{print $1}' | sed 's/^0*//')
         echo "Page: $page_number"
         page_line=0

         while read document_record 
         do
            line_number=line_number+1
            page_line=page_line+1
            echo "Doc ID: $document_id  Doc Line: $line_number   Page: $page_number Line: $page_line-> $(echo $document_record | tr -d \')"
   
            # Call SQL procedure to load the line
            run_sql_proc "letters.load_correspondence_document_line($document_id, $line_number, '$(echo $document_record | tr -d \')', $page_number, $page_line)"

         done <<< "$(tesseract $file_name - -c preserve_interword_spaces=1)"
      fi

   done

   unset document_id
   unset line_number
   unset page_number
   unset page_line
}





typeset document_file1
typeset document_file_name
typeset file_extension
typeset output_file
typeset inbound_directory="/opt/inbound"
typeset output_directory="/var/tmp"
typeset -i document_id
typeset document_record
typeset scrubbed_record
typeset -i page_number=0
typeset -i line_number=0

typeset source_system_code
typeset claim_number
typeset document_ident

typeset image_file
typeset converted_image_file
typeset -u load_lines="Y"



# Get the options
while getopts ":l:" option
do
   case $option in
      l)  load_lines=${OPTARG};;
      :)  echo "Option -$OPTARG requires a value";;
      \?) echo -u2 "Unknown option: $OPTARG";;
   esac
done
shift $((OPTIND-1))

# Defaults to "Yes"
if [ "$load_lines" != "N" ]
then
   load_lines="Y"
fi


# Get the parameters
document_file=$1
document_file_name=${document_file##*/}
file_extension=${document_file##*.}



if [ "$document_file" = "" ]
then
   echo "ERROR: No document provided."
   echo
   echo "USAGE: "
   echo "   pi_load_correspondence_file.sh [-l<Y/N>] <document_file>"
   echo
   echo " where the -l option is for loading the lines (defaults to No)."
   echo
   exit 1
fi


# Set the output file name to the document_file without the path or extension
output_file=${document_file_name%.*}

source_system_code=$(echo $output_file | awk -F_ '{print $1}')
claim_number=$(echo $output_file | awk -F_ '{print $2}')
document_ident=${output_file#*${source_system_code}_${claim_number}_}

echo "document_file:      ${document_file}"
echo "document_file_name: ${document_file_name}"
echo "output_file:        $output_file"
echo "source_system_code: $source_system_code"
echo "claim_number:       $claim_number"
echo "document_ident:     $document_ident"
echo "Program Start:      $(date)"
echo "Load Lines (Y/N):   $load_lines"


# Check if the document file exists
if [ -r $document_file ]
then

   case $file_extension in

      PDF | pdf)
         # First check if pdfinfo will find any text. If not, it was probably 
         # scanned to a pdf. Use Tessarack (OCR) to scan it to a temporary pdf 
         # and then get the text.
         if [ "$(pdfinfo -struct-text $document_file)" = "" ] 
         then
            echo "No plain text found in ${document_file}. Converting to an image file."

            # Convert the original PDF file to image file(s)
            pdftoppm -jpeg -r 300 $document_file ${output_directory}/${output_file}
            echo $(date)

            # Create a new PDF file(s) from the image file(s)
            while read image_file 
            do
               converted_image_file=" $(echo ${image_file} | awk -F. '{print $1}')"
               echo "Converting $image_file to ${converted_image_file}.pdf ..."

               # tesseract ${output_directory}/${output_file}-1.jpg ${output_directory}/$output_file --psm 11 -c preserve_interword_spaces=1 pdf
               tesseract ${image_file} ${converted_image_file} --psm 11 -c preserve_interword_spaces=1 pdf

            done <<< "$(ls -v ${output_directory}/${output_file}*.jpg)"
            echo $(date)

            # Combine all the generated PDF files and move it to the inbound directory
            if [ $(ls -v ${output_directory}/${output_file}-*.pdf | wc -l) -lt 1000 ]
            then

               echo "Combining $(ls -v ${output_directory}/${output_file}-*.pdf) into ${output_directory}/${document_file_name}..."
               pdfunite $(ls -v ${output_directory}/${output_file}-*.pdf) ${output_directory}/${document_file_name}

            else
               # Load the first 999 pages
               echo "Combining $(ls -v ${output_directory}/${output_file}-0*.pdf) into ${output_directory}/${document_file_name}..."
               pdfunite $(ls -v ${output_directory}/${output_file}-0*.pdf) ${output_directory}/${document_file_name}

               # If more than 999 pages, load the next 1001. 
               if [ $(ls -v ${output_directory}/${output_file}-1*.pdf | wc -l) -gt 0 ]
               then
                  echo "There are $(ls -v /var/tmp/CCHP_20221117821052100009_MAIL-1*.pdf | wc -l) more files to combine."
                  echo "Adding $(ls -v ${output_directory}/${output_file}-1*.pdf) to ${output_directory}/${document_file_name}..."
                  pdfunite ${output_directory}/${document_file_name} $(ls -v ${output_directory}/${output_file}-1*.pdf) ${output_directory}/${document_file_name}
               fi
            fi
            echo $(date)

            chmod 666 ${output_directory}/${document_file_name}
            mv ${output_directory}/${document_file_name} $inbound_directory

            # Upload the new PDF version
            run_sql_proc "letters.load_correspondence_document('${document_file_name}')"
            echo $(date)


            # Get the resulting Document ID
            document_id=$(run_sql_func "letters.get_correspondence_document_id('${document_file_name}')")
            echo "Document ID: $document_id"
            echo
            echo $(date)
           
            # Scan the file and write the text to the database
            if [ "$load_lines" = "Y" ]
            then
               load_lines_from_image_files $document_id ${output_directory}/${output_file}*.jpg
            else
               echo "Lines not loaded."
            fi
            echo $(date)

         else
            echo "${document_file} contains plain text. Loading it."
            
            # Copy the original file to the Inbound directory
            cp $document_file $inbound_directory
            echo $(date)

            # Upload the PDF
            run_sql_proc "letters.load_correspondence_document('${document_file_name}')"
            echo $(date)

            # Get the resulting Document ID
            document_id=$(run_sql_func "letters.get_correspondence_document_id('${document_file_name}')")
            echo "Document ID: $document_id"
            echo
            echo $(date)

            # Convert the original PDF file to image file(s) to get the pages
            pdftoppm -jpeg -r 300 $document_file ${output_directory}/${output_file}
            echo $(date)
            if [ "$load_lines" = "Y" ]
            then
               load_lines_from_image_files $document_id ${output_directory}/${output_file}*.jpg
            else
               echo "Lines not loaded."
            fi
            echo $(date)

         fi

      ;;
      *) echo "ERROR: $file_extension files are not supported."
         ;;
   esac

else
   echo "ERROR: $document_file not found or is not readable."
   exit 1
fi

echo "Program complete $(date)."
# Clean up
rm ${output_directory}/${output_file}*.jpg 2> /dev/null
rm ${output_directory}/${output_file}*.pdf 2> /dev/null
rm ${output_directory}/temp_letters.load_correspondence_document*.lst 2> /dev/null

unset document_file
unset document_file_name
unset file_extension
unset output_file
unset inbound_directory
unset output_directory
unset document_id
unset document_record
unset scrubbed_record
unset page_number
unset line_number
unset source_system_code
unset claim_number
unset document_ident
unset image_file
unset converted_image_file


# Complete
