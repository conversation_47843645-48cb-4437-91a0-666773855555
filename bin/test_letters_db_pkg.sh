#!/bin/bash
#-----------------------------------------------------------------------------
#
#   PROCEDURE:    test_letters_db_pkg.sh
#
#   PURPOSE:      This script executes the unit tests for the LETTERS database 
#                 package.
#
#   PARAMETERS:   None.
#
#   USAGE:        From the command-line, type:
#                    test_letters_db_pkg.sh
#
#   AUTHOR:       <PERSON>
#
#
# Copyright (c) 2025 <PERSON>uire
# All rights reserved.
#
# This software is proprietary and confidential. Unauthorized copying of this
# file, via any medium, is strictly prohibited without the express permission
# of the author or owning organization.
#
#-----------------------------------------------------------------------------


# Include the "common" functions
unset PI_COMMON_FUNCTIONS
. $(dirname $0)/pi_common.sh

# Test Set-up: 
# Copy the test Excel file (test_file.xlsx) from the rapidpie/test
# subdirectory to the RPI_INBOUND (/opt/inbound) directory.
#
#   cp test/test_file.xlsx /opt/inbound/
#
# Run the load_data_file procedure to load the file into the PI_DATA_FILES table.
#
#   EXECUTE letters.load_data_file('test_file.xlsx');
#
# Check it:
#   SELECT *
#     FROM pi_data_files
#    WHERE file_name = 'test_file.xlsx';   
#

# Check if the record exists
test_record_count=$(
      sqlplus -s -l $(get_db_con apps) <<-EOF
         SET HEADING OFF
         SELECT COUNT(*)
           FROM pi_data_files
          WHERE file_name = 'test_file.xlsx';
         EXIT
EOF
)
test_record_count=$(echo "$test_record_count" | xargs)
echo "Test Record Count: $test_record_count"

if [ "$test_record_count" = "0" ]
then
   echo "Adding the test record"
   cp ${SIMPLIPIED_HOME}/test/test_file.xlsx /opt/inbound/
   run_sql_proc "letters.load_data_file('test_file.xlsx')"

fi

# Call the utPLSQL test package
run_sql_proc "ut.run('test_letters')"


# Complete
