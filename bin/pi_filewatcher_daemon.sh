#!/bin/bash
#-----------------------------------------------------------------------------
#:wq
#   PROCEDURE:    pi_filewatcher_daemon.sh
#
#   PURPOSE:      This script continuously checks the given directory for 
#                 files and, if one is found, calls it's associated script.
#                 table in the database.
#
#   PARAMETERS:   inbound_directory - The search directory (defaults to 
#                 $HOME/inbound).
#
#   USAGE:        From the command-line, type:
#                    ./pi_filewatcher_daemon.sh <inbound_directory>
#                 Or, to run as a daemon process:
#                    nohup ./pi_filewatcher_daemon.sh <inbound_directory> &
#
#   AUTHOR:       <PERSON>
#
#
# Copyright (c) 2025 Kevin <PERSON>
# All rights reserved.
#
# This software is proprietary and confidential. Unauthorized copying of this
# file, via any medium, is strictly prohibited without the express permission
# of the author or owning organization.
#
#-----------------------------------------------------------------------------


# Include the "common" functions
unset PI_COMMON_FUNCTIONS
. $(dirname $0)/pi_common.sh

# Local variables
typeset inbound_directory=${1}
typeset -i error_status=0
typeset timestamp="$(date +%Y%m%d%H%M%S)"
typeset log_file=/var/tmp/pi_filewatcher_${timestamp}.log

if [ "${inbound_directory}" = "" ]
then
   inbound_directory=$HOME/inbound
fi


# Add the standard header information to the log file
printf "%-10s %s\n" "Program:" "$(basename $0)" | tee $log_file
printf "%-10s %s\n" "Log File:" "$log_file" | tee -a $log_file
printf "%-10s %s\n" "Execution:" "$(date)" | tee -a $log_file

printf "\n%s\n" "Parameters:" | tee -a $log_file
printf "   %-18s %s\n\n\n" "Inbound Directory:" "${inbound_directory}" | \
   tee -a $log_file


until [ -r /var/tmp/pi_stop_filewatcher_daemon ]
do
   for file in ${inbound_directory}/*; do
      if [ -f "$file" ] # Make sure the file still exists
      then
         # Default to load every file into the pi_inbound_interface_stg table.
         # (This will change to get the actions from the database).
         echo "Calling: pi_load_data_file.sh ${file##*/}"
         $(dirname $0)/pi_load_data_file.sh ${file##*/}
      fi
      
   done

   sleep 20s #1m

done


# Clean-up
rm /var/tmp/pi_stop_filewatcher_daemon
unset inbound_directory
unset error_status0
unset timestamp
unset log_file

# Complete

