#!/bin/bash
#-----------------------------------------------------------------------------
#
#   PROCEDURE:    pi_load_data_file.sh
#
#   PURPOSE:      This script loads the given data file into the staging 
#                 table in the database.
#
#   PARAMETERS:   data_file - The data file must be in the $HOME/inbound
#                             directory.
#
#   USAGE:        From the command-line, type:
#                    ./pi_load_data_file.sh <data_file>
#
#   AUTHOR:       <PERSON>
#
#
# Copyright (c) 2025 <PERSON>
# All rights reserved.
#
# This software is proprietary and confidential. Unauthorized copying of this
# file, via any medium, is strictly prohibited without the express permission
# of the author or owning organization.
#
#-----------------------------------------------------------------------------


# Include the "common" functions
unset PI_COMMON_FUNCTIONS
. $(dirname $0)/pi_common.sh

# Local variables
typeset inbound_file=${1}
typeset data_file
typeset -i error_status=0
typeset timestamp="$(date +%Y%m%d%H%M%S)"
typeset log_file=/var/tmp/pi_load_data_file_${inbound_file}_${timestamp}.log
typeset sql_load_log=/var/tmp/pi_inbound_interface_stg_${inbound_file}_${timestamp}.log
typeset subject="Rapid Pie File ${inbound_file} Load Results"
typeset attachments=${log_file}
typeset body=""


# Add the standard header information to the log file
printf "%-10s %s\n" "Program:" "$(basename $0)" | tee $log_file
printf "%-10s %s\n" "Log File:" "$log_file" | tee -a $log_file
printf "%-10s %s\n" "Execution:" "$(date)" | tee -a $log_file

printf "\n%s\n" "Parameters:" | tee -a $log_file
printf "   %-18s %s\n\n\n" "Date File:" "${inbound_file}" | \
   tee -a $log_file


# Move the file out of the inbound directory and add the timestamp
echo "Move the file:" | tee -a $log_file
data_file=$(move_inbound_file ${inbound_file})
error_status=$?

if [ $error_status -eq 0 ]
then
   echo "${inbound_file} successfully moved to ${data_file}." | \
      tee -a $log_file

   # Run SQL*loader to load the data file into the staging table
   run_sql_loader -d ${data_file} -l ${sql_load_log} pi_inbound_interface_stg.ctl
   error_status=$?
   echo | tee -a $log_file
   echo "SQL*Load Results:" | tee -a $log_file
   cat ${sql_load_log} | grep " Rows " | tee -a $log_file
   attachments="${attachments},${sql_load_log}"
else
   # The error message is returned from move_inbound_file
   echo "${data_file}" | tee -a $log_file
   echo "Aborting load." | tee -a $log_file

fi
echo | tee -a $log_file
echo "Load processing complete." | tee -a $log_file


# Email the load results
subject="$(get_subject_prefix $error_status)${subject}" 
if [ $error_status -eq 0 ]
then
   body="The ${inbound_file} file was successfully loaded into the staging table."
else
   body="The ${inbound_file} file was not loaded into the staging table."
   body="${body} Please refer to the attached log file(s) for details."
fi

send_email -s "${subject}" -a ${attachments} -t <EMAIL> "${body}"


# Clean-up
unset inbound_file
unset data_file
unset error_status0
unset timestamp
unset log_file
unset sql_load_log
unset subject
unset attachments
unset body

# Complete
