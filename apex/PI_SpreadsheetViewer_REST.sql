
-- Generated by ORDS REST Data Services 23.4.0.r3461619
-- Schema: APPS  Date: Thu May 15 11:45:43 2025 
--

BEGIN
  ORDS.ENABLE_SCHEMA(
      p_enabled             => TRUE,
      p_schema              => 'APPS',
      p_url_mapping_type    => 'BASE_PATH',
      p_url_mapping_pattern => 'rapidpie',
      p_auto_rest_auth      => FALSE);
    
  ORDS.DEFINE_MODULE(
      p_module_name    => 'SpreadsheetViewer',
      p_base_path      => '/spreadsheet/',
      p_items_per_page => 25,
      p_status         => 'PUBLISHED',
      p_comments       => NULL);

  ORDS.DEFINE_TEMPLATE(
      p_module_name    => 'SpreadsheetViewer',
      p_pattern        => 'getspreadsheet/',
      p_priority       => 0,
      p_etag_type      => 'HASH',
      p_etag_query     => NULL,
      p_comments       => NULL);

  ORDS.DEFINE_HANDLER(
      p_module_name    => 'SpreadsheetViewer',
      p_pattern        => 'getspreadsheet/',
      p_method         => 'GET',
      p_source_type    => 'plsql/block',
      p_mimes_allowed  => NULL,
      p_comments       => NULL,
      p_source         => 
'DECLARE
   sys_cur SYS_REFCURSOR;
BEGIN   
   rp_spreadsheet_data(''LetterInfoUpdateTemplate.xlsx'', sys_cur);
END;
/  ');

    
        
COMMIT;

END;