
-- Generated by ORDS REST Data Services 23.4.0.r3461619
-- Schema: APPS  Date: Thu May 15 11:44:00 2025 
--

BEGIN
  ORDS.ENABLE_SCHEMA(
      p_enabled             => TRUE,
      p_schema              => 'APPS',
      p_url_mapping_type    => 'BASE_PATH',
      p_url_mapping_pattern => 'rapidpie',
      p_auto_rest_auth      => FALSE);
    
  ORDS.DEFINE_MODULE(
      p_module_name    => 'PDFViewer',
      p_base_path      => '/pdfviewer/',
      p_items_per_page => 25,
      p_status         => 'PUBLISHED',
      p_comments       => NULL);

  ORDS.DEFINE_TEMPLATE(
      p_module_name    => 'PDFViewer',
      p_pattern        => 'scanneddocuments/:id',
      p_priority       => 0,
      p_etag_type      => 'HASH',
      p_etag_query     => NULL,
      p_comments       => NULL);

  ORDS.DEFINE_HANDLER(
      p_module_name    => 'PDFViewer',
      p_pattern        => 'scanneddocuments/:id',
      p_method         => 'GET',
      p_source_type    => 'resource/lob',
      p_mimes_allowed  => NULL,
      p_comments       => NULL,
      p_source         => 
'SELECT mimetype, pdf_file
  FROM apps.pi_scanned_documents
 WHERE document_id = :id');

  ORDS.DEFINE_TEMPLATE(
      p_module_name    => 'PDFViewer',
      p_pattern        => 'letter/:id/:usr',
      p_priority       => 0,
      p_etag_type      => 'HASH',
      p_etag_query     => NULL,
      p_comments       => NULL);

  ORDS.DEFINE_HANDLER(
      p_module_name    => 'PDFViewer',
      p_pattern        => 'letter/:id/:usr',
      p_method         => 'GET',
      p_source_type    => 'resource/lob',
      p_mimes_allowed  => NULL,
      p_comments       => NULL,
      p_source         => 
'SELECT mimetype, AttachmentBLOB
  FROM APPS.fnd_attachments
 WHERE AttachmentID = :id
   AND EXISTS (select 1 from APEX_WORKSPACE_SESSIONS where apex_session_id||user_name = :usr)');

  ORDS.DEFINE_TEMPLATE(
      p_module_name    => 'PDFViewer',
      p_pattern        => 'correspondence/:id',
      p_priority       => 0,
      p_etag_type      => 'HASH',
      p_etag_query     => NULL,
      p_comments       => NULL);

  ORDS.DEFINE_HANDLER(
      p_module_name    => 'PDFViewer',
      p_pattern        => 'correspondence/:id',
      p_method         => 'GET',
      p_source_type    => 'resource/lob',
      p_mimes_allowed  => NULL,
      p_comments       => NULL,
      p_source         => 
'SELECT mimetype, AttachmentBLOB
  FROM APPS.fnd_attachments
 WHERE AttachmentID = :id');

    
        
COMMIT;

END;