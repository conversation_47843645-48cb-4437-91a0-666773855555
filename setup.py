"""
Setup script for SimpliPIed package.
"""

from setuptools import setup, find_packages
import os

# Read the README file
def read_readme():
    readme_path = os.path.join(os.path.dirname(__file__), 'README.md')
    if os.path.exists(readme_path):
        with open(readme_path, 'r', encoding='utf-8') as f:
            return f.read()
    return "SimpliPIed - A comprehensive system for processing insurance claims and generating letters."

# Read requirements
def read_requirements():
    requirements_path = os.path.join(os.path.dirname(__file__), 'requirements.txt')
    if os.path.exists(requirements_path):
        with open(requirements_path, 'r') as f:
            return [line.strip() for line in f if line.strip() and not line.startswith('#')]
    return []

setup(
    name="simplipied",
    version="1.0.0",
    author="Kevin <PERSON>re",
    author_email="<EMAIL>",
    description="A comprehensive system for processing insurance claims and generating letters",
    long_description=read_readme(),
    long_description_content_type="text/markdown",
    url="https://github.com/your-org/simplipied",  # Update with actual URL
    package_dir={"": "src"},
    packages=find_packages(where="src"),
    classifiers=[
        "Development Status :: 4 - Beta",
        "Intended Audience :: Healthcare Industry",
        "License :: OSI Approved :: MIT License",  # Update as needed
        "Operating System :: OS Independent",
        "Programming Language :: Python :: 3",
        "Programming Language :: Python :: 3.8",
        "Programming Language :: Python :: 3.9",
        "Programming Language :: Python :: 3.10",
        "Programming Language :: Python :: 3.11",
    ],
    python_requires=">=3.8",
    install_requires=read_requirements(),
    extras_require={
        "dev": [
            "pytest>=6.0.0",
            "pytest-cov>=2.0.0",
            "black>=21.0.0",
            "flake8>=3.8.0",
        ],
    },
    entry_points={
        "console_scripts": [
            "pi-export-excel=simplipied.cli.excel:main",
            "pi-generate-claim-letter=simplipied.cli.claim_letter:main",
            "pi-generate-provider-letter=simplipied.cli.provider_letter:main",
        ],
    },
    include_package_data=True,
    zip_safe=False,
)
