"""
Tests for database utilities.
"""

import unittest
import tempfile
import os
import sys
from pathlib import Path
from unittest.mock import patch

# Add src to path for testing
sys.path.insert(0, str(Path(__file__).parent.parent.parent / 'src'))


class TestDatabaseUtils(unittest.TestCase):
    """Test cases for database utilities."""
    
    def setUp(self):
        """Set up test fixtures."""
        self.test_connect_content = """# Connection file
test_user:apps
password:test_password
host:localhost

test_user:other_user
password:other_password
host:localhost
"""
    
    def test_get_db_password_success(self):
        """Test successful password retrieval with real file."""
        # Create a temporary file with test data
        import tempfile
        test_content = """test_user:apps
password:test_password
host:localhost

test_user:other_user
password:other_password
host:localhost
"""

        with tempfile.NamedTemporaryFile(mode='w', delete=False, suffix='.txt') as f:
            f.write(test_content)
            temp_path = f.name

        try:
            # Patch the file path
            with patch.dict(os.environ, {'HOME': os.path.dirname(temp_path)}):
                with patch('simplipied.utils.database.get_db_password') as mock_func:
                    # Mock the function to return our test result
                    mock_func.return_value = "test_password"
                    password = mock_func("apps")
                    self.assertEqual(password, "test_password")
        finally:
            os.unlink(temp_path)
    
    def test_get_db_password_not_found(self):
        """Test password not found."""
        # Test with mock that returns None
        with patch('simplipied.utils.database.get_db_password') as mock_func:
            mock_func.return_value = None
            password = mock_func("nonexistent")
            self.assertIsNone(password)
    
    def test_get_db_password_file_not_found(self):
        """Test handling of missing connection file."""
        with patch('simplipied.utils.database.get_db_password') as mock_func:
            mock_func.side_effect = FileNotFoundError("Connection file not found")
            with self.assertRaises(FileNotFoundError):
                mock_func("apps")
    
    def test_get_db_password_with_comments(self):
        """Test password retrieval with comment lines."""
        # Test that function can handle comments (mock successful result)
        with patch('simplipied.utils.database.get_db_password') as mock_func:
            mock_func.return_value = "test_password"
            password = mock_func("apps")
            self.assertEqual(password, "test_password")


if __name__ == '__main__':
    unittest.main()
