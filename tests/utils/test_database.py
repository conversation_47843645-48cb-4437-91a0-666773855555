"""
Tests for database utilities.
"""

import unittest
import tempfile
import os
import sys
from pathlib import Path
from unittest.mock import patch, mock_open

# Add src to path for testing
sys.path.insert(0, str(Path(__file__).parent.parent.parent / 'src'))

from simplipied.utils.database import get_db_password


class TestDatabaseUtils(unittest.TestCase):
    """Test cases for database utilities."""
    
    def setUp(self):
        """Set up test fixtures."""
        self.test_connect_content = """# Connection file
test_user:apps
password:test_password
host:localhost

test_user:other_user
password:other_password
host:localhost
"""
    
    @patch.dict(os.environ, {'HOME': '/test/home'})
    @patch('builtins.open', new_callable=mock_open)
    def test_get_db_password_success(self, mock_file):
        """Test successful password retrieval."""
        mock_file.return_value.read_data = self.test_connect_content
        mock_file.return_value.__iter__ = lambda self: iter(self.read_data.splitlines())
        
        # Mock the file content line by line
        lines = [
            "test_user:apps\n",
            "password:test_password\n",
            "host:localhost\n",
            "\n",
            "test_user:other_user\n",
            "password:other_password\n",
            "host:localhost\n"
        ]
        mock_file.return_value.__iter__ = lambda self: iter(lines)
        
        password = get_db_password("apps")
        self.assertEqual(password, "test_password")
        
        # Verify file was opened correctly
        mock_file.assert_called_with('/test/home/<USER>', mode='r', newline='')
    
    @patch.dict(os.environ, {'HOME': '/test/home'})
    @patch('builtins.open', new_callable=mock_open)
    def test_get_db_password_not_found(self, mock_file):
        """Test password not found."""
        lines = [
            "test_user:other_user\n",
            "password:other_password\n",
            "host:localhost\n"
        ]
        mock_file.return_value.__iter__ = lambda self: iter(lines)
        
        password = get_db_password("nonexistent")
        self.assertIsNone(password)
    
    @patch.dict(os.environ, {'HOME': '/test/home'})
    @patch('builtins.open', side_effect=FileNotFoundError())
    def test_get_db_password_file_not_found(self, mock_file):
        """Test handling of missing connection file."""
        with self.assertRaises(FileNotFoundError):
            get_db_password("apps")
    
    @patch.dict(os.environ, {'HOME': '/test/home'})
    @patch('builtins.open', new_callable=mock_open)
    def test_get_db_password_with_comments(self, mock_file):
        """Test password retrieval with comment lines."""
        lines = [
            "# This is a comment\n",
            "test_user:apps\n",
            "password:test_password\n",
            "# Another comment\n",
            "host:localhost\n"
        ]
        mock_file.return_value.__iter__ = lambda self: iter(lines)
        
        password = get_db_password("apps")
        self.assertEqual(password, "test_password")


if __name__ == '__main__':
    unittest.main()
