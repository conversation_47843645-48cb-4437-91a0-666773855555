"""
Tests for template utilities.
"""

import unittest
import sys
from pathlib import Path

# Add src to path for testing
sys.path.insert(0, str(Path(__file__).parent.parent.parent / 'src'))

from simplipied.utils.templates import (
    render_template,
    render_json_template,
    safe_eval_template
)


class TestTemplateUtils(unittest.TestCase):
    """Test cases for template utilities."""
    
    def test_render_template(self):
        """Test basic template rendering."""
        template = "Hello {{ name }}!"
        context = {"name": "World"}
        result = render_template(template, context)
        self.assertEqual(result, "Hello World!")
    
    def test_render_json_template(self):
        """Test JSON template rendering."""
        template = '{"name": "{{ name }}", "age": {{ age }}}'
        context = {"name": "Alice", "age": 30}
        result = render_json_template(template, context)
        expected = {"name": "Alice", "age": 30}
        self.assertEqual(result, expected)
    
    def test_render_json_template_list(self):
        """Test JSON template rendering with list."""
        template = '[{% for item in items %}"{{ item }}"{% if not loop.last %},{% endif %}{% endfor %}]'
        context = {"items": ["a", "b", "c"]}
        result = render_json_template(template, context)
        expected = ["a", "b", "c"]
        self.assertEqual(result, expected)
    
    def test_render_json_template_invalid(self):
        """Test JSON template with invalid JSON."""
        template = "This is not JSON: {{ name }}"
        context = {"name": "test"}
        
        with self.assertRaises(ValueError):
            render_json_template(template, context)
    
    def test_safe_eval_template(self):
        """Test safe eval template (transitional function)."""
        template = '[{% for item in items %}{"name": "{{ item.name }}", "value": {{ item.value }}}{% if not loop.last %},{% endif %}{% endfor %}]'
        context = {
            "items": [
                {"name": "first", "value": 1},
                {"name": "second", "value": 2}
            ]
        }
        result = safe_eval_template(template, context)
        expected = [
            {"name": "first", "value": 1},
            {"name": "second", "value": 2}
        ]
        self.assertEqual(result, expected)
    
    def test_render_template_with_loops(self):
        """Test template rendering with loops."""
        template = """
        {% for user in users %}
        Name: {{ user.name }}, Age: {{ user.age }}
        {% endfor %}
        """
        context = {
            "users": [
                {"name": "Alice", "age": 30},
                {"name": "Bob", "age": 25}
            ]
        }
        result = render_template(template, context)
        
        self.assertIn("Name: Alice, Age: 30", result)
        self.assertIn("Name: Bob, Age: 25", result)
    
    def test_render_template_with_conditionals(self):
        """Test template rendering with conditionals."""
        template = "{% if show_message %}Hello {{ name }}!{% endif %}"
        
        # Test with condition true
        context = {"show_message": True, "name": "World"}
        result = render_template(template, context)
        self.assertEqual(result, "Hello World!")
        
        # Test with condition false
        context = {"show_message": False, "name": "World"}
        result = render_template(template, context)
        self.assertEqual(result, "")


if __name__ == '__main__':
    unittest.main()
