"""
Tests for Excel export functionality.
"""

import unittest
import os
import tempfile
import sys
from pathlib import Path

# Add src to path for testing
sys.path.insert(0, str(Path(__file__).parent.parent.parent / 'src'))

from simplipied.excel.export import (
    generate_excel_from_template,
    create_excel_from_data,
    get_sample_data,
    get_sample_template
)
from openpyxl import load_workbook


class TestExcelExport(unittest.TestCase):
    """Test cases for Excel export functionality."""
    
    def setUp(self):
        """Set up test fixtures."""
        self.temp_dir = tempfile.mkdtemp()
        self.sample_data = get_sample_data()
        self.sample_template = get_sample_template()
        self.sample_context = {"users": self.sample_data}
    
    def tearDown(self):
        """Clean up test fixtures."""
        import shutil
        shutil.rmtree(self.temp_dir, ignore_errors=True)
    
    def test_generate_excel_from_template(self):
        """Test generating Excel file from template."""
        output_file = os.path.join(self.temp_dir, "test_output.xlsx")
        
        # Generate Excel file
        generate_excel_from_template(
            self.sample_context,
            self.sample_template,
            output_file
        )
        
        # Verify file was created
        self.assertTrue(os.path.exists(output_file))
        
        # Verify content
        wb = load_workbook(output_file)
        ws = wb.active
        
        # Check headers
        headers = [cell.value for cell in ws[1]]
        expected_headers = ["Name", "Age", "Department"]
        self.assertEqual(headers, expected_headers)
        
        # Check data rows
        self.assertEqual(ws.max_row, 4)  # 1 header + 3 data rows
        
        # Check first data row
        first_row = [cell.value for cell in ws[2]]
        self.assertEqual(first_row, ["Alice", 30, "HR"])
    
    def test_create_excel_from_data(self):
        """Test creating Excel file directly from data."""
        output_file = os.path.join(self.temp_dir, "test_direct.xlsx")
        
        # Create Excel file
        create_excel_from_data(self.sample_data, output_file)
        
        # Verify file was created
        self.assertTrue(os.path.exists(output_file))
        
        # Verify content
        wb = load_workbook(output_file)
        ws = wb.active
        
        # Check that we have the right number of rows
        self.assertEqual(ws.max_row, 4)  # 1 header + 3 data rows
    
    def test_invalid_template(self):
        """Test handling of invalid template."""
        output_file = os.path.join(self.temp_dir, "test_invalid.xlsx")
        invalid_template = "This is not valid JSON: {{ invalid }}"
        
        with self.assertRaises(Exception):
            generate_excel_from_template(
                self.sample_context,
                invalid_template,
                output_file
            )
    
    def test_empty_data(self):
        """Test handling of empty data."""
        output_file = os.path.join(self.temp_dir, "test_empty.xlsx")
        
        with self.assertRaises(ValueError):
            create_excel_from_data([], output_file)
    
    def test_invalid_data_format(self):
        """Test handling of invalid data format."""
        output_file = os.path.join(self.temp_dir, "test_invalid_data.xlsx")
        invalid_data = ["not", "a", "dict"]
        
        with self.assertRaises(ValueError):
            create_excel_from_data(invalid_data, output_file)
    
    def test_custom_sheet_title(self):
        """Test custom sheet title."""
        output_file = os.path.join(self.temp_dir, "test_title.xlsx")
        custom_title = "Custom Sheet"
        
        create_excel_from_data(self.sample_data, output_file, custom_title)
        
        wb = load_workbook(output_file)
        self.assertEqual(wb.active.title, custom_title)


if __name__ == '__main__':
    unittest.main()
