------------------------------------------------------------------------------
--
--   PROCEDURE:      pi_create_test_connect_func.sql
--
--   PURPOSE:        This script is used to create the RP_TEST_CONNECT function.
--                   It is used for testing the run_sql_func shell function. 
--
--   PARAMETERS:     None. 
--
--   INSTRUCTIONS:   From within SQL*Plus, type:
--                      START pi_create_test_connect_func.sql
--
--   AUTHOR:         <PERSON>re.
--
--   INSTALL SCHEMA: APPS
--
--
--   Copyright (c) 2025 Kevin <PERSON>uire
--   All rights reserved.
--
--   This software is proprietary and confidential. Unauthorized copying of this
--   file, via any medium, is strictly prohibited without the express permission
--   of the author or owning organization.
--
------------------------------------------------------------------------------

SET ECHO ON


SPOOL pi_create_test_connect_func

CREATE OR REPLACE FUNCTION pi_test_connect(p_string IN VARCHAR2)
  RETURN VARCHAR2
IS

BEGIN

   RETURN p_string;

END pi_test_connect;
/
 
SHOW ERRORS

SPOOL OFF

-- Complete
