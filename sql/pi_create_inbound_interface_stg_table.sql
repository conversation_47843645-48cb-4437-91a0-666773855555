------------------------------------------------------------------------------
--
--   PROCEDURE:    pi_create_inbound_interface_stg_table.sql
--
--   PURPOSE:      This procedure is used to create the PI_INBOUND_INTERFACE_STG
--                 table. This table used to sopport loading files into the 
--                 application.
-- 
--   PARAMETERS:   None. 
--
--   INSTRUCTIONS: From within SQL*Plus (connected as APPS), type:
--                    START pi_create_inbound_interface_stg_table.sql
--
--   AUTHOR:       <PERSON>.
--
--   INSTALL SCHEMA: APPS
--
--
--   Copyright (c) 2025 <PERSON>
--   All rights reserved.
--
--   This software is proprietary and confidential. Unauthorized copying of this
--   file, via any medium, is strictly prohibited without the express permission
--   of the author or owning organization.
--
------------------------------------------------------------------------------

WHENEVER OSERROR EXIT

SET ECHO ON



-- Open the log file
SPOOL pi_create_inbound_interface_stg_table



-- Drop the current table 
-- DROP TABLE pi_inbound_interface_stg;



-- Create the table
CREATE TABLE pi_inbound_interface_stg
(
   input_line       VARCHAR2(1000),
   --
   source           VARCHAR2(60),
   load_date        DATE,
   status           VARCHAR2(20) DEFAULT 'NEW',
   record_no        NUMBER,
   comments         VARCHAR2(100)
)
TABLESPACE users
STORAGE (INITIAL 100K NEXT 100K);


-- Stop logging
SPOOL OFF 


-- Complete


