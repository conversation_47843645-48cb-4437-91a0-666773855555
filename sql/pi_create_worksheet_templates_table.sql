------------------------------------------------------------------------------
--
--   PROCEDURE:      pi_create_worksheet_templates_table.sql
--
--   PURPOSE:        This procedure is used to create the 
--                   PI_WORKSHEET_TEMPLATES table. 
-- 
--   PARAMETERS:     None. 
--
--   INSTRUCTIONS:   From within SQL*Plus (connected as APPS), type:
--                      START pi_create_worksheet_templates_table.sql
--
--   AUTHOR:         <PERSON>re.
--
--   INSTALL SCHEMA: APPS
--
--
--   Copyright (c) 2025 Kevin <PERSON>uire
--   All rights reserved.
--
--   This software is proprietary and confidential. Unauthorized copying of this
--   file, via any medium, is strictly prohibited without the express permission
--   of the author or owning organization.
--
------------------------------------------------------------------------------

WHENEVER OSERROR EXIT

SET ECHO ON


-- Open the log file
SPOOL pi_create_worksheet_templates_table


-- Drop the current table 
DROP TABLE pi_worksheet_templates;

-- Create the table
CREATE TABLE pi_worksheet_templates
(
   worksheet_id     NUMBER GENERATED ALWAYS AS IDENTITY 
                    MINVALUE 1 MAXVALUE 9999999999999999999999999999 
                    INCREMENT BY 1 START WITH 1 NOT NULL ENABLE,
   spreadsheet_id   NUMBER        NOT NULL,
   worksheet_order  NUMBER        NOT NULL,
   worksheet_name   VARCHAR2(100) NOT NULL,
   createdby        VARCHAR2(200) DEFAULT 'SYSTEM',
   createddate      DATE          DEFAULT SYSDATE,
   modifiedby       VARCHAR2(200) DEFAULT 'SYSTEM',
   modifieddate     DATE          DEFAULT SYSDATE,
   --
   CONSTRAINT pi_worksheet_templates_pk
      PRIMARY KEY (worksheet_id)
      USING INDEX,
   CONSTRAINT  pi_worksheet_templates_fk
      FOREIGN KEY (spreadsheet_id)
      REFERENCES pi_spreadsheet_templates (spreadsheet_id)
);


-- Stop logging
SPOOL OFF 


-- Complete


