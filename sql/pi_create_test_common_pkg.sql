------------------------------------------------------------------------------
--
--   PROCEDURE:      pi_create_test_common_pkg.sql
--
--   PURPOSE:        This script is used to create the TEST_COMMON package. 
--
--   PARAMETERS:     None. 
--
--   INSTRUCTIONS:   From within SQL*Plus, type:
--                      START pi_create_test_common_pkg.sql
--
--   AUTHOR:         <PERSON>re.
--
--   INSTALL SCHEMA: APPS
--
--
--   Copyright (c) 2025 <PERSON>
--   All rights reserved.
--
--   This software is proprietary and confidential. Unauthorized copying of this
--   file, via any medium, is strictly prohibited without the express permission
--   of the author or owning organization.
--
------------------------------------------------------------------------------

SET ECHO ON


SPOOL pi_create_test_common_pkg

CREATE OR REPLACE PACKAGE test_common AUTHID CURRENT_USER AS

   --%suite(COMMON Unit Test Package (pi_create_test_common_pkg.sql))
   
   -- %beforeall
   PROCEDURE create_test_email_addresses;

   -- %beforeall
   PROCEDURE create_test_new_public_key_requests;

   -- %beforeall
   PROCEDURE create_test_letter_data;
   

   --%context(-- pi_email_addresses Unit Tests)
   
   -- %test(test_get_email_addresses_returns_addresses)
   PROCEDURE test_get_email_addresses_returns_addresses;

   -- %test(test_get_email_addresses_defaults_to_all_addresses)
   PROCEDURE test_get_email_addresses_defaults_to_all_addresses;

   -- %test(test_get_email_addresses_returns_only_to_addresses)
   PROCEDURE test_get_email_addresses_returns_only_to_addresses;

   -- %test(test_get_email_addresses_returns_only_cc_addresses)
   PROCEDURE test_get_email_addresses_returns_only_cc_addresses;

   -- %test(test_get_email_addresses_returns_only_bcc_addresses)
   PROCEDURE test_get_email_addresses_returns_only_bcc_addresses;

   --%endcontext


   

   --%context(-- pi_public_keys Unit Tests)
   
   -- %test(test_get_new_public_key_requests_returns_records)
   PROCEDURE test_get_new_public_key_requests_returns_records;

   -- %test(test_update_public_key_updates_records)
   PROCEDURE test_update_public_key_updates_records;

   -- %test(test_update_public_key_handles_errors)
   PROCEDURE test_update_public_key_handles_errors;

   --%endcontext


END test_common;
/   
SHOW ERRORS


CREATE OR REPLACE PACKAGE BODY test_common AS

   PROCEDURE create_test_email_addresses IS
   BEGIN

      -- Needed for FK constraint
      INSERT INTO pi_interfaces
      (interface, enabled, start_date, end_date)
      VALUES ('TEST', 'Y', SYSDATE, SYSDATE + 1);

      INSERT INTO pi_interface_email_addresses
      (interface, email_address, address_type, enabled)
      VALUES ('TEST', '<EMAIL>', 'TO', 'Y');

      INSERT INTO pi_interface_email_addresses
      (interface, email_address, address_type, enabled)
      VALUES ('TEST', '<EMAIL>', 'TO', 'Y');

      INSERT INTO pi_interface_email_addresses
      (interface, email_address, address_type, enabled)
      VALUES ('TEST', '<EMAIL>', 'CC', 'Y');

      INSERT INTO pi_interface_email_addresses
      (interface, email_address, address_type, enabled)
      VALUES ('TEST', '<EMAIL>', 'BCC', 'Y');

   END create_test_email_addresses;


   PROCEDURE create_test_new_public_key_requests IS
   BEGIN

      -- Hold any pending requests
      UPDATE pi_public_keys
         SET status = 'HOLD'
       WHERE status = 'NEW';

      INSERT INTO pi_public_keys
      (key_name, key_email, key_comment, expiration, status)
      VALUES ('test_key', '<EMAIL>', 'test_comment', '1Y', 'NEW');
    
   END create_test_new_public_key_requests;


   PROCEDURE create_test_letter_data IS
   BEGIN

      -- Populated Attention; Null address line 2
      INSERT INTO ntf_notificationdeliveries
      (notificationbatchid, notificationrefnumber, sourcesystemcode, letterdate, 
       providertin, providerattention, providergroupname, provideraddressline1, 
       provideraddressline2, providercity, providerstatecode, providerzip, transmitfilename)
      VALUES
      (-99, 'Test_Letter_1', 'AHS', TO_DATE('06/30/24', 'MM/DD/RR'), '123456789', 
       'PROCESS UNIT', 'ACME ANVIL CO', '123 ROAD RUNNER RD', NULL, 'TOMBSTONE',
       'AZ', '**********', 'ACME_123456789_ACMEANVILC_20241028');

      -- Null Attention; populated address line 2
      INSERT INTO ntf_notificationdeliveries
      (notificationbatchid, notificationrefnumber, sourcesystemcode, letterdate, 
       providertin, providerattention, providergroupname, provideraddressline1, 
       provideraddressline2, providercity, providerstatecode, providerzip, transmitfilename)
      VALUES
      (-99, 'Test_Letter_2', 'AHS', TO_DATE('06/30/24', 'MM/DD/RR'), '987654321', 
       NULL, 'CORPORATE AMERICA', '1776 WASHINGTON ST', 'SUITE 102', 'PARUMPH',
       'NV', '**********', 'CORP_987654321_CORPAMERIC_20241028');

   END create_test_letter_data;



   -- %test(test_get_email_addresses_returns_addresses)
   PROCEDURE test_get_email_addresses_returns_addresses IS 
      expected      VARCHAR2(200) := '<EMAIL>%';
      actual        VARCHAR2(200);
   BEGIN

      actual := common.get_email_addresses('TEST');

      ut.expect(actual,
         'test_get_email_addresses_returns_addresses'
         ).to_be_like(expected);

   END test_get_email_addresses_returns_addresses;



   -- %test(test_get_email_addresses_defaults_to_all_addresses)
   PROCEDURE test_get_email_addresses_defaults_to_all_addresses IS 
      expected      VARCHAR2(200) := 
         '<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>';
      actual        VARCHAR2(200);
   BEGIN
      actual := common.get_email_addresses('TEST');  -- Default to "ALL"

      ut.expect(actual,
         'test_get_email_addresses_defaults_to_all_addresses'
         ).to_equal(expected);

   END test_get_email_addresses_defaults_to_all_addresses;


  -- %test(test_get_email_addresses_returns_only_to_addresses)
   PROCEDURE test_get_email_addresses_returns_only_to_addresses IS 
      expected      VARCHAR2(200) := '<EMAIL>,<EMAIL>';
      actual        VARCHAR2(200);
   BEGIN
      actual := common.get_email_addresses('TEST', 'TO');

      ut.expect(actual,
         'test_get_email_addresses_returns_only_to_addresses'
         ).to_equal(expected);

   END test_get_email_addresses_returns_only_to_addresses;


   -- %test(test_get_email_addresses_returns_only_cc_addresses)
   PROCEDURE test_get_email_addresses_returns_only_cc_addresses IS 
      expected      VARCHAR2(200) := '<EMAIL>';
      actual        VARCHAR2(200);
   BEGIN
      actual := common.get_email_addresses('TEST', 'CC');

      ut.expect(actual,
         'test_get_email_addresses_returns_only_cc_addresses'
         ).to_equal(expected);

   END test_get_email_addresses_returns_only_cc_addresses;


   -- %test(test_get_email_addresses_returns_only_bcc_addresses)
   PROCEDURE test_get_email_addresses_returns_only_bcc_addresses IS 
      expected      VARCHAR2(200) := '<EMAIL>';
      actual        VARCHAR2(200);
   BEGIN
      actual := common.get_email_addresses('TEST', 'BCC');

      ut.expect(actual,
         'test_get_email_addresses_returns_only_bcc_addresses'
         ).to_equal(expected);

   END test_get_email_addresses_returns_only_bcc_addresses;




   -- %test(test_get_new_public_key_requests_returns_records)
   PROCEDURE test_get_new_public_key_requests_returns_records IS 
      expected      VARCHAR2(200) := '%,test_key,<EMAIL>,test_comment,1Y,NEW;';
      actual        VARCHAR2(200);
   BEGIN

      common.get_new_public_key_requests(actual);

      ut.expect(actual,
         'test_get_new_public_key_requests_returns_records'
         ).to_be_like(expected);

   END test_get_new_public_key_requests_returns_records;



   -- %test(test_update_public_key_updates_records)
   PROCEDURE test_update_public_key_updates_records IS 
      expected      VARCHAR2(200) := 'PROCESSED';
      actual        VARCHAR2(200);
      test_key_id   NUMBER;
   BEGIN

      SELECT key_id
        INTO test_key_id
        FROM pi_public_keys
       WHERE key_name = 'test_key';
      
      common.update_public_key(test_key_id, 'PROCESSED', 'ABCD12345', '2025-09-18', '/var/tmp/public_key_ABCD12345');

      SELECT status
        INTO actual
        FROM pi_public_keys
       WHERE key_id = test_key_id;

      ut.expect(actual,
         'test_update_public_key_updates_records'
         ).to_equal(expected);

   END test_update_public_key_updates_records;


   -- %test(test_update_public_key_handles_errors)
   PROCEDURE test_update_public_key_handles_errors IS 
      expected      VARCHAR2(200) := 'ERROR';
      actual        VARCHAR2(200);
      test_key_id   NUMBER;
   BEGIN

      SELECT key_id
        INTO test_key_id
        FROM pi_public_keys
       WHERE key_name = 'test_key';
      
      common.update_public_key(test_key_id, 'ERROR');

      SELECT status
        INTO actual
        FROM pi_public_keys
       WHERE key_id = test_key_id;

      ut.expect(actual,
         'test_update_public_key_handles_errors'
         ).to_equal(expected);

   END test_update_public_key_handles_errors;


END;
/   
SHOW ERRORS

SET SERVEROUTPUT ON SIZE 1000000

BEGIN
   ut.run('test_common');
END;
/

SPOOL OFF

-- Complete
