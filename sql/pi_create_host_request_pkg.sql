------------------------------------------------------------------------------
--
--   PROCEDURE:      pi_create_host_request_pkg.sql
--
--   PURPOSE:        This script is used to create the HOST_REQUEST package. 
--                   This package supports running server scripts from PL/SQL. 
--
--   PARAMETERS:     None. 
--
--   INSTRUCTIONS:   From within SQL*Plus, type:
--                      START pi_create_host_request_pkg.sql
--
--   AUTHOR:         <PERSON>.
--
--   INSTALL SCHEMA: APPS 
--
--
--   Copyright (c) 2025 Kevin <PERSON>uire
--   All rights reserved.
--
--   This software is proprietary and confidential. Unauthorized copying of this
--   file, via any medium, is strictly prohibited without the express permission
--   of the author or owning organization.
--
------------------------------------------------------------------------------

SET ECHO ON


SPOOL pi_create_host_request_pkg

CREATE OR REPLACE PACKAGE host_request AUTHID CURRENT_USER AS

   -- This procedure is a wrapper to DBA_CREDENTIAL CREATE_CREDENTIAL procedure.
   PROCEDURE create_credential (p_credential_name IN VARCHAR2, 
                                p_user_id IN VARCHAR2, 
                                p_password IN VARCHAR2);

   -- This procedure is a wrapper to DBA_CREDENTIAL DROP_CREDENTIAL procedure.
   PROCEDURE remove_credential (p_credential_name IN VARCHAR2);


   -- This helper function is used to check if the given 
   -- credential exists.
   FUNCTION credential_exists (p_credential_name IN VARCHAR2)
      RETURN BOOLEAN;


   -- This procedure is a wrapper to DBA_CREDENTIAL DROP_JOB procedure.
   PROCEDURE remove_job (p_job_name IN VARCHAR2);


   -- This function executes the given host command on the local database 
   -- server and returns the Job Name from USER_SCHEDULER_JOBS. 
   FUNCTION run_host_command(p_command         IN VARCHAR2, 
                             p_credential_name IN VARCHAR2 DEFAULT 'rp_migrateTest',
                             p_enabled         IN BOOLEAN  DEFAULT TRUE)
      RETURN VARCHAR2;


   -- This function returns a JSON list of "new" host requests. 
   FUNCTION get_new_requests 
      RETURN VARCHAR2;

   -- This function creates a new Host Request and returns the ID
   FUNCTION create_request (p_script IN VARCHAR2, p_parameters IN VARCHAR2 DEFAULT NULL) 
      RETURN NUMBER;

   -- This procedure creates a new Host Request
   PROCEDURE create_request (p_script IN VARCHAR2, p_parameters IN VARCHAR2 DEFAULT NULL);

   -- This procedure sets all "new" host requests on "hold".
   PROCEDURE hold_new_requests;

   -- This procedure sets all "held" host requests back to "new".
   PROCEDURE release_held_requests;

   -- This procedure deletes the requests for the given script. It's used to
   -- support shell testing (i.e. clean-up).
   PROCEDURE delete_request(p_id IN NUMBER);

   -- This function finds the lastest request_id for the given script and 
   -- parameters. It's used to support shell testing.
   -- The tests were running into getting "ORA-14551: cannot perform a DML operation 
   -- inside a query" when using the create_request function to return the ID.
   FUNCTION find_request_id (p_script IN VARCHAR2, p_parameters IN VARCHAR2 DEFAULT NULL) 
      RETURN NUMBER;


   -- This procedure is used to update the request status for the various phases
   -- of processing.
   PROCEDURE mark_status(p_id IN NUMBER, p_status IN VARCHAR2);


   -- This function gets the current status for the given request_id.
   FUNCTION get_status (p_id IN NUMBER) 
      RETURN VARCHAR2;


   -- This procedure is used to copy the log file contents in the 
   -- PI_HOST_REQUEST_LOG_STG table to execution_results column in the 
   -- PI_HOST_REQUESTS table. 
   PROCEDURE copy_staged_results(p_request_id IN NUMBER);


   -- This function gets the execution results (if any) for the given request_id.
   FUNCTION get_execution_results (p_id IN NUMBER) 
      RETURN VARCHAR2;



END host_request;
/   
SHOW ERRORS




CREATE OR REPLACE PACKAGE BODY host_request AS
   
   -- This procedure is a wrapper to DBA_CREDENTIAL CREATE_CREDENTIAL procedure.
   PROCEDURE create_credential (p_credential_name IN VARCHAR2, 
                                p_user_id IN VARCHAR2, 
                                p_password IN VARCHAR2) IS
   BEGIN

      DBMS_CREDENTIAL.CREATE_CREDENTIAL(
         credential_name => p_credential_name,
         username        => p_user_id,
         password        => p_password);

   END create_credential;



   -- This procedure is a wrapper to DBA_CREDENTIAL DROP_CREDENTIAL procedure.
   PROCEDURE remove_credential (p_credential_name IN VARCHAR2) IS
   BEGIN

      DBMS_CREDENTIAL.DROP_CREDENTIAL(
         credential_name => p_credential_name);

   END remove_credential;



   -- This helper function is used to check if the given 
   -- credential exists.
   FUNCTION credential_exists (p_credential_name IN VARCHAR2)
      RETURN BOOLEAN IS

      record_count        NUMBER := 0;
      credential_exists   BOOLEAN;

   BEGIN
      SELECT COUNT(*)
        INTO record_count
        FROM all_credentials
       WHERE credential_name = UPPER(p_credential_name);

      IF record_count = 1 THEN
         credential_exists := TRUE;
      ELSE
         credential_exists := FALSE;
      END IF;

      RETURN credential_exists;

   END credential_exists;



   -- This procedure is a wrapper to DBA_CREDENTIAL DROP_JOB procedure. 
   PROCEDURE remove_job (p_job_name IN VARCHAR2) IS
   BEGIN

      DBMS_SCHEDULER.DROP_JOB(p_job_name);

   END remove_job;
   


   -- This function executes the given host command on the local database 
   -- server and returns the Job Name from USER_SCHEDULER_JOBS. 
   --
   -- $ORACLE_HOME/rdbms/admin/externaljob.ora exists. External jobs run as the
   -- user and group specified in this file, which by default is a lowly 
   -- privileged user (nobody). This means it's not using the given credential.
   FUNCTION run_host_command(p_command         IN VARCHAR2, 
                             p_credential_name IN VARCHAR2 DEFAULT 'rp_migrateTest',
                             p_enabled         IN BOOLEAN  DEFAULT TRUE)
      RETURN VARCHAR2 IS

      pi_job_name VARCHAR2(20);  
      pi_command  VARCHAR2(300) := '$USER/simplipied/bin/pi_run_host_command.sh \"' ||
         p_command || '\"';

   BEGIN

      pi_job_name := DBMS_SCHEDULER.GENERATE_JOB_NAME ('PI_JOB_');

      DBMS_SCHEDULER.CREATE_JOB(
         job_name        => pi_job_name,
         job_type        => 'EXECUTABLE',
         job_action      => p_command,
         enabled         => p_enabled,
         comments        => 'This job was submitted by the host_request.run_host_command() procedure.',
         credential_name => p_credential_name
         );

      RETURN pi_job_name;

   END run_host_command;




   -- This function returns a JSON list of "new" host requests. 
   FUNCTION get_new_requests 
      RETURN VARCHAR2 IS

      new_requests  VARCHAR2(3000);

   BEGIN
   
      SELECT JSON_ARRAYAGG(
                JSON_OBJECT(KEY 'id' VALUE id, 
                            KEY 'script_name' VALUE script_name, 
                            KEY 'parameter_string' VALUE parameter_string
                ) ORDER BY id 
             ) AS json_data
        INTO new_requests  
        FROM pi_host_requests
       WHERE status = 'NEW'
       ORDER BY id;   

      -- This format supports shell JSON parsing (jq '.host_requests[]')
      IF '[' || new_requests || ']' = '[]' THEN
         RETURN '{"host_requests":[]}';
      ELSE
         RETURN '{"host_requests":' || new_requests || '}';
      END IF;

   END get_new_requests;




   -- This function creates a new Host Request and returns the ID
   FUNCTION create_request (p_script IN VARCHAR2, p_parameters IN VARCHAR2 DEFAULT NULL) 
      RETURN NUMBER IS

      request_id   NUMBER := 0;

   BEGIN

      INSERT INTO pi_host_requests
      (script_name, parameter_string)
      VALUES
      (p_script, p_parameters)
      RETURNING id INTO request_id;
      

      RETURN request_id; 

   END create_request;


   -- This procedure creates a new Host Request
   PROCEDURE create_request (p_script IN VARCHAR2, p_parameters IN VARCHAR2 DEFAULT NULL) IS

   BEGIN

      INSERT INTO pi_host_requests
      (script_name, parameter_string)
      VALUES
      (p_script, p_parameters);

   END create_request;



   -- This procedure sets all "new" host requests on "hold".
   PROCEDURE hold_new_requests IS
   BEGIN

      UPDATE pi_host_requests
         SET status = 'HOLD'
       WHERE status = 'NEW';

   END hold_new_requests;    


   -- This procedure sets all "held" host requests back to "new".
   PROCEDURE release_held_requests IS
   BEGIN

      UPDATE pi_host_requests
         SET status = 'NEW'
       WHERE status = 'HOLD';

   END release_held_requests;


   -- This procedure deletes the requests for the given script. It's used to
   -- support shell testing (i.e. clean-up).
   PROCEDURE delete_request(p_id IN NUMBER) IS
   BEGIN

      DELETE FROM pi_host_requests
       WHERE id = p_id;

   END delete_request;


   -- This function finds the lastest request_id for the given script and 
   -- parameters. It's used to support shell testing.
   -- The tests were running into getting "ORA-14551: cannot perform a DML operation 
   -- inside a query" when using the create_request function to return the ID.
   FUNCTION find_request_id (p_script IN VARCHAR2, p_parameters IN VARCHAR2 DEFAULT NULL) 
      RETURN NUMBER IS

      request_id   NUMBER := 0;

   BEGIN

      
      SELECT MAX(id)
        INTO request_id 
        FROM pi_host_requests 
       WHERE script_name = p_script
         AND NVL(parameter_string, 'X') = NVL(p_parameters, 'X');

      RETURN request_id; 

   END find_request_id;


   -- This procedure is used to update the request status for the various phases
   -- of processing.
   PROCEDURE mark_status(p_id IN NUMBER, p_status IN VARCHAR2) IS
   BEGIN

      UPDATE pi_host_requests
         SET status = p_status
       WHERE id = p_id;

   END mark_status;



   -- This function gets the current status for the given request_id.
   FUNCTION get_status (p_id IN NUMBER) 
      RETURN VARCHAR2 IS

      current_status   VARCHAR2(255);

   BEGIN

      SELECT status
        INTO current_status 
        FROM pi_host_requests 
       WHERE id = p_id;

      RETURN current_status; 

   END get_status;


   -- This procedure is used to copy the log file contents in the 
   -- PI_HOST_REQUEST_LOG_STG table to execution_results column in the 
   -- PI_HOST_REQUESTS table. 
   PROCEDURE copy_staged_results(p_request_id IN NUMBER) IS

   BEGIN
      
      UPDATE pi_host_requests
         SET execution_results = 
             (SELECT log_contents
                FROM pi_host_request_log_stg
               WHERE request_id = p_request_id)
       WHERE id = p_request_id;

   END copy_staged_results;




   -- This function gets the execution results (if any) for the given request_id.
   FUNCTION get_execution_results (p_id IN NUMBER) 
      RETURN VARCHAR2 IS

      results   VARCHAR2(3000);

   BEGIN

      SELECT execution_results
        INTO results 
        FROM pi_host_requests 
       WHERE id = p_id;

      RETURN results; 

   END get_execution_results;



END host_request;
/   
SHOW ERRORS


SPOOL OFF

-- Complete
