------------------------------------------------------------------------------
--
--   PROCEDURE:      pi_create_spreadsheet_pkg.sql
--
--   PURPOSE:        This script is used to create the SPREADSHEET package. 
--
--   PARAMETERS:     None. 
--
--   INSTRUCTIONS:   From within SQL*Plus, type:
--                      START pi_create_spreadsheet_pkg.sql
--
--   AUTHOR:         <PERSON>re.
--
--   INSTALL SCHEMA: APPS
--
--
--   Copyright (c) 2025 <PERSON>
--   All rights reserved.
--
--   This software is proprietary and confidential. Unauthorized copying of this
--   file, via any medium, is strictly prohibited without the express permission
--   of the author or owning organization.
--
------------------------------------------------------------------------------

SET ECHO ON


SPOOL pi_create_spreadsheet_pkg

CREATE OR REPLACE PACKAGE spreadsheet AUTHID CURRENT_USER AS

   -- This helper function is used to check if the given 
   -- file has been loaded.
   FUNCTION file_loaded (p_filename IN VARCHAR2)
      RETURN BOOLEAN;


   -- This procedure loads the given data file from the 
   -- RPI_INBOUND into the pi_data_files table.  
   PROCEDURE load_data_file(p_file_name IN VARCHAR2);


   -- This function gets the column name from the first row of the spreadsheet.
   FUNCTION get_column_alias(p_file_name IN VARCHAR2, p_col_name IN VARCHAR2)
      RETURN VARCHAR2;

   -- This function builds the SQL string used in the dynamic SELECT of the spreadsheet data.
   FUNCTION build_spreadsheet_sql(p_file_name IN VARCHAR2)
      RETURN VARCHAR2;


   -- This function returns a SYS_REFCURSOR of the spreadsheet data.
   FUNCTION get_spreadsheet_cursor(p_file_name IN VARCHAR2)
      RETURN SYS_REFCURSOR;

   -- SELECT *
   --   FROM TABLE(XMLSEQUENCE(spreadsheet.get_spreadsheet_cursor('LetterInfoUpdateTemplate.xlsx')));   --- Works; returns XML



   -- This procedure loads the given data file into the pi_spreadsheetstaging table.  
   -- If the file is already loaded, it clears the previous data first.
   PROCEDURE stage_data(p_file_name IN VARCHAR2);



END spreadsheet;
/   
SHOW ERRORS


CREATE OR REPLACE PACKAGE BODY spreadsheet AS

   -- This helper function is used to check if the given 
   -- file has been loaded.
   FUNCTION file_loaded (p_filename IN VARCHAR2)
      RETURN BOOLEAN IS

      record_count   NUMBER := 0;
      file_loaded   BOOLEAN;

   BEGIN
      SELECT COUNT(*)
        INTO record_count
        FROM pi_data_files
       WHERE file_name = p_filename;

      IF record_count = 1 THEN
         file_loaded := TRUE;
      ELSE
         file_loaded := FALSE;
      END IF;

      RETURN file_loaded;

   END file_loaded;



   -- This procedure loads the given data file from the 
   -- RPI_INBOUND into the pi_data_files table.  
   PROCEDURE load_data_file(p_file_name IN VARCHAR2) IS

      directory_name   VARCHAR2 (12) := 'RPI_INBOUND';
      file_handle      BFILE;
      return_blob      BLOB := empty_blob();
      file_extension   VARCHAR2(4);
      file_mime_type        VARCHAR2(255);

   BEGIN

      file_extension := UPPER(SUBSTR(p_file_name, INSTR(p_file_name, '.') + 1));

      IF file_extension = 'XLSX' THEN
         file_mime_type := 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet';
      END IF;

      INSERT INTO pi_data_files 
      (file_name, file_type, mime_type, data_file)
      VALUES (p_file_name, file_extension, file_mime_type, empty_blob())
         RETURN data_file INTO return_blob;

      file_handle := bfilename(directory_name, p_file_name);

      IF dbms_lob.fileexists(file_handle) = 1 THEN
         dbms_lob.fileopen(file_handle, dbms_lob.file_readonly);
         dbms_lob.loadfromfile(return_blob, file_handle, dbms_lob.getlength(file_handle));
         dbms_lob.fileclose(file_handle);
      END IF;   

   END load_data_file;


   -- This function gets the column name from the first row of the spreadsheet.
   FUNCTION get_column_alias(p_file_name IN VARCHAR2, p_col_name IN VARCHAR2)
      RETURN VARCHAR2 IS

      l_column_alias   VARCHAR2(100);
      l_sql            VARCHAR2(1000) :=
         'SELECT ' || p_col_name || 
         '  FROM pi_data_files df,' ||
         '       TABLE(apex_data_parser.parse(p_content => df.data_file, p_file_name => df.file_name)) col' ||
         ' WHERE df.file_name = ''' || p_file_name || '''' ||
         '   AND col.line_number = 1';

   BEGIN

      EXECUTE IMMEDIATE l_sql INTO l_column_alias;

      RETURN NVL(l_column_alias, p_col_name);

   EXCEPTION
      WHEN NO_DATA_FOUND THEN
         RETURN p_col_name;

   END get_column_alias;




   -- This function builds the SQL string used in the dynamic SELECT of the spreadsheet data.
   FUNCTION build_spreadsheet_sql(p_file_name IN VARCHAR2)
      RETURN VARCHAR2 IS

      l_sql_statement     VARCHAR2(3000);
      l_col001            VARCHAR2(100) := NVL(get_column_alias(p_file_name, 'COL001'), 'COL001');
      l_col002            VARCHAR2(100) := NVL(get_column_alias(p_file_name, 'COL002'), 'COL002');
      l_col003            VARCHAR2(100) := NVL(get_column_alias(p_file_name, 'COL003'), 'COL003');
      l_col004            VARCHAR2(100) := NVL(get_column_alias(p_file_name, 'COL004'), 'COL004');
      l_col005            VARCHAR2(100) := NVL(get_column_alias(p_file_name, 'COL005'), 'COL005');
      l_col006            VARCHAR2(100) := NVL(get_column_alias(p_file_name, 'COL006'), 'COL006');
      l_col007            VARCHAR2(100) := NVL(get_column_alias(p_file_name, 'COL007'), 'COL007');
      l_col008            VARCHAR2(100) := NVL(get_column_alias(p_file_name, 'COL008'), 'COL008');
      l_col009            VARCHAR2(100) := NVL(get_column_alias(p_file_name, 'COL009'), 'COL009');
      l_col010            VARCHAR2(100) := NVL(get_column_alias(p_file_name, 'COL010'), 'COL010');
      --
      l_col011            VARCHAR2(100) := NVL(get_column_alias(p_file_name, 'COL011'), 'COL011');
      l_col012            VARCHAR2(100) := NVL(get_column_alias(p_file_name, 'COL012'), 'COL012');
      l_col013            VARCHAR2(100) := NVL(get_column_alias(p_file_name, 'COL013'), 'COL013');
      l_col014            VARCHAR2(100) := NVL(get_column_alias(p_file_name, 'COL014'), 'COL014');
      l_col015            VARCHAR2(100) := NVL(get_column_alias(p_file_name, 'COL015'), 'COL015');
      l_col016            VARCHAR2(100) := NVL(get_column_alias(p_file_name, 'COL016'), 'COL016');
      l_col017            VARCHAR2(100) := NVL(get_column_alias(p_file_name, 'COL017'), 'COL017');
      l_col018            VARCHAR2(100) := NVL(get_column_alias(p_file_name, 'COL018'), 'COL018');
      l_col019            VARCHAR2(100) := NVL(get_column_alias(p_file_name, 'COL019'), 'COL019');
      l_col020            VARCHAR2(100) := NVL(get_column_alias(p_file_name, 'COL020'), 'COL020');
      --
      l_col021            VARCHAR2(100) := NVL(get_column_alias(p_file_name, 'COL021'), 'COL021');
      l_col022            VARCHAR2(100) := NVL(get_column_alias(p_file_name, 'COL022'), 'COL022');
      l_col023            VARCHAR2(100) := NVL(get_column_alias(p_file_name, 'COL023'), 'COL023');
      l_col024            VARCHAR2(100) := NVL(get_column_alias(p_file_name, 'COL024'), 'COL024');
      l_col025            VARCHAR2(100) := NVL(get_column_alias(p_file_name, 'COL025'), 'COL025');
      l_col026            VARCHAR2(100) := NVL(get_column_alias(p_file_name, 'COL026'), 'COL026');
      l_col027            VARCHAR2(100) := NVL(get_column_alias(p_file_name, 'COL027'), 'COL027');
      l_col028            VARCHAR2(100) := NVL(get_column_alias(p_file_name, 'COL028'), 'COL028');
      l_col029            VARCHAR2(100) := NVL(get_column_alias(p_file_name, 'COL029'), 'COL029');
      l_col030            VARCHAR2(100) := NVL(get_column_alias(p_file_name, 'COL030'), 'COL030');
      --
      l_col031            VARCHAR2(100) := NVL(get_column_alias(p_file_name, 'COL031'), 'COL031');
      l_col032            VARCHAR2(100) := NVL(get_column_alias(p_file_name, 'COL032'), 'COL032');
      l_col033            VARCHAR2(100) := NVL(get_column_alias(p_file_name, 'COL033'), 'COL033');
      l_col034            VARCHAR2(100) := NVL(get_column_alias(p_file_name, 'COL034'), 'COL034');
      l_col035            VARCHAR2(100) := NVL(get_column_alias(p_file_name, 'COL035'), 'COL035');
      l_col036            VARCHAR2(100) := NVL(get_column_alias(p_file_name, 'COL036'), 'COL036');
      l_col037            VARCHAR2(100) := NVL(get_column_alias(p_file_name, 'COL037'), 'COL037');
      l_col038            VARCHAR2(100) := NVL(get_column_alias(p_file_name, 'COL038'), 'COL038');
      l_col039            VARCHAR2(100) := NVL(get_column_alias(p_file_name, 'COL039'), 'COL039');
      l_col040            VARCHAR2(100) := NVL(get_column_alias(p_file_name, 'COL040'), 'COL040');
      --
      l_col041            VARCHAR2(100) := NVL(get_column_alias(p_file_name, 'COL041'), 'COL041');
      l_col042            VARCHAR2(100) := NVL(get_column_alias(p_file_name, 'COL042'), 'COL042');
      l_col043            VARCHAR2(100) := NVL(get_column_alias(p_file_name, 'COL043'), 'COL043');
      l_col044            VARCHAR2(100) := NVL(get_column_alias(p_file_name, 'COL044'), 'COL044');
      l_col045            VARCHAR2(100) := NVL(get_column_alias(p_file_name, 'COL045'), 'COL045');
      l_col046            VARCHAR2(100) := NVL(get_column_alias(p_file_name, 'COL046'), 'COL046');
      l_col047            VARCHAR2(100) := NVL(get_column_alias(p_file_name, 'COL047'), 'COL047');
      l_col048            VARCHAR2(100) := NVL(get_column_alias(p_file_name, 'COL048'), 'COL048');
      l_col049            VARCHAR2(100) := NVL(get_column_alias(p_file_name, 'COL049'), 'COL049');
      l_col050            VARCHAR2(100) := NVL(get_column_alias(p_file_name, 'COL050'), 'COL050');
   

   BEGIN

      IF file_loaded(p_file_name) THEN
      
         l_sql_statement :=
         'SELECT xl.line_number, ' ||
               ' xl.col001 AS ' || l_col001 || ', ' ||
               ' xl.col002 AS ' || l_col002 || ',' ||
               ' xl.col003 AS ' || l_col003 || ',' ||
               ' xl.col004 AS ' || l_col004 || ',' ||
               ' xl.col005 AS ' || l_col005 || ',' ||
               ' xl.col006 AS ' || l_col006 || ',' ||
               ' xl.col007 AS ' || l_col007 || ',' ||
               ' xl.col008 AS ' || l_col008 || ',' ||
               ' xl.col009 AS ' || l_col009 || ',' ||
               ' xl.col010 AS ' || l_col010 || ',' ||
               ' xl.col011 AS ' || l_col011 || ',' ||
               ' xl.col012 AS ' || l_col012 || ',' ||
               ' xl.col013 AS ' || l_col013 || ',' ||
               ' xl.col014 AS ' || l_col014 || ',' ||
               ' xl.col015 AS ' || l_col015 || ',' ||
               ' xl.col016 AS ' || l_col016 || ',' ||
               ' xl.col017 AS ' || l_col017 || ',' ||
               ' xl.col018 AS ' || l_col018 || ',' ||
               ' xl.col019 AS ' || l_col019 || ',' ||
               ' xl.col020 AS ' || l_col020 || ',' ||
               ' xl.col021 AS ' || l_col021 || ',' ||
               ' xl.col022 AS ' || l_col022 || ',' ||
               ' xl.col023 AS ' || l_col023 || ',' ||
               ' xl.col024 AS ' || l_col024 || ',' ||
               ' xl.col025 AS ' || l_col025 || ',' ||
               ' xl.col026 AS ' || l_col026 || ',' ||
               ' xl.col027 AS ' || l_col027 || ',' ||
               ' xl.col028 AS ' || l_col028 || ',' ||
               ' xl.col029 AS ' || l_col029 || ',' ||
               ' xl.col030 AS ' || l_col030 || ',' ||
               ' xl.col031 AS ' || l_col031 || ',' ||
               ' xl.col032 AS ' || l_col032 || ',' ||
               ' xl.col033 AS ' || l_col033 || ',' ||
               ' xl.col034 AS ' || l_col034 || ',' ||
               ' xl.col035 AS ' || l_col035 || ',' ||
               ' xl.col036 AS ' || l_col036 || ',' ||
               ' xl.col037 AS ' || l_col037 || ',' ||
               ' xl.col038 AS ' || l_col038 || ',' ||
               ' xl.col039 AS ' || l_col039 || ',' ||
               ' xl.col040 AS ' || l_col040 || ',' ||
               ' xl.col041 AS ' || l_col041 || ',' ||
               ' xl.col042 AS ' || l_col042 || ',' ||
               ' xl.col043 AS ' || l_col043 || ',' ||
               ' xl.col044 AS ' || l_col044 || ',' ||
               ' xl.col045 AS ' || l_col045 || ',' ||
               ' xl.col046 AS ' || l_col046 || ',' ||
               ' xl.col047 AS ' || l_col047 || ',' ||
               ' xl.col048 AS ' || l_col048 || ',' ||
               ' xl.col049 AS ' || l_col049 || ',' ||
               ' xl.col050 AS ' || l_col050 || 
          ' FROM pi_data_files df,' ||
               ' TABLE(apex_data_parser.parse(p_content => df.data_file, p_file_name => df.file_name)) xl' ||
         ' WHERE df.file_name = ''' || p_file_name || '''' ||
           ' AND xl.line_number > 1' ||
         ' ORDER BY xl.line_number';
      ELSE
         l_sql_statement :=
         'SELECT xl.line_number,' ||
               ' xl.col001, xl.col002, xl.col003, xl.col004, xl.col005,' ||
               ' xl.col006, xl.col007, xl.col008, xl.col009, xl.col010,' ||
			   --
               ' xl.col011, xl.col012, xl.col013, xl.col014, xl.col015,' ||
               ' xl.col016, xl.col017, xl.col018, xl.col019, xl.col020,' ||
			   --
               ' xl.col021, xl.col022, xl.col023, xl.col024, xl.col025,' ||
               ' xl.col026, xl.col027, xl.col028, xl.col029, xl.col030,' ||
			   --
               ' xl.col031, xl.col032, xl.col033, xl.col034, xl.col035,' ||
               ' xl.col036, xl.col037, xl.col038, xl.col039, xl.col040,' ||
			   --
               ' xl.col041, xl.col042, xl.col043, xl.col044, xl.col045,' ||
               ' xl.col046, xl.col047, xl.col048, xl.col049, xl.col050' || 
          ' FROM pi_data_files df,' ||
               ' TABLE(apex_data_parser.parse(p_content => df.data_file, p_file_name => df.file_name)) xl' ||
         ' WHERE 1 = 2' ||
         ' ORDER BY xl.line_number';
      END IF;

      -- DBMS_OUTPUT.PUT_LINE('SQL: ' || l_sql_statement);

      RETURN l_sql_statement;

   END build_spreadsheet_sql;



   -- This function returns a SYS_REFCURSOR of the spreadsheet data.
   FUNCTION get_spreadsheet_cursor(p_file_name IN VARCHAR2)
      RETURN SYS_REFCURSOR IS

      l_spreadsheet_data  SYS_REFCURSOR;
      l_sql_statement     VARCHAR2(32767);

   BEGIN

      l_sql_statement := build_spreadsheet_sql(p_file_name);

      OPEN l_spreadsheet_data FOR l_sql_statement;

      RETURN l_spreadsheet_data;

   EXCEPTION
      WHEN OTHERS THEN
         IF l_spreadsheet_data%ISOPEN THEN
            CLOSE l_spreadsheet_data;
         END IF;
         RAISE_APPLICATION_ERROR(-20001, 'An error occurred: ' || SQLERRM);

   END get_spreadsheet_cursor; 


   -- This procedure loads the given data file into the pi_spreadsheetstaging table.  
   -- If the file is already loaded, it clears the previous data first.
   PROCEDURE stage_data(p_file_name IN VARCHAR2) IS

      l_spreadsheet_data  SYS_REFCURSOR;
      l_spreadsheet_row   pi_spreadsheetstaging%ROWTYPE;

   BEGIN
   --   l_spreadsheet_data := get_spreadsheet_cursor(p_file_name);

      -- Clear the previous data
      DELETE FROM pi_spreadsheetstaging
       WHERE file_name = p_file_name;

      -- Load the staging table from the Excel file in the pi_data_files table 
      FOR l_spreadsheet_row IN 
         (SELECT p_file_name, xl.line_number, 
                 xl.col001, xl.col002, xl.col003, xl.col004, xl.col005, xl.col006, xl.col007, xl.col008, xl.col009, xl.col010, 
                 xl.col011, xl.col012, xl.col013, xl.col014, xl.col015, xl.col016, xl.col017, xl.col018, xl.col019, xl.col020, 
                 xl.col021, xl.col022, xl.col023, xl.col024, xl.col025, xl.col026, xl.col027, xl.col028, xl.col029, xl.col030, 
                 xl.col031, xl.col032, xl.col033, xl.col034, xl.col035, xl.col036, xl.col037, xl.col038, xl.col039, xl.col040, 
                 xl.col041, xl.col042, xl.col043, xl.col044, xl.col045, xl.col046, xl.col047, xl.col048, xl.col049, xl.col050,
                 xl.col051, xl.col052, xl.col053, xl.col054, xl.col055, xl.col056, xl.col057, xl.col058, xl.col059, xl.col060,
                 xl.col061, xl.col062, xl.col063, xl.col064, xl.col065, xl.col066, xl.col067, xl.col068, xl.col069, xl.col070,
                 xl.col071, xl.col072, xl.col073, xl.col074, xl.col075, xl.col076, xl.col077, xl.col078, xl.col079, xl.col080,
                 xl.col081, xl.col082, xl.col083, xl.col084, xl.col085, xl.col086, xl.col087, xl.col088, xl.col089, xl.col090,
                 xl.col091, xl.col092, xl.col093, xl.col094, xl.col095, xl.col096, xl.col097, xl.col098, xl.col099, xl.col100
            FROM pi_data_files df,
                 TABLE(apex_data_parser.parse(p_content => df.data_file, p_file_name => df.file_name)) xl
           WHERE df.file_name = p_file_name
             AND xl.line_number > 1
           ORDER BY xl.line_number) 
      LOOP
         -- DBMS_OUTPUT.PUT_LINE(p_file_name || ' ' || l_spreadsheet_row.line_number || ' ' ||  
         --    l_spreadsheet_row.col001 || ' ' || l_spreadsheet_row.col002 || ' ' || l_spreadsheet_row.col003 || ' ' || 
         --    l_spreadsheet_row.col004 || ' ' || l_spreadsheet_row.col005);

         INSERT INTO pi_spreadsheetstaging
         (file_name, line_number, 
          col001, col002, col003, col004, col005, col006, col007, col008, col009, col010, 
          col011, col012, col013, col014, col015, col016, col017, col018, col019, col020, 
          col021, col022, col023, col024, col025, col026, col027, col028, col029, col030,
          col031, col032, col033, col034, col035, col036, col037, col038, col039, col040,
          col041, col042, col043, col044, col045, col046, col047, col048, col049, col050,
          col051, col052, col053, col054, col055, col056, col057, col058, col059, col060,
          col061, col062, col063, col064, col065, col066, col067, col068, col069, col070,
          col071, col072, col073, col074, col075, col076, col077, col078, col079, col080,
          col081, col082, col083, col084, col085, col086, col087, col088, col089, col090,
          col091, col092, col093, col094, col095, col096, col097, col098, col099, col100)
         VALUES
         (p_file_name, l_spreadsheet_row.line_number, 
          l_spreadsheet_row.col001, l_spreadsheet_row.col002, l_spreadsheet_row.col003, l_spreadsheet_row.col004, l_spreadsheet_row.col005,                     
          l_spreadsheet_row.col006, l_spreadsheet_row.col007, l_spreadsheet_row.col008, l_spreadsheet_row.col009, l_spreadsheet_row.col010, 
          --
          l_spreadsheet_row.col011, l_spreadsheet_row.col012, l_spreadsheet_row.col013, l_spreadsheet_row.col014, l_spreadsheet_row.col015,                     
          l_spreadsheet_row.col016, l_spreadsheet_row.col017, l_spreadsheet_row.col018, l_spreadsheet_row.col019, l_spreadsheet_row.col020, 
          --
          l_spreadsheet_row.col021, l_spreadsheet_row.col022, l_spreadsheet_row.col023, l_spreadsheet_row.col024, l_spreadsheet_row.col025,                     
          l_spreadsheet_row.col026, l_spreadsheet_row.col027, l_spreadsheet_row.col028, l_spreadsheet_row.col029, l_spreadsheet_row.col030,
          --
          l_spreadsheet_row.col031, l_spreadsheet_row.col032, l_spreadsheet_row.col033, l_spreadsheet_row.col034, l_spreadsheet_row.col035,                     
          l_spreadsheet_row.col036, l_spreadsheet_row.col037, l_spreadsheet_row.col038, l_spreadsheet_row.col039, l_spreadsheet_row.col040,
          --
          l_spreadsheet_row.col041, l_spreadsheet_row.col042, l_spreadsheet_row.col043, l_spreadsheet_row.col044, l_spreadsheet_row.col045,                     
          l_spreadsheet_row.col046, l_spreadsheet_row.col047, l_spreadsheet_row.col048, l_spreadsheet_row.col049, l_spreadsheet_row.col050,
          --
          l_spreadsheet_row.col051, l_spreadsheet_row.col052, l_spreadsheet_row.col053, l_spreadsheet_row.col054, l_spreadsheet_row.col055,                     
          l_spreadsheet_row.col056, l_spreadsheet_row.col057, l_spreadsheet_row.col058, l_spreadsheet_row.col059, l_spreadsheet_row.col060,
          --
          l_spreadsheet_row.col061, l_spreadsheet_row.col062, l_spreadsheet_row.col063, l_spreadsheet_row.col064, l_spreadsheet_row.col065,                     
          l_spreadsheet_row.col066, l_spreadsheet_row.col067, l_spreadsheet_row.col068, l_spreadsheet_row.col069, l_spreadsheet_row.col070,
          --
          l_spreadsheet_row.col071, l_spreadsheet_row.col072, l_spreadsheet_row.col073, l_spreadsheet_row.col074, l_spreadsheet_row.col075,                     
          l_spreadsheet_row.col076, l_spreadsheet_row.col077, l_spreadsheet_row.col078, l_spreadsheet_row.col079, l_spreadsheet_row.col080,
          --
          l_spreadsheet_row.col081, l_spreadsheet_row.col082, l_spreadsheet_row.col083, l_spreadsheet_row.col084, l_spreadsheet_row.col085,                     
          l_spreadsheet_row.col086, l_spreadsheet_row.col087, l_spreadsheet_row.col088, l_spreadsheet_row.col089, l_spreadsheet_row.col090,
          --
          l_spreadsheet_row.col091, l_spreadsheet_row.col092, l_spreadsheet_row.col093, l_spreadsheet_row.col094, l_spreadsheet_row.col095,                     
          l_spreadsheet_row.col096, l_spreadsheet_row.col097, l_spreadsheet_row.col098, l_spreadsheet_row.col099, l_spreadsheet_row.col100);
      END LOOP;
      
   EXCEPTION
      WHEN OTHERS THEN
         IF l_spreadsheet_data%ISOPEN THEN
            CLOSE l_spreadsheet_data;
         END IF;
         RAISE_APPLICATION_ERROR(-20001, 'An error occurred: ' || SQLERRM);

   END stage_data;


END spreadsheet;
/   
SHOW ERRORS

SPOOL OFF

-- Complete
