------------------------------------------------------------------------------
--
--   PROCEDURE:      pi_create_spreadsheet_pkg.sql
--
--   PURPOSE:        This script is used to create the SPREADSHEET package. 
--
--   PARAMETERS:     None. 
--
--   INSTRUCTIONS:   From within SQL*Plus, type:
--                      START pi_create_spreadsheet_pkg.sql
--
--   AUTHOR:         <PERSON>re.
--
--   INSTALL SCHEMA: APPS
--
--
--   Copyright (c) 2025 <PERSON>
--   All rights reserved.
--
--   This software is proprietary and confidential. Unauthorized copying of this
--   file, via any medium, is strictly prohibited without the express permission
--   of the author or owning organization.
--
------------------------------------------------------------------------------

SET ECHO ON


SPOOL pi_create_spreadsheet_pkg

CREATE OR REPLACE PACKAGE spreadsheet AUTHID CURRENT_USER AS

   -- This helper function is used to check if the given 
   -- file has been loaded.
   FUNCTION file_loaded (p_filename IN VARCHAR2)
      RETURN BOOLEAN;


   -- This procedure loads the given data file from the 
   -- RPI_INBOUND into the pi_data_files table.  
   PROCEDURE load_data_file(p_file_name IN VARCHAR2);


   -- This function gets the column name from the first row of the spreadsheet.
   FUNCTION get_column_alias(p_file_name IN VARCHAR2, p_col_name IN VARCHAR2)
      RETURN VARCHAR2;

   -- This function builds the SQL string used in the dynamic SELECT of the spreadsheet data.
   FUNCTION build_spreadsheet_sql(p_file_name IN VARCHAR2)
      RETURN VARCHAR2;


   -- This function returns a SYS_REFCURSOR of the spreadsheet data.
   FUNCTION get_spreadsheet_cursor(p_file_name IN VARCHAR2)
      RETURN SYS_REFCURSOR;

   -- SELECT *
   --   FROM TABLE(XMLSEQUENCE(spreadsheet.get_spreadsheet_cursor('LetterInfoUpdateTemplate.xlsx')));   --- Works; returns XML



   -- This procedure loads the given data file into the pi_spreadsheetstaging table.  
   -- If the file is already loaded, it clears the previous data first.
   PROCEDURE stage_data(p_file_name IN VARCHAR2);




   -- This function returns a list of the worksheets for the given spreadsheet
   -- as a SYS_REFCURSOR.
   FUNCTION get_worksheets_cursor(p_spreadsheet_name IN VARCHAR2)
      RETURN SYS_REFCURSOR;


   -- This function returns a SYS_REFCURSOR of the open payables claims data.
   FUNCTION get_open_payables_claims_cursor
      RETURN SYS_REFCURSOR;


   -- This function returns a SYS_REFCURSOR of the open payables instructions data.
   FUNCTION get_open_payables_instructions_cursor
      RETURN SYS_REFCURSOR;


   -- This function returns the open payables claims Jinja2 template.
   FUNCTION get_open_payables_claims_template
      RETURN VARCHAR2;   

   -- This function returns the worksheet (Jinja2) template.
   FUNCTION get_worksheet_template (p_spreadsheet_name IN VARCHAR2, p_worksheet_name IN VARCHAR2)
      RETURN VARCHAR2;


   -- This function gets the file name for the given spreadsheet.
   FUNCTION get_spreadsheet_filename(p_spreadsheet_name IN VARCHAR2)
      RETURN VARCHAR2; 


   -- This procedure reloads the base spreadsheet template
   PROCEDURE reload_spreadsheet_template;

END spreadsheet;
/   
SHOW ERRORS


CREATE OR REPLACE PACKAGE BODY spreadsheet AS

   -- This helper function is used to check if the given 
   -- file has been loaded.
   FUNCTION file_loaded (p_filename IN VARCHAR2)
      RETURN BOOLEAN IS

      record_count   NUMBER := 0;
      file_loaded   BOOLEAN;

   BEGIN
      SELECT COUNT(*)
        INTO record_count
        FROM pi_data_files
       WHERE file_name = p_filename;

      IF record_count = 1 THEN
         file_loaded := TRUE;
      ELSE
         file_loaded := FALSE;
      END IF;

      RETURN file_loaded;

   END file_loaded;



   -- This procedure loads the given data file from the 
   -- RPI_INBOUND into the pi_data_files table.  
   PROCEDURE load_data_file(p_file_name IN VARCHAR2) IS

      directory_name   VARCHAR2 (12) := 'RPI_INBOUND';
      file_handle      BFILE;
      return_blob      BLOB := empty_blob();
      file_extension   VARCHAR2(4);
      file_mime_type        VARCHAR2(255);

   BEGIN

      file_extension := UPPER(SUBSTR(p_file_name, INSTR(p_file_name, '.') + 1));

      IF file_extension = 'XLSX' THEN
         file_mime_type := 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet';
      END IF;

      INSERT INTO pi_data_files 
      (file_name, file_type, mime_type, data_file)
      VALUES (p_file_name, file_extension, file_mime_type, empty_blob())
         RETURN data_file INTO return_blob;

      file_handle := bfilename(directory_name, p_file_name);

      IF dbms_lob.fileexists(file_handle) = 1 THEN
         dbms_lob.fileopen(file_handle, dbms_lob.file_readonly);
         dbms_lob.loadfromfile(return_blob, file_handle, dbms_lob.getlength(file_handle));
         dbms_lob.fileclose(file_handle);
      END IF;   

   END load_data_file;


   -- This function gets the column name from the first row of the spreadsheet.
   FUNCTION get_column_alias(p_file_name IN VARCHAR2, p_col_name IN VARCHAR2)
      RETURN VARCHAR2 IS

      l_column_alias   VARCHAR2(100);
      l_sql            VARCHAR2(1000) :=
         'SELECT ' || p_col_name || 
         '  FROM pi_data_files df,' ||
         '       TABLE(apex_data_parser.parse(p_content => df.data_file, p_file_name => df.file_name)) col' ||
         ' WHERE df.file_name = ''' || p_file_name || '''' ||
         '   AND col.line_number = 1';

   BEGIN

      EXECUTE IMMEDIATE l_sql INTO l_column_alias;

      RETURN NVL(l_column_alias, p_col_name);

   EXCEPTION
      WHEN NO_DATA_FOUND THEN
         RETURN p_col_name;

   END get_column_alias;




   -- This function builds the SQL string used in the dynamic SELECT of the spreadsheet data.
   FUNCTION build_spreadsheet_sql(p_file_name IN VARCHAR2)
      RETURN VARCHAR2 IS

      l_sql_statement     VARCHAR2(3000);
      l_col001            VARCHAR2(100) := NVL(get_column_alias(p_file_name, 'COL001'), 'COL001');
      l_col002            VARCHAR2(100) := NVL(get_column_alias(p_file_name, 'COL002'), 'COL002');
      l_col003            VARCHAR2(100) := NVL(get_column_alias(p_file_name, 'COL003'), 'COL003');
      l_col004            VARCHAR2(100) := NVL(get_column_alias(p_file_name, 'COL004'), 'COL004');
      l_col005            VARCHAR2(100) := NVL(get_column_alias(p_file_name, 'COL005'), 'COL005');
      l_col006            VARCHAR2(100) := NVL(get_column_alias(p_file_name, 'COL006'), 'COL006');
      l_col007            VARCHAR2(100) := NVL(get_column_alias(p_file_name, 'COL007'), 'COL007');
      l_col008            VARCHAR2(100) := NVL(get_column_alias(p_file_name, 'COL008'), 'COL008');
      l_col009            VARCHAR2(100) := NVL(get_column_alias(p_file_name, 'COL009'), 'COL009');
      l_col010            VARCHAR2(100) := NVL(get_column_alias(p_file_name, 'COL010'), 'COL010');
      --
      l_col011            VARCHAR2(100) := NVL(get_column_alias(p_file_name, 'COL011'), 'COL011');
      l_col012            VARCHAR2(100) := NVL(get_column_alias(p_file_name, 'COL012'), 'COL012');
      l_col013            VARCHAR2(100) := NVL(get_column_alias(p_file_name, 'COL013'), 'COL013');
      l_col014            VARCHAR2(100) := NVL(get_column_alias(p_file_name, 'COL014'), 'COL014');
      l_col015            VARCHAR2(100) := NVL(get_column_alias(p_file_name, 'COL015'), 'COL015');
      l_col016            VARCHAR2(100) := NVL(get_column_alias(p_file_name, 'COL016'), 'COL016');
      l_col017            VARCHAR2(100) := NVL(get_column_alias(p_file_name, 'COL017'), 'COL017');
      l_col018            VARCHAR2(100) := NVL(get_column_alias(p_file_name, 'COL018'), 'COL018');
      l_col019            VARCHAR2(100) := NVL(get_column_alias(p_file_name, 'COL019'), 'COL019');
      l_col020            VARCHAR2(100) := NVL(get_column_alias(p_file_name, 'COL020'), 'COL020');
      --
      l_col021            VARCHAR2(100) := NVL(get_column_alias(p_file_name, 'COL021'), 'COL021');
      l_col022            VARCHAR2(100) := NVL(get_column_alias(p_file_name, 'COL022'), 'COL022');
      l_col023            VARCHAR2(100) := NVL(get_column_alias(p_file_name, 'COL023'), 'COL023');
      l_col024            VARCHAR2(100) := NVL(get_column_alias(p_file_name, 'COL024'), 'COL024');
      l_col025            VARCHAR2(100) := NVL(get_column_alias(p_file_name, 'COL025'), 'COL025');
      l_col026            VARCHAR2(100) := NVL(get_column_alias(p_file_name, 'COL026'), 'COL026');
      l_col027            VARCHAR2(100) := NVL(get_column_alias(p_file_name, 'COL027'), 'COL027');
      l_col028            VARCHAR2(100) := NVL(get_column_alias(p_file_name, 'COL028'), 'COL028');
      l_col029            VARCHAR2(100) := NVL(get_column_alias(p_file_name, 'COL029'), 'COL029');
      l_col030            VARCHAR2(100) := NVL(get_column_alias(p_file_name, 'COL030'), 'COL030');
      --
      l_col031            VARCHAR2(100) := NVL(get_column_alias(p_file_name, 'COL031'), 'COL031');
      l_col032            VARCHAR2(100) := NVL(get_column_alias(p_file_name, 'COL032'), 'COL032');
      l_col033            VARCHAR2(100) := NVL(get_column_alias(p_file_name, 'COL033'), 'COL033');
      l_col034            VARCHAR2(100) := NVL(get_column_alias(p_file_name, 'COL034'), 'COL034');
      l_col035            VARCHAR2(100) := NVL(get_column_alias(p_file_name, 'COL035'), 'COL035');
      l_col036            VARCHAR2(100) := NVL(get_column_alias(p_file_name, 'COL036'), 'COL036');
      l_col037            VARCHAR2(100) := NVL(get_column_alias(p_file_name, 'COL037'), 'COL037');
      l_col038            VARCHAR2(100) := NVL(get_column_alias(p_file_name, 'COL038'), 'COL038');
      l_col039            VARCHAR2(100) := NVL(get_column_alias(p_file_name, 'COL039'), 'COL039');
      l_col040            VARCHAR2(100) := NVL(get_column_alias(p_file_name, 'COL040'), 'COL040');
      --
      l_col041            VARCHAR2(100) := NVL(get_column_alias(p_file_name, 'COL041'), 'COL041');
      l_col042            VARCHAR2(100) := NVL(get_column_alias(p_file_name, 'COL042'), 'COL042');
      l_col043            VARCHAR2(100) := NVL(get_column_alias(p_file_name, 'COL043'), 'COL043');
      l_col044            VARCHAR2(100) := NVL(get_column_alias(p_file_name, 'COL044'), 'COL044');
      l_col045            VARCHAR2(100) := NVL(get_column_alias(p_file_name, 'COL045'), 'COL045');
      l_col046            VARCHAR2(100) := NVL(get_column_alias(p_file_name, 'COL046'), 'COL046');
      l_col047            VARCHAR2(100) := NVL(get_column_alias(p_file_name, 'COL047'), 'COL047');
      l_col048            VARCHAR2(100) := NVL(get_column_alias(p_file_name, 'COL048'), 'COL048');
      l_col049            VARCHAR2(100) := NVL(get_column_alias(p_file_name, 'COL049'), 'COL049');
      l_col050            VARCHAR2(100) := NVL(get_column_alias(p_file_name, 'COL050'), 'COL050');
   

   BEGIN

      IF file_loaded(p_file_name) THEN
      
         l_sql_statement :=
         'SELECT xl.line_number, ' ||
               ' xl.col001 AS ' || l_col001 || ', ' ||
               ' xl.col002 AS ' || l_col002 || ',' ||
               ' xl.col003 AS ' || l_col003 || ',' ||
               ' xl.col004 AS ' || l_col004 || ',' ||
               ' xl.col005 AS ' || l_col005 || ',' ||
               ' xl.col006 AS ' || l_col006 || ',' ||
               ' xl.col007 AS ' || l_col007 || ',' ||
               ' xl.col008 AS ' || l_col008 || ',' ||
               ' xl.col009 AS ' || l_col009 || ',' ||
               ' xl.col010 AS ' || l_col010 || ',' ||
               ' xl.col011 AS ' || l_col011 || ',' ||
               ' xl.col012 AS ' || l_col012 || ',' ||
               ' xl.col013 AS ' || l_col013 || ',' ||
               ' xl.col014 AS ' || l_col014 || ',' ||
               ' xl.col015 AS ' || l_col015 || ',' ||
               ' xl.col016 AS ' || l_col016 || ',' ||
               ' xl.col017 AS ' || l_col017 || ',' ||
               ' xl.col018 AS ' || l_col018 || ',' ||
               ' xl.col019 AS ' || l_col019 || ',' ||
               ' xl.col020 AS ' || l_col020 || ',' ||
               ' xl.col021 AS ' || l_col021 || ',' ||
               ' xl.col022 AS ' || l_col022 || ',' ||
               ' xl.col023 AS ' || l_col023 || ',' ||
               ' xl.col024 AS ' || l_col024 || ',' ||
               ' xl.col025 AS ' || l_col025 || ',' ||
               ' xl.col026 AS ' || l_col026 || ',' ||
               ' xl.col027 AS ' || l_col027 || ',' ||
               ' xl.col028 AS ' || l_col028 || ',' ||
               ' xl.col029 AS ' || l_col029 || ',' ||
               ' xl.col030 AS ' || l_col030 || ',' ||
               ' xl.col031 AS ' || l_col031 || ',' ||
               ' xl.col032 AS ' || l_col032 || ',' ||
               ' xl.col033 AS ' || l_col033 || ',' ||
               ' xl.col034 AS ' || l_col034 || ',' ||
               ' xl.col035 AS ' || l_col035 || ',' ||
               ' xl.col036 AS ' || l_col036 || ',' ||
               ' xl.col037 AS ' || l_col037 || ',' ||
               ' xl.col038 AS ' || l_col038 || ',' ||
               ' xl.col039 AS ' || l_col039 || ',' ||
               ' xl.col040 AS ' || l_col040 || ',' ||
               ' xl.col041 AS ' || l_col041 || ',' ||
               ' xl.col042 AS ' || l_col042 || ',' ||
               ' xl.col043 AS ' || l_col043 || ',' ||
               ' xl.col044 AS ' || l_col044 || ',' ||
               ' xl.col045 AS ' || l_col045 || ',' ||
               ' xl.col046 AS ' || l_col046 || ',' ||
               ' xl.col047 AS ' || l_col047 || ',' ||
               ' xl.col048 AS ' || l_col048 || ',' ||
               ' xl.col049 AS ' || l_col049 || ',' ||
               ' xl.col050 AS ' || l_col050 || 
          ' FROM pi_data_files df,' ||
               ' TABLE(apex_data_parser.parse(p_content => df.data_file, p_file_name => df.file_name)) xl' ||
         ' WHERE df.file_name = ''' || p_file_name || '''' ||
           ' AND xl.line_number > 1' ||
         ' ORDER BY xl.line_number';
      ELSE
         l_sql_statement :=
         'SELECT xl.line_number,' ||
               ' xl.col001, xl.col002, xl.col003, xl.col004, xl.col005,' ||
               ' xl.col006, xl.col007, xl.col008, xl.col009, xl.col010,' ||
			   --
               ' xl.col011, xl.col012, xl.col013, xl.col014, xl.col015,' ||
               ' xl.col016, xl.col017, xl.col018, xl.col019, xl.col020,' ||
			   --
               ' xl.col021, xl.col022, xl.col023, xl.col024, xl.col025,' ||
               ' xl.col026, xl.col027, xl.col028, xl.col029, xl.col030,' ||
			   --
               ' xl.col031, xl.col032, xl.col033, xl.col034, xl.col035,' ||
               ' xl.col036, xl.col037, xl.col038, xl.col039, xl.col040,' ||
			   --
               ' xl.col041, xl.col042, xl.col043, xl.col044, xl.col045,' ||
               ' xl.col046, xl.col047, xl.col048, xl.col049, xl.col050' || 
          ' FROM pi_data_files df,' ||
               ' TABLE(apex_data_parser.parse(p_content => df.data_file, p_file_name => df.file_name)) xl' ||
         ' WHERE 1 = 2' ||
         ' ORDER BY xl.line_number';
      END IF;

      -- DBMS_OUTPUT.PUT_LINE('SQL: ' || l_sql_statement);

      RETURN l_sql_statement;

   END build_spreadsheet_sql;



   -- This function returns a SYS_REFCURSOR of the spreadsheet data.
   FUNCTION get_spreadsheet_cursor(p_file_name IN VARCHAR2)
      RETURN SYS_REFCURSOR IS

      l_spreadsheet_data  SYS_REFCURSOR;
      l_sql_statement     VARCHAR2(32767);

   BEGIN

      l_sql_statement := build_spreadsheet_sql(p_file_name);

      OPEN l_spreadsheet_data FOR l_sql_statement;

      RETURN l_spreadsheet_data;

   EXCEPTION
      WHEN OTHERS THEN
         IF l_spreadsheet_data%ISOPEN THEN
            CLOSE l_spreadsheet_data;
         END IF;
         RAISE_APPLICATION_ERROR(-20001, 'An error occurred: ' || SQLERRM);

   END get_spreadsheet_cursor; 


   -- This procedure loads the given data file into the pi_spreadsheetstaging table.  
   -- If the file is already loaded, it clears the previous data first.
   PROCEDURE stage_data(p_file_name IN VARCHAR2) IS

      l_spreadsheet_data  SYS_REFCURSOR;
      l_spreadsheet_row   pi_spreadsheetstaging%ROWTYPE;

   BEGIN
   --   l_spreadsheet_data := get_spreadsheet_cursor(p_file_name);

      -- Clear the previous data
      DELETE FROM pi_spreadsheetstaging
       WHERE file_name = p_file_name;

      -- Load the staging table from the Excel file in the pi_data_files table 
      FOR l_spreadsheet_row IN 
         (SELECT p_file_name, xl.line_number, 
                 xl.col001, xl.col002, xl.col003, xl.col004, xl.col005, xl.col006, xl.col007, xl.col008, xl.col009, xl.col010, 
                 xl.col011, xl.col012, xl.col013, xl.col014, xl.col015, xl.col016, xl.col017, xl.col018, xl.col019, xl.col020, 
                 xl.col021, xl.col022, xl.col023, xl.col024, xl.col025, xl.col026, xl.col027, xl.col028, xl.col029, xl.col030, 
                 xl.col031, xl.col032, xl.col033, xl.col034, xl.col035, xl.col036, xl.col037, xl.col038, xl.col039, xl.col040, 
                 xl.col041, xl.col042, xl.col043, xl.col044, xl.col045, xl.col046, xl.col047, xl.col048, xl.col049, xl.col050,
                 xl.col051, xl.col052, xl.col053, xl.col054, xl.col055, xl.col056, xl.col057, xl.col058, xl.col059, xl.col060,
                 xl.col061, xl.col062, xl.col063, xl.col064, xl.col065, xl.col066, xl.col067, xl.col068, xl.col069, xl.col070,
                 xl.col071, xl.col072, xl.col073, xl.col074, xl.col075, xl.col076, xl.col077, xl.col078, xl.col079, xl.col080,
                 xl.col081, xl.col082, xl.col083, xl.col084, xl.col085, xl.col086, xl.col087, xl.col088, xl.col089, xl.col090,
                 xl.col091, xl.col092, xl.col093, xl.col094, xl.col095, xl.col096, xl.col097, xl.col098, xl.col099, xl.col100
            FROM pi_data_files df,
                 TABLE(apex_data_parser.parse(p_content => df.data_file, p_file_name => df.file_name)) xl
           WHERE df.file_name = p_file_name
             AND xl.line_number > 1
           ORDER BY xl.line_number) 
      LOOP
         -- DBMS_OUTPUT.PUT_LINE(p_file_name || ' ' || l_spreadsheet_row.line_number || ' ' ||  
         --    l_spreadsheet_row.col001 || ' ' || l_spreadsheet_row.col002 || ' ' || l_spreadsheet_row.col003 || ' ' || 
         --    l_spreadsheet_row.col004 || ' ' || l_spreadsheet_row.col005);

         INSERT INTO pi_spreadsheetstaging
         (file_name, line_number, 
          col001, col002, col003, col004, col005, col006, col007, col008, col009, col010, 
          col011, col012, col013, col014, col015, col016, col017, col018, col019, col020, 
          col021, col022, col023, col024, col025, col026, col027, col028, col029, col030,
          col031, col032, col033, col034, col035, col036, col037, col038, col039, col040,
          col041, col042, col043, col044, col045, col046, col047, col048, col049, col050,
          col051, col052, col053, col054, col055, col056, col057, col058, col059, col060,
          col061, col062, col063, col064, col065, col066, col067, col068, col069, col070,
          col071, col072, col073, col074, col075, col076, col077, col078, col079, col080,
          col081, col082, col083, col084, col085, col086, col087, col088, col089, col090,
          col091, col092, col093, col094, col095, col096, col097, col098, col099, col100)
         VALUES
         (p_file_name, l_spreadsheet_row.line_number, 
          l_spreadsheet_row.col001, l_spreadsheet_row.col002, l_spreadsheet_row.col003, l_spreadsheet_row.col004, l_spreadsheet_row.col005,                     
          l_spreadsheet_row.col006, l_spreadsheet_row.col007, l_spreadsheet_row.col008, l_spreadsheet_row.col009, l_spreadsheet_row.col010, 
          --
          l_spreadsheet_row.col011, l_spreadsheet_row.col012, l_spreadsheet_row.col013, l_spreadsheet_row.col014, l_spreadsheet_row.col015,                     
          l_spreadsheet_row.col016, l_spreadsheet_row.col017, l_spreadsheet_row.col018, l_spreadsheet_row.col019, l_spreadsheet_row.col020, 
          --
          l_spreadsheet_row.col021, l_spreadsheet_row.col022, l_spreadsheet_row.col023, l_spreadsheet_row.col024, l_spreadsheet_row.col025,                     
          l_spreadsheet_row.col026, l_spreadsheet_row.col027, l_spreadsheet_row.col028, l_spreadsheet_row.col029, l_spreadsheet_row.col030,
          --
          l_spreadsheet_row.col031, l_spreadsheet_row.col032, l_spreadsheet_row.col033, l_spreadsheet_row.col034, l_spreadsheet_row.col035,                     
          l_spreadsheet_row.col036, l_spreadsheet_row.col037, l_spreadsheet_row.col038, l_spreadsheet_row.col039, l_spreadsheet_row.col040,
          --
          l_spreadsheet_row.col041, l_spreadsheet_row.col042, l_spreadsheet_row.col043, l_spreadsheet_row.col044, l_spreadsheet_row.col045,                     
          l_spreadsheet_row.col046, l_spreadsheet_row.col047, l_spreadsheet_row.col048, l_spreadsheet_row.col049, l_spreadsheet_row.col050,
          --
          l_spreadsheet_row.col051, l_spreadsheet_row.col052, l_spreadsheet_row.col053, l_spreadsheet_row.col054, l_spreadsheet_row.col055,                     
          l_spreadsheet_row.col056, l_spreadsheet_row.col057, l_spreadsheet_row.col058, l_spreadsheet_row.col059, l_spreadsheet_row.col060,
          --
          l_spreadsheet_row.col061, l_spreadsheet_row.col062, l_spreadsheet_row.col063, l_spreadsheet_row.col064, l_spreadsheet_row.col065,                     
          l_spreadsheet_row.col066, l_spreadsheet_row.col067, l_spreadsheet_row.col068, l_spreadsheet_row.col069, l_spreadsheet_row.col070,
          --
          l_spreadsheet_row.col071, l_spreadsheet_row.col072, l_spreadsheet_row.col073, l_spreadsheet_row.col074, l_spreadsheet_row.col075,                     
          l_spreadsheet_row.col076, l_spreadsheet_row.col077, l_spreadsheet_row.col078, l_spreadsheet_row.col079, l_spreadsheet_row.col080,
          --
          l_spreadsheet_row.col081, l_spreadsheet_row.col082, l_spreadsheet_row.col083, l_spreadsheet_row.col084, l_spreadsheet_row.col085,                     
          l_spreadsheet_row.col086, l_spreadsheet_row.col087, l_spreadsheet_row.col088, l_spreadsheet_row.col089, l_spreadsheet_row.col090,
          --
          l_spreadsheet_row.col091, l_spreadsheet_row.col092, l_spreadsheet_row.col093, l_spreadsheet_row.col094, l_spreadsheet_row.col095,                     
          l_spreadsheet_row.col096, l_spreadsheet_row.col097, l_spreadsheet_row.col098, l_spreadsheet_row.col099, l_spreadsheet_row.col100);
      END LOOP;
      
   EXCEPTION
      WHEN OTHERS THEN
         IF l_spreadsheet_data%ISOPEN THEN
            CLOSE l_spreadsheet_data;
         END IF;
         RAISE_APPLICATION_ERROR(-20001, 'An error occurred: ' || SQLERRM);

   END stage_data;




   ---------------------------------------------------------------------------
   -- Open Payables export support

   -- This function returns a list of the worksheets for the given spreadsheet
   -- as a SYS_REFCURSOR.
   FUNCTION get_worksheets_cursor(p_spreadsheet_name IN VARCHAR2)
      RETURN SYS_REFCURSOR IS

      l_worksheets        SYS_REFCURSOR;

   BEGIN

      OPEN l_worksheets FOR
         SELECT *
           FROM pi_worksheet_templates
          WHERE spreadsheet_id = 
                (SELECT spreadsheet_id
                   FROM pi_spreadsheet_templates
                  WHERE spreadsheet_name = p_spreadsheet_name)
          ORDER BY worksheet_order;

      RETURN l_worksheets;

   EXCEPTION
      WHEN OTHERS THEN
         IF l_worksheets%ISOPEN THEN
            CLOSE l_worksheets;
         END IF;
         RAISE_APPLICATION_ERROR(-20001, 'An error occurred: ' || SQLERRM);

   END get_worksheets_cursor; 


   -- This function returns a SYS_REFCURSOR of the open payables claims data.
   FUNCTION get_open_payables_claims_cursor
      RETURN SYS_REFCURSOR IS

      l_claims_data       SYS_REFCURSOR;
      l_sql_statement     VARCHAR2(32767) := 'SELECT *
                                                FROM pi_open_payables_claims_staging
                                               WHERE sourcerecordid <> ''SourceRecordID'' 
                                               ORDER BY TO_NUMBER(seqno)'; -- Skip the first row; it contains the internal column names

   BEGIN

      OPEN l_claims_data FOR l_sql_statement;

      RETURN l_claims_data;

   EXCEPTION
      WHEN OTHERS THEN
         IF l_claims_data%ISOPEN THEN
            CLOSE l_claims_data;
         END IF;
         RAISE_APPLICATION_ERROR(-20001, 'An error occurred: ' || SQLERRM);

   END get_open_payables_claims_cursor; 



   -- This function returns a SYS_REFCURSOR of the open payables instructions data.
   FUNCTION get_open_payables_instructions_cursor
      RETURN SYS_REFCURSOR IS

      l_instructions      SYS_REFCURSOR;
      l_sql_statement     VARCHAR2(32767) := 'SELECT *
                                                FROM pi_open_payables_instructions_staging 
                                               ORDER BY record_no'; 

   BEGIN

      OPEN l_instructions FOR l_sql_statement;

      RETURN l_instructions;

   EXCEPTION
      WHEN OTHERS THEN
         IF l_instructions%ISOPEN THEN
            CLOSE l_instructions;
         END IF;
         RAISE_APPLICATION_ERROR(-20001, 'An error occurred: ' || SQLERRM);

   END get_open_payables_instructions_cursor; 



   -- This function returns the open payables claims Jinja2 template.
   FUNCTION get_open_payables_claims_template
      RETURN VARCHAR2 IS

      l_claims_data       VARCHAR2(32767);
      l_sql_statement     VARCHAR2(32767) := 'SELECT *
                                               FROM pi_open_payables_claims_staging
                                              ORDER BY seqno';

   BEGIN

      -- Change to build from a table
/*      
      SELECT '[' ||
'    {% for claim in claims %}' ||
'        {' ||
'            "SourceRecordID": "{{ claim.SourceRecordID }}",' ||
'            "SeqNo": "{{ claim.SeqNo }}",' ||
'            "StateID": "{{ claim.StateID }}",' ||
'            "ProviderName": "{{ claim.ProviderName }}",' ||
'            "ProviderID": "{{ claim.ProviderID }}",' ||
'            "ProviderTIN": "{{ claim.ProviderTIN }}",' ||
'            "LOB": "{{ claim.LOB }}",' ||
'            "ProviderAccountID": "{{ claim.ProviderAccountID }}",' ||
'            "ClaimNumber": "{{ claim.ClaimNumber }}",' ||
'            "ClaimPaidDate": "{{ claim.ClaimPaidDate }}",' ||
'            "ClaimPaidAmount": "{{ claim.ClaimPaidAmount }}",' ||
'            "OpenPayableAmount": "{{ claim.OpenPayableAmount }}",' ||
'            "OpenPayableDate": "{{ claim.OpenPayableDate }}",' ||
'            "OpenPayableAge": "{{ claim.OpenPayableAge }}",' ||
'            "ShiftToAccount": "{{ claim.ShiftToAccount }}"' ||
'        }{% if not loop.last %},{% endif %}' ||
'        {% endfor %}' ||
']'
        INTO l_claims_data 
        FROM dual;
*/

      SELECT '{' ||
             '"worksheet_name": "Claims", ' ||
             '"freeze_top_row": true, ' ||
             '"bold_headers": true, ' ||
             '"auto_fit_columns": true, ' ||
             '"column_format": {' ||
             '  "SourceRecordID": "number",' ||
             '  "SeqNo": "number",' ||
             '  "StateID": "text",' ||
             '  "ProviderName": "text",' ||
             '  "ProviderID": "text",' ||
             '  "ProviderTIN": "text",' ||
             '  "LOB": "text",' ||
             '  "ProviderAccountID": "text",' ||
             '  "ClaimNumber": "text",' ||
             '  "ClaimPaidDate": "date",' ||
             '  "ClaimPaidAmount": "currency",' || 
             '  "OpenPayableAmount": "currency",' ||
             '  "OpenPayableDate": "date",' ||
             '  "OpenPayableAge": "text",' ||
             '  "ShiftToAccount": "text"' ||
             '}, ' ||
             '"cell_styles": { ' ||
             '  "ClaimPaidAmount": {' ||
             '    "font_color": "FF0000",' ||        -- red
             '    "font_size": 12,' ||
             '    "bg_color": "FFF0F0"' ||
             '  },' ||
             '  "ClaimNumber": {' ||
             '    "font_color": "FFF0F0",' ||        -- red
             '    "font_size": 14,' ||
             '    "bg_color": "FF0000"' ||
             '  }' ||
             '}, ' ||
             '"conditional_formatting": [' ||
             '  {' || 
             '    "column": "OpenPayableAmount",' ||
             '    "type": "greaterThan",' ||
             '    "value": 1000,' ||
             '    "font_color": "FFFFFF",' ||
             '    "bg_color": "FF0000"' ||
             '  }' ||
             '], ' ||
            '"rows": [' ||
'    {% for claim in claims %}' ||
'        {' ||
'            "SourceRecordID": "{{ claim.SourceRecordID }}",' ||
'            "SeqNo": "{{ claim.SeqNo }}",' ||
'            "StateID": "{{ claim.StateID }}",' ||
'            "ProviderName": "{{ claim.ProviderName }}",' ||
'            "ProviderID": "{{ claim.ProviderID }}",' ||
'            "ProviderTIN": "{{ claim.ProviderTIN }}",' ||
'            "LOB": "{{ claim.LOB }}",' ||
'            "ProviderAccountID": "{{ claim.ProviderAccountID }}",' ||
'            "ClaimNumber": "{{ claim.ClaimNumber }}",' ||
'            "ClaimPaidDate": "{{ claim.ClaimPaidDate }}",' ||
'            "ClaimPaidAmount": {{ claim.ClaimPaidAmount }},' ||
'            "OpenPayableAmount": {{ claim.OpenPayableAmount }},' ||
'            "OpenPayableDate": "{{ claim.OpenPayableDate }}",' ||
'            "OpenPayableAge": "{{ claim.OpenPayableAge }}",' ||
'            "ShiftToAccount": "{{ claim.ShiftToAccount }}"' ||
'        }{% if not loop.last %},{% endif %}' ||
'        {% endfor %}' ||
'    ]' ||
'}' 
        INTO l_claims_data 
        FROM dual;      
/*          
{
  "worksheet_name": "Claims",
  "freeze_top_row": true,
  "bold_headers": true,
  "auto_fit_columns": true,
  "column_format": {
    "ClaimPaidAmount": "currency",
    "ClaimPaidDate": "date",
    "OpenPayableAge": "number"
  },
  "rows": [
    {% for c in claims %}
    {
      "ClaimNumber": "{{ c.ClaimNumber }}",
      "ClaimPaidDate": "{{ c.ClaimPaidDate }}",
      "ClaimPaidAmount": {{ c.ClaimPaidAmount }},
      "OpenPayableAge": {{ c.OpenPayableAge }}
    }{% if not loop.last %},{% endif %}
    {% endfor %}
  ]
}
*/
      RETURN l_claims_data;

   END get_open_payables_claims_template; 


   -- This function returns the worksheet (Jinja2) template.
   FUNCTION get_worksheet_template (p_spreadsheet_name IN VARCHAR2, p_worksheet_name IN VARCHAR2)
      RETURN VARCHAR2 IS

      worksheet_template   VARCHAR2(32767);
      l_column_name       VARCHAR2(100);
      l_data_type         VARCHAR2(100);
      l_font              VARCHAR2(100);
      l_font_size         NUMBER;
      l_font_color        VARCHAR2(10);
      l_background_color  VARCHAR2(10);
      l_bold              VARCHAR2(1);
      l_italic            VARCHAR2(1);
      l_condition_type    VARCHAR2(100);
      l_condition_value   VARCHAR2(100);
      l_condition_font_color VARCHAR2(10);
      l_condition_bg_color VARCHAR2(10);
      l_condition_added   VARCHAR2(1) := 'N';
      l_providerid        VARCHAR2(100);
      l_providername      VARCHAR2(100);
      l_lob               VARCHAR2(100);

      CURSOR c_worksheet_columns IS
         SELECT column_name, data_type, font, font_size, font_color, 
                background_color, bold, italic, condition_type, 
                condition_value, condition_font_color, condition_bg_color
           FROM pi_worksheet_column_templates
          WHERE worksheet_id = 
                (SELECT worksheet_id
                  FROM pi_worksheet_templates
                WHERE worksheet_name = p_worksheet_name
                  AND spreadsheet_id = 
                      (SELECT spreadsheet_id
                         FROM pi_spreadsheet_templates
                        WHERE spreadsheet_name = p_spreadsheet_name))
          ORDER BY column_order;


      CURSOR c_worksheet_locked_columns IS
         SELECT column_name
           FROM pi_worksheet_column_templates
          WHERE worksheet_id = 
                (SELECT worksheet_id
                  FROM pi_worksheet_templates
                WHERE worksheet_name = p_worksheet_name
                  AND spreadsheet_id = 
                      (SELECT spreadsheet_id
                         FROM pi_spreadsheet_templates
                        WHERE spreadsheet_name = p_spreadsheet_name))
            AND lock_column = 'Y'
          ORDER BY column_order;


      CURSOR c_worksheet_unlocked_columns IS
         SELECT column_name
           FROM pi_worksheet_column_templates
          WHERE worksheet_id = 
                (SELECT worksheet_id
                  FROM pi_worksheet_templates
                WHERE worksheet_name = p_worksheet_name
                  AND spreadsheet_id = 
                      (SELECT spreadsheet_id
                         FROM pi_spreadsheet_templates
                        WHERE spreadsheet_name = p_spreadsheet_name))
            AND lock_column = 'N'
          ORDER BY column_order;    

      CURSOR c_worksheet_hidden_columns IS
         SELECT column_name
           FROM pi_worksheet_column_templates
          WHERE worksheet_id = 
                (SELECT worksheet_id
                  FROM pi_worksheet_templates
                WHERE worksheet_name = p_worksheet_name
                  AND spreadsheet_id = 
                      (SELECT spreadsheet_id
                         FROM pi_spreadsheet_templates
                        WHERE spreadsheet_name = p_spreadsheet_name))
            AND hide_column = 'Y'
          ORDER BY column_order;    

      CURSOR c_providers IS
         SELECT DISTINCT providerid, providername
           FROM pi_open_payables_claims_staging
          WHERE providerid <> 'Provider ID'
          ORDER BY 1, 2;

      CURSOR c_lobs IS
         SELECT DISTINCT lob
           FROM pi_open_payables_claims_staging
          WHERE providerid <> 'Provider ID'
          ORDER BY 1;    

   BEGIN

      -- Build the Worksheet-level section
      worksheet_template := '{' ||
         '"worksheet_name": "' || p_worksheet_name ||'", ' ||
         '"freeze_top_row": true, ' ||
         '"bold_headers": true, ' ||
         '"auto_fit_columns": true, ';

      -- Build the Column Format section
      worksheet_template := worksheet_template ||
         '"column_format": {';

      OPEN c_worksheet_columns;
      LOOP
         FETCH c_worksheet_columns INTO l_column_name, l_data_type, l_font, 
            l_font_size, l_font_color, l_background_color, l_bold, l_italic, 
            l_condition_type, l_condition_value, l_condition_font_color, 
            l_condition_bg_color;
         EXIT WHEN c_worksheet_columns%NOTFOUND;
         worksheet_template := worksheet_template ||
            '  "' || l_column_name || '": "' || l_data_type || '",';
      END LOOP;
      CLOSE c_worksheet_columns;

      -- Remove the trailing comma
      worksheet_template := RTRIM(worksheet_template, ',');

      -- Close the Column Format section 
      worksheet_template := worksheet_template || '}, ';  


      -- Add the lock sheet section if there are locked columns 
      SELECT worksheet_template || '"lock_sheet": true, '
        INTO worksheet_template
        FROM dual
       WHERE EXISTS 
             (SELECT 1 
                FROM pi_worksheet_column_templates
               WHERE worksheet_id = 
                     (SELECT worksheet_id
                        FROM pi_worksheet_templates
                       WHERE worksheet_name = p_worksheet_name
                 AND spreadsheet_id = 
                     (SELECT spreadsheet_id
                        FROM pi_spreadsheet_templates
                       WHERE spreadsheet_name = p_spreadsheet_name))
                 AND lock_column = 'Y')
      UNION 
      SELECT worksheet_template || '"lock_sheet": false, '
        FROM dual
       WHERE NOT EXISTS 
             (SELECT 1 
                FROM pi_worksheet_column_templates
               WHERE worksheet_id = 
                     (SELECT worksheet_id
                        FROM pi_worksheet_templates
                       WHERE worksheet_name = p_worksheet_name
                 AND spreadsheet_id = 
                     (SELECT spreadsheet_id
                        FROM pi_spreadsheet_templates
                       WHERE spreadsheet_name = p_spreadsheet_name))
                 AND lock_column = 'Y'); 


      -- Build the Locked Columns section
      worksheet_template := worksheet_template ||
         '"locked_columns": [';

      OPEN c_worksheet_locked_columns;
      LOOP
         FETCH c_worksheet_locked_columns INTO l_column_name;
         EXIT WHEN c_worksheet_locked_columns%NOTFOUND;
         worksheet_template := worksheet_template ||
            '  "' || l_column_name || '",';
      END LOOP;
      CLOSE c_worksheet_locked_columns;

      -- Remove the trailing comma
      worksheet_template := RTRIM(worksheet_template, ',');

      -- Close the Locked Columns section 
      worksheet_template := worksheet_template || '], '; 



      -- Build the Unlocked Columns section
      worksheet_template := worksheet_template ||
         '"unlocked_columns": [';

      OPEN c_worksheet_unlocked_columns;
      LOOP
         FETCH c_worksheet_unlocked_columns INTO l_column_name;
         EXIT WHEN c_worksheet_unlocked_columns%NOTFOUND;
         worksheet_template := worksheet_template ||
            '  "' || l_column_name || '",';
      END LOOP;
      CLOSE c_worksheet_unlocked_columns;

      -- Remove the trailing comma
      worksheet_template := RTRIM(worksheet_template, ',');

      -- Close the Unlocked Columns section 
      worksheet_template := worksheet_template || '], '; 



      -- Build the Cell Styles section
      worksheet_template := worksheet_template ||
         '"cell_styles": {';

      OPEN c_worksheet_columns;
      LOOP
         FETCH c_worksheet_columns INTO l_column_name, l_data_type, l_font, 
            l_font_size, l_font_color, l_background_color, l_bold, l_italic, 
            l_condition_type, l_condition_value, l_condition_font_color, 
            l_condition_bg_color;
         EXIT WHEN c_worksheet_columns%NOTFOUND;
         
         worksheet_template := worksheet_template ||
            '  "' || l_column_name || '": {';
            
         IF l_font != 'Default' THEN
            worksheet_template := worksheet_template ||
               '    "font_name": "' || l_font || '",';
         END IF;
         
         IF l_font_size != 11 THEN
            worksheet_template := worksheet_template ||
               '    "font_size": ' || l_font_size || ',';
         END IF;

         IF l_font_color != 'Default' THEN
            worksheet_template := worksheet_template ||
               '    "font_color": "' || l_font_color || '",';
         END IF;

         IF l_background_color != 'Default' THEN
            worksheet_template := worksheet_template ||
               '    "bg_color": "' || l_background_color || '",';
         END IF;

         IF l_bold = 'Y' THEN
            worksheet_template := worksheet_template ||
               '    "bold": true,';
         END IF;

         IF l_italic = 'Y' THEN
            worksheet_template := worksheet_template ||
               '    "italic": true,';
         END IF;

         -- Remove the trailing comma
         worksheet_template := RTRIM(worksheet_template, ',');

         worksheet_template := worksheet_template ||
            '  },';

      END LOOP;
      CLOSE c_worksheet_columns;

      -- Remove the trailing comma
      worksheet_template := RTRIM(worksheet_template, ',');

      -- Close the Cell Styles section 
      worksheet_template := worksheet_template || '}, ';  



      -- Build the conditional formatting section
      worksheet_template := worksheet_template ||
         '"conditional_formatting": [';

      OPEN c_worksheet_columns;
      LOOP
         FETCH c_worksheet_columns INTO l_column_name, l_data_type, l_font, 
            l_font_size, l_font_color, l_background_color, l_bold, l_italic, 
            l_condition_type, l_condition_value, l_condition_font_color, 
            l_condition_bg_color;
         EXIT WHEN c_worksheet_columns%NOTFOUND;
         
         IF l_condition_type != 'None' THEN
            worksheet_template := worksheet_template || '  {';
            worksheet_template := worksheet_template ||
               '    "column": "' || l_column_name || '",';
            worksheet_template := worksheet_template ||
               '    "type": "' || l_condition_type || '",';
            worksheet_template := worksheet_template ||
               '    "value": ' || l_condition_value || ',';
            worksheet_template := worksheet_template ||
               '    "font_color": "' || l_condition_font_color || '",';
            worksheet_template := worksheet_template ||
               '    "bg_color": "' || l_condition_bg_color || '"';
            worksheet_template := worksheet_template ||
               '  },';
            l_condition_added := 'Y';   
         END IF;

         

      END LOOP;
      CLOSE c_worksheet_columns;

      -- Remove the trailing comma
      IF l_condition_added = 'Y' THEN
         worksheet_template := RTRIM(worksheet_template, ',');
      END IF;

      -- Close the conditional formatting section 
      worksheet_template := worksheet_template || '], ';  



      -- Build the Hidden Columns section
      worksheet_template := worksheet_template ||
         '"hidden_columns": [';

      OPEN c_worksheet_hidden_columns;
      LOOP
         FETCH c_worksheet_hidden_columns INTO l_column_name;
         EXIT WHEN c_worksheet_hidden_columns%NOTFOUND;
         worksheet_template := worksheet_template ||
            '  "' || l_column_name || '",';
      END LOOP;
      CLOSE c_worksheet_hidden_columns;

      -- Remove the trailing comma
      worksheet_template := RTRIM(worksheet_template, ',');

      -- Close the Hidden Columns section 
      worksheet_template := worksheet_template || '], '; 



      -- Build the Lookup Sources section
      -- (Only add these examples to the "Claims" worksheet)
      IF p_worksheet_name = 'Claims' THEN

         worksheet_template := worksheet_template ||
            '"lookup_sources": {';

         -- Add the Simple named lists -> dropdowns
         worksheet_template := worksheet_template ||
            '  "States": ' ||
            '    [ "AL", "AK", "AZ", "AR", "CA", "CO", "CT", "DE", "FL", "GA", ' ||
            '"HI", "ID", "IL", "IN", "IA", "KS", "KY", "LA", "ME", "MD", "MA", ' ||
            '"MI", "MN", "MS", "MO", "MT", "NE", "NV", "NH", "NJ", "NM", "NY", ' ||
            '"NC", "ND", "OH", "OK", "OR", "PA", "RI", "SC", "SD", "TN", "TX", ' ||
            '"UT", "VT", "VA", "WA", "WV", "WI", "WY" ],';


         -- Add the Providers Dynamic lookup sources
         worksheet_template := worksheet_template ||
            '"Providers": {' ||
            '  "headers": [ "ProviderID", "ProviderName" ],' ||
            '  "rows": [';
         
         OPEN c_providers;
         LOOP
            FETCH c_providers INTO l_providerid, l_providername;
            EXIT WHEN c_providers%NOTFOUND;
            worksheet_template := worksheet_template ||
                '      {"ProviderID": "' || l_providerid || '", "ProviderName": "' || l_providername || '" },';
         END LOOP;
         CLOSE c_providers;

         -- Remove the trailing comma
         worksheet_template := RTRIM(worksheet_template, ',');

         -- Close the Providers dynamic lookup section 
         worksheet_template := worksheet_template || ']}, '; 


         -- Add the LOB Dynamic lookup sources
         worksheet_template := worksheet_template ||
            '"LOB": [';
         
         OPEN c_lobs;
         LOOP
            FETCH c_lobs INTO l_lob;
            EXIT WHEN c_lobs%NOTFOUND;
            worksheet_template := worksheet_template ||
                '  "' || l_lob || '",';
         END LOOP;
         CLOSE c_lobs;

         -- Remove the trailing comma
         worksheet_template := RTRIM(worksheet_template, ',');

         -- Close the LOB dynamic lookup section 
         worksheet_template := worksheet_template || '] '; 

         -- Close the lookup section 
         worksheet_template := worksheet_template || '}, '; 

      END IF;


      /* Build the Data Validations section
      "data_validations": [
        { "column": "StateID", "source": "States", "allow_blank": false },
        // Or use a direct range reference if you prefer: "=LOOKUPS!$A$2:$A$100"
        { "column": "LOB", "source": "=LOOKUPS!$C$2:$C$50", "allow_blank": true }
      ],
      */
      worksheet_template := worksheet_template ||
         '"data_validations": [';

       worksheet_template := worksheet_template ||
         '  { "column": "StateID", "source": "States", "allow_blank": false }, '; 
       worksheet_template := worksheet_template ||
         '  { "column": "LOB", "source": "LOB", "allow_blank": false }, '; 
      worksheet_template := worksheet_template ||
         '  { "column": "ShiftToAccount", "source": "LOB", "allow_blank": false } ';    

      -- Close the Hidden Columns section 
      worksheet_template := worksheet_template || '], '; 

      /* Build the XLookups section
      "xlookups": [
        {
          "result_column": "ProviderName",   // column to fill with result
          "key_column": "ProviderID",        // column in your data with the key
          "source": "Providers",             // lookup source name (from lookup_sources)
          "if_not_found": ""                 // optional
      }
      ],
      */
      worksheet_template := worksheet_template ||
         '"xlookups": [';

       worksheet_template := worksheet_template ||
         '  { "result_column": "ProviderName", "key_column": "ProviderID", "source": "Providers", "if_not_found": "" } '; 

      -- Close the XLookups section 
      worksheet_template := worksheet_template || '], '; 


      

      -- Build the Rows section
      worksheet_template := worksheet_template || '"rows": [';
      worksheet_template := worksheet_template || '  {% for record in records %}';
      worksheet_template := worksheet_template || '    {';


      OPEN c_worksheet_columns;
      LOOP
         FETCH c_worksheet_columns INTO l_column_name, l_data_type, l_font, 
            l_font_size, l_font_color, l_background_color, l_bold, l_italic, 
            l_condition_type, l_condition_value, l_condition_font_color, 
            l_condition_bg_color;
         EXIT WHEN c_worksheet_columns%NOTFOUND;

         CASE WHEN l_data_type = 'number' OR l_data_type = 'currency' THEN
            worksheet_template := worksheet_template ||
               '      "' || l_column_name || '": {{ record.' || l_column_name || ' }},';
         ELSE
            worksheet_template := worksheet_template ||
               '      "' || l_column_name || '": "{{ record.' || l_column_name || ' }}",';
         END CASE;

      END LOOP;
      CLOSE c_worksheet_columns;

      -- Remove the trailing comma
      worksheet_template := RTRIM(worksheet_template, ',');

      -- Close the Row section
      worksheet_template := worksheet_template || 
      '    }{% if not loop.last %},{% endif %}' ||
      '    {% endfor %}' ||
      ']';




      -- Close the Worksheet section 
      worksheet_template := worksheet_template || '}';     
      /*
      SELECT '{' ||
             '"worksheet_name": "Claims", ' ||
             '"freeze_top_row": true, ' ||
             '"bold_headers": true, ' ||
             '"auto_fit_columns": true, ' ||
             '"column_format": {' ||
             '  "SourceRecordID": "number",' ||
             '  "SeqNo": "number",' ||
             '  "StateID": "text",' ||
             '  "ProviderName": "text",' ||
             '  "ProviderID": "text",' ||
             '  "ProviderTIN": "text",' ||
             '  "LOB": "text",' ||
             '  "ProviderAccountID": "text",' ||
             '  "ClaimNumber": "text",' ||
             '  "ClaimPaidDate": "date",' ||
             '  "ClaimPaidAmount": "currency",' || 
             '  "OpenPayableAmount": "currency",' ||
             '  "OpenPayableDate": "date",' ||
             '  "OpenPayableAge": "text",' ||
             '  "ShiftToAccount": "text"' ||
             '}, ' ||
             '"cell_styles": { ' ||
             '  "ClaimPaidAmount": {' ||
             '    "font_color": "FF0000",' ||        -- red
             '    "font_size": 12,' ||
             '    "bg_color": "FFF0F0"' ||
             '  }' ||
             '}, ' ||
             '"conditional_formatting": [' ||
             '  {' || 
             '    "column": "OpenPayableAmount",' ||
             '    "type": "greaterThan",' ||
             '    "value": 1000,' ||
             '    "font_color": "FFFFFF",' ||
             '    "bg_color": "FF0000"' ||
             '  }' ||
             '], ' ||
            '"rows": [' ||
'    {% for claim in claims %}' ||
'        {' ||
'            "SourceRecordID": "{{ claim.SourceRecordID }}",' ||
'            "SeqNo": "{{ claim.SeqNo }}",' ||
'            "StateID": "{{ claim.StateID }}",' ||
'            "ProviderName": "{{ claim.ProviderName }}",' ||
'            "ProviderID": "{{ claim.ProviderID }}",' ||
'            "ProviderTIN": "{{ claim.ProviderTIN }}",' ||
'            "LOB": "{{ claim.LOB }}",' ||
'            "ProviderAccountID": "{{ claim.ProviderAccountID }}",' ||
'            "ClaimNumber": "{{ claim.ClaimNumber }}",' ||
'            "ClaimPaidDate": "{{ claim.ClaimPaidDate }}",' ||
'            "ClaimPaidAmount": {{ claim.ClaimPaidAmount }},' ||
'            "OpenPayableAmount": {{ claim.OpenPayableAmount }},' ||
'            "OpenPayableDate": "{{ claim.OpenPayableDate }}",' ||
'            "OpenPayableAge": "{{ claim.OpenPayableAge }}",' ||
'            "ShiftToAccount": "{{ claim.ShiftToAccount }}"' ||
'        }{% if not loop.last %},{% endif %}' ||
'        {% endfor %}' ||
'    ]' ||
'}' 
        INTO l_claims_data 
        FROM dual;      
*/
      RETURN worksheet_template;

   END get_worksheet_template; 


   -- This function gets the file name for the given spreadsheet.
   FUNCTION get_spreadsheet_filename(p_spreadsheet_name IN VARCHAR2)
      RETURN VARCHAR2 IS

      l_file_name   VARCHAR2(100);

   BEGIN

      SELECT file_name
        INTO l_file_name
        FROM pi_spreadsheet_templates
       WHERE spreadsheet_name = p_spreadsheet_name;

      RETURN l_file_name;

   END get_spreadsheet_filename; 



   -- This procedure reloads the base spreadsheet template
   PROCEDURE reload_spreadsheet_template IS

   BEGIN

      -- Clear the previous data
      DELETE FROM pi_worksheet_column_templates;
      DELETE FROM pi_worksheet_templates;
      DELETE FROM pi_spreadsheet_templates;

      -- Main (open payables) spreadsheet
      INSERT INTO pi_spreadsheet_templates 
      (spreadsheet_name, file_name)
      VALUES ('Open Payables', 'Open_Payables.xlsx');


      -- Instructions worksheet
      INSERT INTO pi_worksheet_templates
      (spreadsheet_id, worksheet_order, worksheet_name)
      SELECT spreadsheet_id, 1, 'Instructions'
        FROM pi_spreadsheet_templates
       WHERE file_name = 'Open_Payables.xlsx';

      INSERT INTO pi_worksheet_column_templates
      (worksheet_id, column_order, column_name, data_type, lock_column, hide_column)
      SELECT worksheet_id, 1, 'record_no', 'number', 'Y', 'Y'
        FROM pi_worksheet_templates
       WHERE worksheet_name = 'Instructions';

      INSERT INTO pi_worksheet_column_templates
      (worksheet_id, column_order, column_name, data_type, lock_column)
      SELECT worksheet_id, 2, 'Instruction', 'text', 'Y'
        FROM pi_worksheet_templates
       WHERE worksheet_name = 'Instructions';




      -- Claims worksheet
      INSERT INTO pi_worksheet_templates
      (spreadsheet_id, worksheet_order, worksheet_name)
      SELECT spreadsheet_id, 3, 'Claims'
        FROM pi_spreadsheet_templates
       WHERE file_name = 'Open_Payables.xlsx';

      INSERT INTO pi_worksheet_column_templates
      (worksheet_id, column_order, column_name, data_type, font, font_size, font_color, bold, italic, lock_column)
      SELECT worksheet_id, 1, 'SourceRecordID', 'text', 'Times New Roman', 12, '336600', 'Y', 'Y', 'Y'
        FROM pi_worksheet_templates
       WHERE worksheet_name = 'Claims';

      INSERT INTO pi_worksheet_column_templates
      (worksheet_id, column_order, column_name, data_type, font, font_size, font_color, bold, italic, lock_column)
      SELECT worksheet_id, 2, 'SeqNo', 'text', 'Times New Roman', 12, 'CCCCFF', 'N', 'N', 'Y'
        FROM pi_worksheet_templates
       WHERE worksheet_name = 'Claims';

      INSERT INTO pi_worksheet_column_templates
      (worksheet_id, column_order, column_name, data_type, font, font_size, font_color, bold, italic, lock_column)
      SELECT worksheet_id, 3, 'StateID', 'text', 'Default', 11, 'Default', 'N', 'N', 'Y'
        FROM pi_worksheet_templates
       WHERE worksheet_name = 'Claims';

      INSERT INTO pi_worksheet_column_templates
      (worksheet_id, column_order, column_name, data_type, font, font_size, font_color, bold, italic, lock_column)
      SELECT worksheet_id, 4, 'ProviderName', 'text', 'Courier New', 14, '0000FF', 'N', 'N', 'Y'
        FROM pi_worksheet_templates
       WHERE worksheet_name = 'Claims';

      INSERT INTO pi_worksheet_column_templates
      (worksheet_id, column_order, column_name, data_type, font, font_size, font_color, bold, italic, lock_column)
      SELECT worksheet_id, 5, 'ProviderID', 'text', 'Default', 11, 'Default', 'Y', 'Y', 'Y'
        FROM pi_worksheet_templates
       WHERE worksheet_name = 'Claims';

      INSERT INTO pi_worksheet_column_templates
      (worksheet_id, column_order, column_name, data_type, lock_column)
      SELECT worksheet_id, 6, 'ProviderTIN', 'text', 'Y'
        FROM pi_worksheet_templates
       WHERE worksheet_name = 'Claims';

      INSERT INTO pi_worksheet_column_templates
      (worksheet_id, column_order, column_name, data_type, lock_column)
      SELECT worksheet_id, 7, 'LOB', 'text', 'Y'
        FROM pi_worksheet_templates
       WHERE worksheet_name = 'Claims';

      INSERT INTO pi_worksheet_column_templates
      (worksheet_id, column_order, column_name, data_type, lock_column)
      SELECT worksheet_id, 8, 'ProviderAccountID', 'text', 'Y'
        FROM pi_worksheet_templates
       WHERE worksheet_name = 'Claims';

      INSERT INTO pi_worksheet_column_templates
      (worksheet_id, column_order, column_name, data_type, lock_column)
      SELECT worksheet_id, 9, 'ClaimNumber', 'text', 'Y'
        FROM pi_worksheet_templates
       WHERE worksheet_name = 'Claims';

      INSERT INTO pi_worksheet_column_templates
      (worksheet_id, column_order, column_name, data_type, lock_column)
      SELECT worksheet_id, 10, 'ClaimPaidDate', 'date', 'Y'
        FROM pi_worksheet_templates
       WHERE worksheet_name = 'Claims';

      INSERT INTO pi_worksheet_column_templates
      (worksheet_id, column_order, column_name, data_type, font, font_size, 
       font_color, bold, italic, background_color, condition_type, 
       condition_value, condition_font_color, condition_bg_color, lock_column)
      SELECT worksheet_id, 11, 'ClaimPaidAmount', 'currency', 'Default', 12, 
             'FF0000', 'N', 'N', 'FFF0F0', 'lessThanOrEqual', 100, '0000FF', '808080', 'Y'
        FROM pi_worksheet_templates
       WHERE worksheet_name = 'Claims';

      INSERT INTO pi_worksheet_column_templates
      (worksheet_id, column_order, column_name, data_type, condition_type, 
       condition_value, condition_font_color, condition_bg_color, lock_column)
      SELECT worksheet_id, 12, 'OpenPayableAmount', 'currency', 'greaterThan', 
             1000, 'FFFFFF', 'FF0000', 'Y'
        FROM pi_worksheet_templates
       WHERE worksheet_name = 'Claims';

      INSERT INTO pi_worksheet_column_templates
      (worksheet_id, column_order, column_name, data_type, lock_column)
      SELECT worksheet_id, 13, 'OpenPayableDate', 'date', 'Y'
        FROM pi_worksheet_templates
       WHERE worksheet_name = 'Claims';

      INSERT INTO pi_worksheet_column_templates
      (worksheet_id, column_order, column_name, data_type, lock_column)
      SELECT worksheet_id, 14, 'OpenPayableAge', 'text', 'Y'
        FROM pi_worksheet_templates
       WHERE worksheet_name = 'Claims';

      INSERT INTO pi_worksheet_column_templates
      (worksheet_id, column_order, column_name, data_type)
      SELECT worksheet_id, 15, 'ShiftToAccount', 'text'
        FROM pi_worksheet_templates
       WHERE worksheet_name = 'Claims';  



   END reload_spreadsheet_template;

END spreadsheet;
/   
SHOW ERRORS

SPOOL OFF

-- Complete
