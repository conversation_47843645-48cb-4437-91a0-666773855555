------------------------------------------------------------------------------
--
--   PROCEDURE:      pi_create_host_request_log_stg_table.sql
--
--   PURPOSE:        This procedure is used to create the PI_HOST_REQUEST_LOG_STG 
--                   table. This table is used to process log files generated by
--                   the Host Requests.
-- 
--   PARAMETERS:     None. 
--
--   INSTRUCTIONS:   From within SQL*Plus (connected as APPS), type:
--                      START pi_create_host_request_log_stg_table.sql
--
--   AUTHOR:         <PERSON>.
--
--   INSTALL SCHEMA: APPS
--
--
--   Copyright (c) 2025 Kevin <PERSON>uire
--   All rights reserved.
--
--   This software is proprietary and confidential. Unauthorized copying of this
--   file, via any medium, is strictly prohibited without the express permission
--   of the author or owning organization.
--
------------------------------------------------------------------------------

WHENEVER OSERROR EXIT

SET ECHO ON


-- Open the log file
SPOOL pi_create_host_request_log_stg_table


-- Drop the current table 
DROP TABLE pi_host_request_log_stg;

-- Create the table
CREATE TABLE pi_host_request_log_stg
(
   request_id     NUMBER  NOT NULL ENABLE PRIMARY KEY,
   log_file       VARCHAR2(100)  NOT NULL,
   log_contents   CLOB
);


-- Stop logging
SPOOL OFF 


-- Complete
