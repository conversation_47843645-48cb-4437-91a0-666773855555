------------------------------------------------------------------------------
--
--   PROCEDURE:      pi_create_fnd_keys_table.sql
--
--   PURPOSE:        This procedure is used to create the FND_KEYS table. This
--                   table is used to define the types of keys used in the 
--                   application. 
-- 
--   PARAMETERS:     None. 
--
--   INSTRUCTIONS:   From within SQL*Plus (connected as APPS), type:
--                      START pi_create_fnd_keys_table.sql
--
--   AUTHOR:         <PERSON>uire.
--
--   INSTALL SCHEMA: APPS
--
--
--   Copyright (c) 2025 Kevin Le<PERSON>uire
--   All rights reserved.
--
--   This software is proprietary and confidential. Unauthorized copying of this
--   file, via any medium, is strictly prohibited without the express permission
--   of the author or owning organization.
--
------------------------------------------------------------------------------

WHENEVER OSERROR EXIT

SET ECHO ON



-- Open the log file
SPOOL pi_create_fnd_keys_table



-- Drop the current table 
-- DROP TABLE fnd_keys;


-- Create the table
CREATE TABLE fnd_keys 
(
   keyid         NUMBER GENERATED ALWAYS AS IDENTITY 
                 MINVALUE 1 MAXVALUE 9999999999999999999999999999 
                 INCREMENT BY 1 START WITH 1 
                 CACHE 20 NOORDER NOCYCLE NOKEEP NOSCALE NOT NULL ENABLE, 
   keyname      VARCHAR2(200) NOT NULL, 
   keycode      VARCHAR2(50)  NOT NULL, 
   description  VARCHAR2(200), 
   createdby    VARCHAR2(200) DEFAULT 'SYSTEM', 
   createddate  DATE          DEFAULT SYSDATE, 
   modifiedby   VARCHAR2(200) DEFAULT 'SYSTEM', 
   modifieddate DATE          DEFAULT SYSDATE,
   --
   CONSTRAINT fnd_keys_pk
     PRIMARY KEY (keyid)
     USING INDEX
);

DESCRIBE fnd_keys


-- Stop logging
SPOOL OFF 


-- Complete


