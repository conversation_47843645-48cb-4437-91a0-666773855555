------------------------------------------------------------------------------
--
--   PROCEDURE:      pi_create_fnd_key_values_table.sql
--
--   PURPOSE:        This procedure is used to create the FND_KEY_VALUES table. 
--                   This table is used to define the key values used in the 
--                   application. 
-- 
--   PARAMETERS:     None. 
--
--   INSTRUCTIONS:   From within SQL*Plus (connected as APPS), type:
--                      START pi_create_fnd_key_values_table.sql
--
--   AUTHOR:         <PERSON>.
--
--   INSTALL SCHEMA: APPS
--
--
--   Copyright (c) 2025 Kevin <PERSON>
--   All rights reserved.
--
--   This software is proprietary and confidential. Unauthorized copying of this
--   file, via any medium, is strictly prohibited without the express permission
--   of the author or owning organization.
--
------------------------------------------------------------------------------

WHENEVER OSERROR EXIT

SET ECHO ON



-- Open the log file
SPOOL pi_create_fnd_key_values_table



-- Drop the current table 
-- DROP TABLE fnd_key_values;


-- Create the table
CREATE TABLE fnd_key_values 
(
   keyvalueid    NUMBER GENERATED ALWAYS AS IDENTITY 
                 MINVALUE 1 MAXVALUE 9999999999999999999999999999 
                 INCREMENT BY 1 START WITH 1 
                 CACHE 20 NOORDER NOCYCLE NOKEEP NOSCALE NOT NULL ENABLE, 
   keyid        NUMBER        NOT NULL,
   keyvaluecode VARCHAR2(50)  NOT NULL, 
   keyvalue     VARCHAR2(200) NOT NULL, 
   description  VARCHAR2(200), 
   createdby    VARCHAR2(200) DEFAULT 'SYSTEM', 
   createddate  DATE          DEFAULT SYSDATE, 
   modifiedby   VARCHAR2(200) DEFAULT 'SYSTEM', 
   modifieddate DATE          DEFAULT SYSDATE,
   --
   CONSTRAINT fnd_key_values_pk
     PRIMARY KEY (keyvalueid)
     USING INDEX,
   CONSTRAINT  fnd_key_values_fk
      FOREIGN KEY (keyid)
      REFERENCES fnd_keys (keyid)
);


DESCRIBE fnd_key_values


-- Stop logging
SPOOL OFF 


-- Complete


