------------------------------------------------------------------------------
--
--   PROCEDURE:      pi_create_public_keys_table.sql
--
--   PURPOSE:        This procedure is used to create the PI_PUBLIC_KEYS table.
--                   This table is used to create and track application PGP 
--                   public keys.
-- 
--   PARAMETERS:     None. 
--
--   INSTRUCTIONS:   From within SQL*Plus (connected as APPS), type:
--                      START pi_create_public_keys_table.sql
--
--   AUTHOR:         <PERSON>.
--
--   INSTALL SCHEMA: APPS
--
--
--   Copyright (c) 2025 Kevin <PERSON>uire
--   All rights reserved.
--
--   This software is proprietary and confidential. Unauthorized copying of this
--   file, via any medium, is strictly prohibited without the express permission
--   of the author or owning organization.
--
------------------------------------------------------------------------------

WHENEVER OSERROR EXIT

SET ECHO ON



-- Open the log file
SPOOL pi_create_public_keys_table



-- Drop the current table 
DROP TABLE pi_public_keys;



-- Create the table
CREATE TABLE pi_public_keys
(
   key_id         NUMBER GENERATED ALWAYS AS IDENTITY
                  MINVALUE 1 MAXVALUE 9999999999999999999999999999
                  INCREMENT BY 1 START WITH 1 NOT NULL ENABLE,
   key_name       VARCHAR2(100)   NOT NULL,
   key_email      VARCHAR2(100)   DEFAULT '<EMAIL>',
   key_comment    VARCHAR2(100)   NOT NULL,
   expiration     VARCHAR2(12)    DEFAULT '1Y',
   status         VARCHAR2(12)    DEFAULT 'NEW',  
   start_date     DATE            DEFAULT SYSDATE,
   end_date       DATE,
   --
   fingerprint    VARCHAR2(2048),
   expires        DATE,
   key_file       VARCHAR2(100),
   -- 
   CONSTRAINT pi_public_keys_pk 
     PRIMARY KEY (key_id)
     USING INDEX 
);


-- Stop logging
SPOOL OFF 


-- Complete


