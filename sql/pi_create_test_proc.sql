------------------------------------------------------------------------------
--
--   PROCEDURE:      pi_create_test_proc.sql
--
--   PURPOSE:        This script is used to create the RP_TEST_PROC procedure. 
--                   It is used for testing the run_sql_proc shell function.
--
--   PARAMETERS:     None. 
--
--   INSTRUCTIONS:   From within SQL*Plus, type:
--                      START pi_create_test_proc.sql
--
--   AUTHOR:         <PERSON>.
--
--   INSTALL SCHEMA: APPS
--
--
--   Copyright (c) 2025 <PERSON>
--   All rights reserved.
--
--   This software is proprietary and confidential. Unauthorized copying of this
--   file, via any medium, is strictly prohibited without the express permission
--   of the author or owning organization.
--
------------------------------------------------------------------------------

SET ECHO ON


SPOOL pi_create_test_proc

CREATE OR REPLACE PROCEDURE pi_test_proc (p_parameter IN VARCHAR2) IS
BEGIN

   dbms_output.put_line('in_parameter: ' || p_parameter);
   
END pi_test_proc;
/
 
SHOW ERRORS

SPOOL OFF

-- Complete
