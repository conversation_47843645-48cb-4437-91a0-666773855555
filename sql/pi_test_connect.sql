------------------------------------------------------------------------------
--
--   PROCEDURE:      pi_test_connect.sql
--
--   PURPOSE:        This script is used to test a database connection.
--                   It is used for testing the get_db_con shell function.
--
--   PARAMETERS:     None. 
--
--   INSTRUCTIONS:   From within SQL*Plus, type:
--                      START pi_test_connect
--
--   AUTHOR:         <PERSON> LeQuire.
--
--
--   Copyright (c) 2025 Kevin <PERSON>re
--   All rights reserved.
--
--   This software is proprietary and confidential. Unauthorized copying of this
--   file, via any medium, is strictly prohibited without the express permission
--   of the author or owning organization.
--
------------------------------------------------------------------------------
WHENEVER OSERROR EXIT
WHENEVER SQLERROR EXIT

SET ECHO OFF

-- Start the log file
-- SPOOL pi_test_connect

COLUMN global_name    FORMAT A25
COLUMN open_mode      FORMAT A12
COLUMN database_role  FORMAT A13
COLUMN db_unique_name FORMAT A14
COLUMN sort_order     NOPRINT

SET HEADING OFF

-- List the database information
SELECT 'USER:           ' || USER, 1 sort_order 
  FROM dual
UNION
SELECT 'GLOBAL_DB_NAME: ' || property_value, 2 sort_order
  FROM database_properties
 WHERE property_name = 'GLOBAL_DB_NAME'  
 ORDER BY 2; 


-- Stop spooling
-- SPOOL OFF

-- Complete
