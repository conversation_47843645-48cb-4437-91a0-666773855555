------------------------------------------------------------------------------
--
--   PROCEDURE:      pi_create_interfaces_table.sql
--
--   PURPOSE:        This procedure is used to create the PI_INTERFACES table. 
-- 
--   PARAMETERS:     None. 
--
--   INSTRUCTIONS:   From within SQL*Plus (connected as APPS), type:
--                      START pi_create_interfaces_table.sql
--
--   AUTHOR:         <PERSON>uire.
--
--   INSTALL SCHEMA: APPS
--
--
--   Copyright (c) 2025 Kevin <PERSON>
--   All rights reserved.
--
--   This software is proprietary and confidential. Unauthorized copying of this
--   file, via any medium, is strictly prohibited without the express permission
--   of the author or owning organization.
--
------------------------------------------------------------------------------

WHENEVER OSERROR EXIT

SET ECHO ON



-- Open the log file
SPOOL pi_create_interfaces_table



-- Drop the current table 
DROP TABLE pi_create_interfaces_table;



-- Create the table
CREATE TABLE pi_interfaces
(
   interface      VARCHAR2(100)   NOT NULL,
   enabled        VARCHAR2(1)     NOT NULL,
   start_date     DATE            DEFAULT SYSDATE,
   end_date       DATE,
   -- 
   CONSTRAINT pi_interfaces_pk 
     PRIMARY KEY (interface)
     USING INDEX 
);


-- Stop logging
SPOOL OFF 


-- Complete


