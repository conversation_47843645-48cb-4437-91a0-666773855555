------------------------------------------------------------------------------
--
--   PROCEDURE:      pi_create_data_files_table.sql
--
--   PURPOSE:        This procedure is used to create the pi_data_files table. 
--                   This table is used to Excel data files.
-- 
--   PARAMETERS:     None. 
--
--   INSTRUCTIONS:   From within SQL*Plus (connected as APPS), type:
--                      START pi_create_data_files_table.sql
--
--   AUTHOR:         <PERSON>.
--
--   INSTALL SCHEMA: APPS
--
--
--   Copyright (c) 2025 <PERSON>
--   All rights reserved.
--
--   This software is proprietary and confidential. Unauthorized copying of this
--   file, via any medium, is strictly prohibited without the express permission
--   of the author or owning organization.
--
------------------------------------------------------------------------------

WHENEVER OSERROR EXIT

SET ECHO ON


-- Open the log file
SPOOL pi_create_data_files_table


-- Drop the current table 
DROP TABLE pi_data_files;

-- Create the table
CREATE TABLE pi_data_files
(
   id          NUMBER GENERATED ALWAYS AS IDENTITY 
                  MINVALUE 1 MAXVALUE 9999999999999999999999999999 
                  INCREMENT BY 1 START WITH 1 NOT NULL ENABLE PRIMARY KEY,
   file_name   VARCHAR2(100) NOT NULL,
   file_type   VARCHAR2(4), 
   mime_type   VARCHAR2(255),
   data_file   BLOB,
   upload_date DATE DEFAULT SYSDATE
);


-- Stop logging
SPOOL OFF 


-- Complete
