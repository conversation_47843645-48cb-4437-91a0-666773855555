------------------------------------------------------------------------------
--
--   PROCEDURE:      pi_create_interface_email_addresses_table.sql
--
--   PURPOSE:        This procedure is used to create the 
--                   PI_INTERFACE_EMAIL_ADDRESSES table. 
-- 
--   PARAMETERS:     None. 
--
--   INSTRUCTIONS:   From within SQL*Plus (connected as APPS), type:
--                      START pi_create_interface_email_addresses_table.sql
--
--   AUTHOR:         <PERSON>re.
--
--   INSTALL SCHEMA: APPS
--
--
--   Copyright (c) 2025 Kevin <PERSON>uire
--   All rights reserved.
--
--   This software is proprietary and confidential. Unauthorized copying of this
--   file, via any medium, is strictly prohibited without the express permission
--   of the author or owning organization.
--
------------------------------------------------------------------------------

WHENEVER OSERROR EXIT

SET ECHO ON



-- Open the log file
SPOOL pi_create_interface_email_addresses_table



-- Drop the current table 
-- DROP TABLE pi_interface_email_addresses;



-- Create the table
CREATE TABLE pi_interface_email_addresses
(
   interface      VARCHAR2(100)   NOT NULL,
   email_address  VARCHAR2(1000)  NOT NULL,
   address_type   VARCHAR2(3)     DEFAULT 'TO' NOT NULL , -- TO, CC, or BCC
   enabled        VARCHAR2(1)     NOT NULL,
   start_date     DATE            DEFAULT SYSDATE,
   end_date       DATE,
   -- 
   CONSTRAINT pi_interface_email_addresses_pk 
     PRIMARY KEY (interface, email_address)
     USING INDEX,
   CONSTRAINT  pi_interface_email_addresses_fk
      FOREIGN KEY (interface)
      REFERENCES pi_interfaces (interface)
);


-- Stop logging
SPOOL OFF 


-- Complete


