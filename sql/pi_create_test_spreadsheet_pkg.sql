------------------------------------------------------------------------------
--
--   PROCEDURE:      pi_create_test_spreadsheet_pkg.sql
--
--   PURPOSE:        This script is used to create the TEST_SPREADSHEET
--                   package. 
--
--   PARAMETERS:     None. 
--
--   INSTRUCTIONS:   From within SQL*Plus, type:
--                      START pi_create_test_spreadsheet_pkg.sql
--
--   AUTHOR:         <PERSON>.
--
--   INSTALL SCHEMA: APPS
--
--
--   Copyright (c) 2025 <PERSON>
--   All rights reserved.
--
--   This software is proprietary and confidential. Unauthorized copying of this
--   file, via any medium, is strictly prohibited without the express permission
--   of the author or owning organization.
--
------------------------------------------------------------------------------

SET ECHO ON


SPOOL pi_create_test_spreadsheet_pkg

CREATE OR REPLACE PACKAGE test_spreadsheet AUTHID CURRENT_USER AS

   --%suite(test_spreadsheet Unit Test Package (pi_create_test_spreadsheet_pkg.sql))
   
   /* Many of these tests rely on the "test_file.xlsx" in the PI_DATA_FILES 
      table.
   
      If the record is missing, it can be recreating by running the following 
      on the server (from the "simplipied" directory):
         cp test/test_file.xlsx /var/tmp/
         bin/pi_upload_data_file.sh /var/tmp/test_file.xlsx

      Alternatively, copy the test_file.xlsx file to the /opt/inbound 
      directory and run the following PL/SQL procedure:

         EXECUTE spreadsheet.load_data_file('test_file.xlsx')
   */

   -- %beforeall
   PROCEDURE load_test_spreadsheet;


   
   --%context(-- file_loaded Unit Tests)

   -- %test(test_file_loaded_detects_loaded_file)
   PROCEDURE test_file_loaded_detects_loaded_file;

   -- %test(test_file_loaded_detects_missing_file)
   PROCEDURE test_file_loaded_detects_missing_file;

   --%endcontext



   --%context(-- load_data_file Unit Tests)

   -- %test(test_load_data_file_loads_file_name)
   PROCEDURE test_load_data_file_loads_file_name;

   -- %test(test_load_data_file_loads_file_type)
   PROCEDURE test_load_data_file_loads_file_type;

   -- %test(test_load_data_file_loads_mime_type)
   PROCEDURE test_load_data_file_loads_mime_type;
   --application/vnd.openxmlformats-officedocument.spreadsheetml.sheet


   --%endcontext




   --%context(-- get_column_alias Unit Tests)
   
   -- %test(test_get_column_alias_gets_first_column)
   PROCEDURE test_get_column_alias_gets_first_column;

   -- %test(test_get_column_alias_gets_generic_name_if_column_not_loaded)
   PROCEDURE test_get_column_alias_gets_generic_name_if_column_not_loaded;

   -- %test(test_get_column_alias_gets_generic_name_if_file_not_loaded)
   PROCEDURE test_get_column_alias_gets_generic_name_if_file_not_loaded;

   --%endcontext



   --%context(-- build_spreadsheet_sql Unit Tests)
   
   -- %test(test_build_spreadsheet_sql_returns_select_statement)
   PROCEDURE test_build_spreadsheet_sql_returns_select_statement;

   -- %test(test_build_spreadsheet_sql_returns_empty_table_query_if_no_file)
   PROCEDURE test_build_spreadsheet_sql_returns_empty_table_query_if_no_file;

  --%endcontext



   --%context(-- get_spreadsheet_cursor Unit Tests)
   
   -- %test(test_get_spreadsheet_cursor_creates_cursor)
   PROCEDURE test_get_spreadsheet_cursor_creates_cursor;

   -- %test(test_get_spreadsheet_cursor_has_rows)
   PROCEDURE test_get_spreadsheet_cursor_has_rows;

   -- %test(test_get_spreadsheet_cursor_is_empty_if_no_file)
   PROCEDURE test_get_spreadsheet_cursor_is_empty_if_no_file;

  --%endcontext



   --%context(-- stage_data Unit Tests)
   
   -- %test(test_stage_data_loads_staging_table)
   PROCEDURE test_stage_data_loads_staging_table;

   -- %test(test_stage_data_clears_previous_spreadsheet)
   PROCEDURE test_stage_data_clears_previous_spreadsheet;

   --%endcontext



   --%context(-- get_open_payables_claims_cursor Unit Tests)
   
   -- %test(test_get_open_payables_claims_cursor_creates_cursor)
   PROCEDURE test_get_open_payables_claims_cursor_creates_cursor;

   --%endcontext



   --%context(-- get_open_payables_claims_template Unit Tests)

   -- %test(test_get_open_payables_claims_template_returns_template)
   PROCEDURE test_get_open_payables_claims_template_returns_template;

   -- %test(test_get_open_payables_claims_template_includes_column_format)
   PROCEDURE test_get_open_payables_claims_template_includes_column_format;
   
   -- %test(test_get_open_payables_claims_template_includes_cell_styles)
   PROCEDURE test_get_open_payables_claims_template_includes_cell_styles;

   -- %test(test_get_open_payables_claims_template_includes_conditional_formatting)
   PROCEDURE test_get_open_payables_claims_template_includes_conditional_formatting;

    --%endcontext



   --%context(-- get_worksheet_template Unit Tests)

   -- %test(test_get_worksheet_template_returns_column_format_section)
   PROCEDURE test_get_worksheet_template_returns_column_format_section;
   
   -- %test(test_get_worksheet_template_returns_rows_section)
   PROCEDURE test_get_worksheet_template_returns_rows_section;

   -- %test(test_get_worksheet_template_returns_cell_styles_section)
   PROCEDURE test_get_worksheet_template_returns_cell_styles_section;

   -- %test(test_get_worksheet_template_returns_conditional_formatting_section)
   PROCEDURE test_get_worksheet_template_returns_conditional_formatting_section;

   -- %test(test_get_worksheet_template_returns_locked_columns_section)
   PROCEDURE test_get_worksheet_template_returns_locked_columns_section;

   -- %test(test_get_worksheet_template_returns_unlocked_columns_section)
   PROCEDURE test_get_worksheet_template_returns_unlocked_columns_section;
   
   -- %test(test_get_worksheet_template_locks_sheet_if_locked_columns_present)
   PROCEDURE test_get_worksheet_template_locks_sheet_if_locked_columns_present;

   -- %test(test_get_worksheet_template_does_not_lock_sheet_if_no_locked_columns_present)
   PROCEDURE test_get_worksheet_template_does_not_lock_sheet_if_no_locked_columns_present;

   -- %test(test_get_worksheet_template_returns_hidden_columns_section)
   PROCEDURE test_get_worksheet_template_returns_hidden_columns_section;

   -- %test(test_get_worksheet_template_returns_lookup_section)
   PROCEDURE test_get_worksheet_template_returns_lookup_section;

   -- %test(test_get_worksheet_template_returns_dynamic_lookup_section)
   PROCEDURE test_get_worksheet_template_returns_dynamic_lookup_section;

   -- %test(test_get_worksheet_template_returns_data_validation_section)
   PROCEDURE test_get_worksheet_template_returns_data_validation_section;

   -- %test(test_get_worksheet_template_returns_xlookups_section)
   PROCEDURE test_get_worksheet_template_returns_xlookups_section;

   --%endcontext




   --%context(-- get_open_payables_instructions_cursor Unit Tests)
   
   -- %test(test_get_open_payables_instructions_cursor_creates_cursor)
   PROCEDURE test_get_open_payables_instructions_cursor_creates_cursor;

   --%endcontext




   --%context(-- get_worksheets_cursor Unit Tests)

   -- %test(test_get_worksheets_cursor_creates_cursor)
   PROCEDURE test_get_worksheets_cursor_creates_cursor;

   --%endcontext



   -- %context(-- get_spreadsheet_filename Unit Tests)

   -- %test(test_get_spreadsheet_filename_returns_filename)
   PROCEDURE test_get_spreadsheet_filename_returns_filename;
   
   -- %endcontext


END test_spreadsheet;
/   
SHOW ERRORS




CREATE OR REPLACE PACKAGE BODY test_spreadsheet AS


   PROCEDURE load_test_spreadsheet IS
      /* Many of these tests rely on the "test_file.xlsx" in the PI_DATA_FILES 
         table.
   
         If the record is missing, it can be recreating by running the following 
         on the server (from the "simplipied" directory):
            cp test/test_file.xlsx /var/tmp/
            bin/pi_upload_data_file.sh /var/tmp/test_file.xlsx

         Alternatively, copy the test_file.xlsx file to the /opt/inbound 
         directory and run the following PL/SQL procedure:

            EXECUTE spreadsheet.load_data_file('test_file.xlsx')
      */

   BEGIN

      IF NOT spreadsheet.file_loaded('test_file.xlsx') then
         spreadsheet.load_data_file('test_file.xlsx');
      END IF;

   END load_test_spreadsheet;



   -- %test(test_file_loaded_detects_loaded_file)
   PROCEDURE test_file_loaded_detects_loaded_file IS 

      actual        BOOLEAN;
      test_filename VARCHAR2(200) := 'test_file.xlsx'; 

   BEGIN
      
              
      actual := spreadsheet.file_loaded(test_filename);

      ut.expect(actual,
         'test_file_loaded_detects_loaded_file'
         ).to_be_true;
         

   END test_file_loaded_detects_loaded_file;


   -- %test(test_file_loaded_detects_missing_file)
   PROCEDURE test_file_loaded_detects_missing_file IS 

      actual        BOOLEAN;
      test_filename VARCHAR2(200) := 'non-loaded_test_file.xlsx'; 

   BEGIN
      
              
      actual := spreadsheet.file_loaded(test_filename);

      ut.expect(actual,
         'test_file_loaded_detects_missing_file'
         ).to_be_false;
         

   END test_file_loaded_detects_missing_file;




   -- %test(test_load_data_file_loads_file_name)
   PROCEDURE test_load_data_file_loads_file_name IS 

      expected      VARCHAR2(1000);
      actual        VARCHAR2(1000);
      test_filename VARCHAR2(200) := 'another_test_file.xlsx'; 

   BEGIN
      
      expected := test_filename;
         
      spreadsheet.load_data_file(test_filename);

      SELECT file_name
        INTO actual
        FROM pi_data_files
       WHERE file_name = test_filename;

      ut.expect(actual,
         'test_load_data_file_loads_file_name'
         ).to_equal(expected);
         

   END test_load_data_file_loads_file_name;



   -- %test(test_load_data_file_loads_file_type)
   PROCEDURE test_load_data_file_loads_file_type IS 

      expected      VARCHAR2(1000) := 'XLSX';
      actual        VARCHAR2(1000);
      test_filename VARCHAR2(200) := 'another_test_file.xlsx'; 

   BEGIN
    
      spreadsheet.load_data_file(test_filename);

      SELECT file_type
        INTO actual
        FROM pi_data_files
       WHERE file_name = test_filename;

      ut.expect(actual,
         'test_load_data_file_loads_file_type'
         ).to_equal(expected);
         

   END test_load_data_file_loads_file_type;



   -- %test(test_load_data_file_loads_mime_type)
   PROCEDURE test_load_data_file_loads_mime_type IS 

      expected      VARCHAR2(1000) := 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet';
      actual        VARCHAR2(1000);
      test_filename VARCHAR2(200) := 'another_test_file.xlsx'; 

   BEGIN
    
      spreadsheet.load_data_file(test_filename);

      SELECT mime_type
        INTO actual
        FROM pi_data_files
       WHERE file_name = test_filename;

      ut.expect(actual,
         'test_load_data_file_loads_mime_type'
         ).to_equal(expected);
         

   END test_load_data_file_loads_mime_type;
   



   -- %test(test_get_column_alias_gets_first_column)
   PROCEDURE test_get_column_alias_gets_first_column IS 
      expected      VARCHAR2(100) := 'ClientCode';
      actual        VARCHAR2(100);
   BEGIN
      
      actual := spreadsheet.get_column_alias('test_file.xlsx', 'COL001');

      ut.expect(actual,
         'test_get_column_alias_gets_first_column'
         ).to_equal(expected);

   END test_get_column_alias_gets_first_column;


   -- %test(test_get_column_alias_gets_generic_name_if_column_not_loaded)
   PROCEDURE test_get_column_alias_gets_generic_name_if_column_not_loaded IS 
      expected      VARCHAR2(100) := 'COL050';
      actual        VARCHAR2(100);
   BEGIN
      
      actual := spreadsheet.get_column_alias('test_file.xlsx', 'COL050');

      ut.expect(actual,
         'test_get_column_alias_gets_generic_name_if_column_not_loaded'
         ).to_equal(expected);

   END test_get_column_alias_gets_generic_name_if_column_not_loaded;


   -- %test(test_get_column_alias_gets_generic_name_if_file_not_loaded)
   PROCEDURE test_get_column_alias_gets_generic_name_if_file_not_loaded IS 
      expected      VARCHAR2(100) := 'COL001';
      actual        VARCHAR2(100);
   BEGIN
      
      actual := spreadsheet.get_column_alias('non-existing_test_file.xlsx', 'COL001');

      ut.expect(actual,
         'test_get_column_alias_gets_generic_name_if_file_not_loaded'
         ).to_equal(expected);

   END test_get_column_alias_gets_generic_name_if_file_not_loaded;




   -- %test(test_build_spreadsheet_sql_returns_select_statement)
   PROCEDURE test_build_spreadsheet_sql_returns_select_statement IS 
      expected      VARCHAR2(3000);
      actual        VARCHAR2(3000);
   BEGIN
      
      expected := 
      'SELECT xl.line_number,  xl.col001 AS ClientCode,  xl.col002 AS ProviderTIN, xl.col003 AS ProviderAttention, ' || 
             'xl.col004 AS ProviderGroupName, xl.col005 AS ProviderAddressLine1, xl.col006 AS ProviderAddressLine2, ' || 
             'xl.col007 AS ProviderCity, xl.col008 AS ProviderStateCode, xl.col009 AS ProviderZip, xl.col010 AS LetterDate, ' || 
             'xl.col011 AS ClaimNumber, xl.col012 AS ClaimLineNumber, xl.col013 AS ClaimStateCode, xl.col014 AS MemberName, ' || 
             'xl.col015 AS MemberID, xl.col016 AS PatientAccountNumber, xl.col017 AS DateOfServiceFrom, xl.col018 AS DateOfServiceTo, ' || 
             'xl.col019 AS TotalBillAmount, xl.col020 AS PaidByPlanName, xl.col021 AS ClaimPaidDate, xl.col022 AS CheckNumber, ' || 
             'xl.col023 AS HspcEffDate, xl.col024 AS HspcEndDate, xl.col025 AS OverpaidAmount, xl.col026 AS OverpaymentDescription, ' || 
             'xl.col027 AS OverpaymentConcept, xl.col028 AS LegalHealthPlanName, xl.col029 AS HealthPlanLOB, xl.col030 AS AppealPOBox, ' || 
             'xl.col031 AS AppealAddressLine1, xl.col032 AS AppealAddressLine2, xl.col033 AS AppealCity, xl.col034 AS AppealStateCode, ' || 
             'xl.col035 AS AppealZipCode, xl.col036 AS AppealContactLine1, xl.col037 AS AppealContactLine2, xl.col038 AS AppealContactLine3, ' ||
             'xl.col039 AS AppealPhoneNumber, xl.col040 AS AppealFaxNumber, xl.col041 AS LetterRequestDate, xl.col042 AS LetterTemplateCode, ' || 
             'xl.col043 AS LetterFileName, xl.col044 AS COL044, xl.col045 AS COL045, xl.col046 AS COL046, xl.col047 AS COL047, xl.col048 AS COL048, ' || 
             'xl.col049 AS COL049, xl.col050 AS COL050 ' ||  
        'FROM pi_data_files df, ' || 
             'TABLE(apex_data_parser.parse(p_content => df.data_file, p_file_name => df.file_name)) xl ' || 
       'WHERE df.file_name = ''test_file.xlsx'' ' || 
         'AND xl.line_number > 1 ' ||  
       'ORDER BY xl.line_number';

      actual := spreadsheet.build_spreadsheet_sql('test_file.xlsx');

      ut.expect(actual,
         'test_build_spreadsheet_sql_returns_select_statement'
         ).to_equal(expected);

   END test_build_spreadsheet_sql_returns_select_statement;



   -- %test(test_build_spreadsheet_sql_returns_empty_table_query_if_no_file)
   PROCEDURE test_build_spreadsheet_sql_returns_empty_table_query_if_no_file IS 
      expected      VARCHAR2(3000);
      actual        VARCHAR2(3000);
   BEGIN
      
      expected := 
         'SELECT xl.line_number,' ||
               ' xl.col001, xl.col002, xl.col003, xl.col004, xl.col005,' ||
               ' xl.col006, xl.col007, xl.col008, xl.col009, xl.col010,' ||
			   --
               ' xl.col011, xl.col012, xl.col013, xl.col014, xl.col015,' ||
               ' xl.col016, xl.col017, xl.col018, xl.col019, xl.col020,' ||
			   --
               ' xl.col021, xl.col022, xl.col023, xl.col024, xl.col025,' ||
               ' xl.col026, xl.col027, xl.col028, xl.col029, xl.col030,' ||
			   --
               ' xl.col031, xl.col032, xl.col033, xl.col034, xl.col035,' ||
               ' xl.col036, xl.col037, xl.col038, xl.col039, xl.col040,' ||
			   --
               ' xl.col041, xl.col042, xl.col043, xl.col044, xl.col045,' ||
               ' xl.col046, xl.col047, xl.col048, xl.col049, xl.col050' || 
          ' FROM pi_data_files df,' ||
               ' TABLE(apex_data_parser.parse(p_content => df.data_file, p_file_name => df.file_name)) xl' ||
         ' WHERE 1 = 2' ||
         ' ORDER BY xl.line_number';

      actual := spreadsheet.build_spreadsheet_sql('non-existing_test_file.xlsx');

      ut.expect(actual,
         'test_build_spreadsheet_sql_returns_empty_table_query_if_no_file'
         ).to_equal(expected);

   END test_build_spreadsheet_sql_returns_empty_table_query_if_no_file;







   -- %test(test_get_spreadsheet_cursor_creates_cursor)
   PROCEDURE test_get_spreadsheet_cursor_creates_cursor IS 
      expected      SYS_REFCURSOR;
      actual        SYS_REFCURSOR;
   BEGIN
      
      OPEN expected FOR
         SELECT xl.line_number,  xl.col001 AS ClientCode,  xl.col002 AS ProviderTIN, xl.col003 AS ProviderAttention,  
                xl.col004 AS ProviderGroupName, xl.col005 AS ProviderAddressLine1, xl.col006 AS ProviderAddressLine2,  
                xl.col007 AS ProviderCity, xl.col008 AS ProviderStateCode, xl.col009 AS ProviderZip, xl.col010 AS LetterDate,  
                xl.col011 AS ClaimNumber, xl.col012 AS ClaimLineNumber, xl.col013 AS ClaimStateCode, xl.col014 AS MemberName,  
                xl.col015 AS MemberID, xl.col016 AS PatientAccountNumber, xl.col017 AS DateOfServiceFrom, xl.col018 AS DateOfServiceTo, 
                xl.col019 AS TotalBillAmount, xl.col020 AS PaidByPlanName, xl.col021 AS ClaimPaidDate, xl.col022 AS CheckNumber,  
                xl.col023 AS HspcEffDate, xl.col024 AS HspcEndDate, xl.col025 AS OverpaidAmount, xl.col026 AS OverpaymentDescription,  
                xl.col027 AS OverpaymentConcept, xl.col028 AS LegalHealthPlanName, xl.col029 AS HealthPlanLOB, xl.col030 AS AppealPOBox,  
                xl.col031 AS AppealAddressLine1, xl.col032 AS AppealAddressLine2, xl.col033 AS AppealCity, xl.col034 AS AppealStateCode,  
                xl.col035 AS AppealZipCode, xl.col036 AS AppealContactLine1, xl.col037 AS AppealContactLine2, xl.col038 AS AppealContactLine3, 
                xl.col039 AS AppealPhoneNumber, xl.col040 AS AppealFaxNumber, xl.col041 AS LetterRequestDate, xl.col042 AS LetterTemplateCode,  
                xl.col043 AS LetterFileName, xl.col044 AS COL044, xl.col045 AS COL045, xl.col046 AS COL046, xl.col047 AS COL047, xl.col048 AS COL048,  
                xl.col049 AS COL049, xl.col050 AS COL050   
           FROM pi_data_files df,  
                TABLE(apex_data_parser.parse(p_content => df.data_file, p_file_name => df.file_name)) xl 
          WHERE df.file_name = 'test_file.xlsx'  
            AND xl.line_number > 1  
          ORDER BY xl.line_number;

      actual := spreadsheet.get_spreadsheet_cursor('test_file.xlsx');

      ut.expect(actual,
         'test_get_spreadsheet_cursor_creates_cursor'
         ).to_equal(expected);

      IF expected%ISOPEN THEN
         CLOSE expected;
      END IF;

      IF actual%ISOPEN THEN
         CLOSE actual;
      END IF;

   END test_get_spreadsheet_cursor_creates_cursor;


-- %test(test_get_spreadsheet_cursor_has_rows)
   PROCEDURE test_get_spreadsheet_cursor_has_rows IS 
      expected      NUMBER := 4;
      actual        SYS_REFCURSOR;
   BEGIN

      actual := spreadsheet.get_spreadsheet_cursor('test_file.xlsx');

      ut.expect(actual,
         'test_get_spreadsheet_cursor_has_rows'
         ).to_have_count(expected);

      IF actual%ISOPEN THEN
         CLOSE actual;
      END IF;

   END test_get_spreadsheet_cursor_has_rows;


   -- %test(test_get_spreadsheet_cursor_is_empty_if_no_file)
   PROCEDURE test_get_spreadsheet_cursor_is_empty_if_no_file IS 
      actual SYS_REFCURSOR;
   BEGIN

      actual := spreadsheet.get_spreadsheet_cursor('non-existing_test_file.xlsx');

      ut.expect(actual,
         'test_get_spreadsheet_cursor_is_empty_if_no_file'
         ).to_be_empty();

      IF actual%ISOPEN THEN
         CLOSE actual;
      END IF;

   END test_get_spreadsheet_cursor_is_empty_if_no_file;




   -- %test(test_stage_data_loads_staging_table)
   PROCEDURE test_stage_data_loads_staging_table IS 
      expected   VARCHAR2(100) := 'ABC Health Group';
      actual     VARCHAR2(100);
   BEGIN

      spreadsheet.stage_data('test_file.xlsx');

      SELECT col004
        INTO actual
        FROM pi_spreadsheetstaging
       WHERE file_name = 'test_file.xlsx'
         AND line_number = 2;

      ut.expect(actual,
         'test_stage_data_loads_staging_table'
         ).to_equal(expected);

   END test_stage_data_loads_staging_table;


   -- %test(test_stage_data_clears_previous_spreadsheet)
   PROCEDURE test_stage_data_clears_previous_spreadsheet IS 
      expected   VARCHAR2(100) := 'ABC Health Group';
      actual     VARCHAR2(100);
   BEGIN

      spreadsheet.stage_data('test_file.xlsx');

      UPDATE pi_spreadsheetstaging 
         SET col004 = 'This value has changed'
      WHERE file_name = 'test_file.xlsx'
        AND line_number = 2;

      spreadsheet.stage_data('test_file.xlsx');

      SELECT col004
        INTO actual
        FROM pi_spreadsheetstaging
       WHERE file_name = 'test_file.xlsx'
         AND line_number = 2;

      ut.expect(actual,
         'test_stage_data_clears_previous_spreadsheet'
         ).to_equal(expected);

   END test_stage_data_clears_previous_spreadsheet;




   -- %test(test_get_open_payables_claims_cursor_creates_cursor)
   PROCEDURE test_get_open_payables_claims_cursor_creates_cursor IS 
      expected      SYS_REFCURSOR;
      actual        SYS_REFCURSOR;
   BEGIN
      
      OPEN expected FOR
         SELECT *
           FROM pi_open_payables_claims_staging
          WHERE sourcerecordid <> 'SourceRecordID' -- Skip the first row; it contains the internal column names
          ORDER BY TO_NUMBER(seqno);

      actual := spreadsheet.get_open_payables_claims_cursor;

      ut.expect(actual,
         'test_get_open_payables_claims_cursor_creates_cursor'
         ).to_equal(expected);

      IF expected%ISOPEN THEN
         CLOSE expected;
      END IF;

      IF actual%ISOPEN THEN
         CLOSE actual;
      END IF;

   END test_get_open_payables_claims_cursor_creates_cursor;



   -- %test(test_get_open_payables_claims_template_returns_template)
   PROCEDURE test_get_open_payables_claims_template_returns_template IS 
      expected      VARCHAR2(32767);
      actual        VARCHAR2(32767);
   BEGIN
      
      expected := '%[' ||
'    {% for claim in claims %}' ||
'        {' ||
'            "SourceRecordID": "{{ claim.SourceRecordID }}",' ||
'            "SeqNo": "{{ claim.SeqNo }}",' ||
'            "StateID": "{{ claim.StateID }}",' ||
'            "ProviderName": "{{ claim.ProviderName }}",' ||
'            "ProviderID": "{{ claim.ProviderID }}",' ||
'            "ProviderTIN": "{{ claim.ProviderTIN }}",' ||
'            "LOB": "{{ claim.LOB }}",' ||
'            "ProviderAccountID": "{{ claim.ProviderAccountID }}",' ||
'            "ClaimNumber": "{{ claim.ClaimNumber }}",' ||
'            "ClaimPaidDate": "{{ claim.ClaimPaidDate }}",' ||
'            "ClaimPaidAmount": {{ claim.ClaimPaidAmount }},' ||
'            "OpenPayableAmount": {{ claim.OpenPayableAmount }},' ||
'            "OpenPayableDate": "{{ claim.OpenPayableDate }}",' ||
'            "OpenPayableAge": "{{ claim.OpenPayableAge }}",' ||
'            "ShiftToAccount": "{{ claim.ShiftToAccount }}"' ||
'        }{% if not loop.last %},{% endif %}' ||
'        {% endfor %}' ||
'    ]%';

      actual := spreadsheet.get_open_payables_claims_template;

      ut.expect(actual,
         'test_get_open_payables_claims_template_returns_template'
         ).to_be_like(expected);  -- Actual contains more data, but should contain expected

   END test_get_open_payables_claims_template_returns_template;



   -- %test(test_get_open_payables_claims_template_includes_column_format)
   PROCEDURE test_get_open_payables_claims_template_includes_column_format IS 
      expected      VARCHAR2(32767);
      actual        VARCHAR2(32767);
   BEGIN
      
      expected := '%' ||
         '"column_format": {' ||
         '  "SourceRecordID": "number",' ||
         '  "SeqNo": "number",' ||
         '  "StateID": "text",' ||
         '  "ProviderName": "text",' ||
         '  "ProviderID": "text",' ||
         '  "ProviderTIN": "text",' ||
         '  "LOB": "text",' ||
         '  "ProviderAccountID": "text",' ||
         '  "ClaimNumber": "text",' ||
         '  "ClaimPaidDate": "date",' ||
         '  "ClaimPaidAmount": "currency",' || 
         '  "OpenPayableAmount": "currency",' ||
         '  "OpenPayableDate": "date",' ||
         '  "OpenPayableAge": "text",' ||
         '  "ShiftToAccount": "text"' ||
         '},%';

      actual := spreadsheet.get_open_payables_claims_template;

      ut.expect(actual,
         'test_get_open_payables_claims_template_includes_column_format'
         ).to_be_like(expected);  

   END test_get_open_payables_claims_template_includes_column_format;



   -- %test(test_get_open_payables_claims_template_includes_cell_styles)
   PROCEDURE test_get_open_payables_claims_template_includes_cell_styles IS 
      expected      VARCHAR2(32767);
      actual        VARCHAR2(32767);
   BEGIN
      
      expected := '%' ||
         '"cell_styles": { ' ||
         '  "ClaimPaidAmount": {' ||
         '    "font_color": "FF0000",' ||        -- red
         '    "font_size": 12,' ||
         '    "bg_color": "FFF0F0"' ||
         '  },%';

      actual := spreadsheet.get_open_payables_claims_template;

      ut.expect(actual,
         'test_get_open_payables_claims_template_includes_cell_styles'
         ).to_be_like(expected);  

   END  test_get_open_payables_claims_template_includes_cell_styles;



    -- %test(test_get_open_payables_claims_template_includes_conditional_formatting)
   PROCEDURE test_get_open_payables_claims_template_includes_conditional_formatting IS 
      expected      VARCHAR2(32767);
      actual        VARCHAR2(32767);
   BEGIN
      
      expected := '%' ||
         '"conditional_formatting": [' ||
         '  {' || 
         '    "column": "OpenPayableAmount",' ||
         '    "type": "greaterThan",' ||
         '    "value": 1000,' ||
         '    "font_color": "FFFFFF",' ||
         '    "bg_color": "FF0000"' ||
         '  }' ||
         '],%';

      actual := spreadsheet.get_open_payables_claims_template;

      ut.expect(actual,
         'test_get_open_payables_claims_template_includes_conditional_formatting'
         ).to_be_like(expected);  

   END  test_get_open_payables_claims_template_includes_conditional_formatting;




   -- %test(test_get_worksheet_template_returns_column_format_section)
   PROCEDURE test_get_worksheet_template_returns_column_format_section IS 
      expected      VARCHAR2(32767);
      actual        VARCHAR2(32767);
   BEGIN
      
       expected := '%' ||
         '"column_format": {' ||
         '  "SourceRecordID": "text",' ||
         '  "SeqNo": "text",' ||
         '  "StateID": "text",' ||
         '  "ProviderName": "text",' ||
         '  "ProviderID": "text",' ||
         '  "ProviderTIN": "text",' ||
         '  "LOB": "text",' ||
         '  "ProviderAccountID": "text",' ||
         '  "ClaimNumber": "text",' ||
         '  "ClaimPaidDate": "date",' ||
         '  "ClaimPaidAmount": "currency",' || 
         '  "OpenPayableAmount": "currency",' ||
         '  "OpenPayableDate": "date",' ||
         '  "OpenPayableAge": "text",' ||
         '  "ShiftToAccount": "text"' ||
         '},%';

      actual := spreadsheet.get_worksheet_template('Open Payables' , 'Claims');

      ut.expect(actual,
         'test_get_worksheet_template_returns_column_format_section'
         ).to_be_like(expected);  -- Actual contains more data, but should contain expected

   END test_get_worksheet_template_returns_column_format_section;



   -- %test(test_get_worksheet_template_returns_rows_section)
   PROCEDURE test_get_worksheet_template_returns_rows_section IS 
      expected      VARCHAR2(32767);
      actual        VARCHAR2(32767);
   BEGIN
      
       expected := '%"rows": [' ||
       '  {% for record in records %}' ||
       '    {' ||
       '      "SourceRecordID": "{{ record.SourceRecordID }}",' ||
       '      "SeqNo": "{{ record.SeqNo }}",' ||
       '      "StateID": "{{ record.StateID }}",' ||
       '      "ProviderName": "{{ record.ProviderName }}",' ||
       '      "ProviderID": "{{ record.ProviderID }}",' ||
       '      "ProviderTIN": "{{ record.ProviderTIN }}",' ||
       '      "LOB": "{{ record.LOB }}",' ||
       '      "ProviderAccountID": "{{ record.ProviderAccountID }}",' ||
       '      "ClaimNumber": "{{ record.ClaimNumber }}",' ||
       '      "ClaimPaidDate": "{{ record.ClaimPaidDate }}",' ||
       '      "ClaimPaidAmount": {{ record.ClaimPaidAmount }},' ||
       '      "OpenPayableAmount": {{ record.OpenPayableAmount }},' ||
       '      "OpenPayableDate": "{{ record.OpenPayableDate }}",' ||
       '      "OpenPayableAge": "{{ record.OpenPayableAge }}",' ||
       '      "ShiftToAccount": "{{ record.ShiftToAccount }}"' ||
       '    }{% if not loop.last %},{% endif %}' ||
       '    {% endfor %}' ||
      ']%';

      actual := spreadsheet.get_worksheet_template('Open Payables' , 'Claims');

      ut.expect(actual,
         'test_get_worksheet_template_returns_rows_section'
         ).to_be_like(expected);  -- Actual contains more data, but should contain expected

   END test_get_worksheet_template_returns_rows_section;



   -- %test(test_get_worksheet_template_returns_cell_styles_section)
   PROCEDURE test_get_worksheet_template_returns_cell_styles_section IS 
      expected      VARCHAR2(32767);
      actual        VARCHAR2(32767);
   BEGIN
      
      expected := '%' ||
         '"cell_styles": {' ||
         '  "SourceRecordID": {' ||    
         '    "font_name": "Times New Roman",' ||
         '    "font_size": 12,' || 
         '    "font_color": "336600",' || 
         '    "bold": true,' ||
         '    "italic": true' ||
         '  },' ||
         '  "SeqNo": {' || '%';

      actual := spreadsheet.get_worksheet_template('Open Payables' , 'Claims');

      ut.expect(actual,
         'test_get_worksheet_template_returns_cell_styles_section'
         ).to_be_like(expected);  

   END  test_get_worksheet_template_returns_cell_styles_section;



   -- %test(test_get_worksheet_template_returns_conditional_formatting_section)
   PROCEDURE test_get_worksheet_template_returns_conditional_formatting_section IS 
      expected      VARCHAR2(32767);
      actual        VARCHAR2(32767);
   BEGIN
      
       expected := '%' ||
         '"conditional_formatting": [' ||
         '  {' || 
         '    "column": "ClaimPaidAmount",' ||
         '    "type": "lessThanOrEqual",' ||
         '    "value": 100,' ||
         '    "font_color": "0000FF",' ||
         '    "bg_color": "808080"' ||
         '  },' ||
         '  {' || 
         '    "column": "OpenPayableAmount",' ||
         '    "type": "greaterThan",' ||
         '    "value": 1000,' ||
         '    "font_color": "FFFFFF",' ||
         '    "bg_color": "FF0000"' ||
         '  }' ||
         '],%';

      actual := spreadsheet.get_worksheet_template('Open Payables' , 'Claims');

      ut.expect(actual,
         'test_get_worksheet_template_returns_conditional_formatting_section'
         ).to_be_like(expected);  

   END  test_get_worksheet_template_returns_conditional_formatting_section;



   -- %test(test_get_worksheet_template_returns_locked_columns_section)
   PROCEDURE test_get_worksheet_template_returns_locked_columns_section IS 
      expected      VARCHAR2(32767);
      actual        VARCHAR2(32767);
   BEGIN
      
       expected := '%' ||
         '"locked_columns": [' ||
         '  "SourceRecordID",' ||
         '  "SeqNo", %';

      actual := spreadsheet.get_worksheet_template('Open Payables' , 'Claims');

      ut.expect(actual,
         'test_get_worksheet_template_returns_locked_columns_section'
         ).to_be_like(expected);  

   END  test_get_worksheet_template_returns_locked_columns_section;


   -- %test(test_get_worksheet_template_returns_unlocked_columns_section)
   PROCEDURE test_get_worksheet_template_returns_unlocked_columns_section IS 
      expected      VARCHAR2(32767);
      actual        VARCHAR2(32767);
   BEGIN
      
       expected := '%' ||
         '"unlocked_columns": [' ||
         '  "ShiftToAccount"%';

      actual := spreadsheet.get_worksheet_template('Open Payables' , 'Claims');

      ut.expect(actual,
         'test_get_worksheet_template_returns_unlocked_columns_section'
         ).to_be_like(expected);  

   END  test_get_worksheet_template_returns_unlocked_columns_section;


-- %test(test_get_worksheet_template_locks_sheet_if_locked_columns_present)
   PROCEDURE test_get_worksheet_template_locks_sheet_if_locked_columns_present IS 
      expected      VARCHAR2(32767);
      actual        VARCHAR2(32767);
   BEGIN
      
      expected := '%' ||
         '"lock_sheet": true, %';

      actual := spreadsheet.get_worksheet_template('Open Payables' , 'Claims');

      ut.expect(actual,
         'test_get_worksheet_template_locks_sheet_if_locked_columns_present'
         ).to_be_like(expected);  

   END  test_get_worksheet_template_locks_sheet_if_locked_columns_present;


   -- %test(test_get_worksheet_template_does_not_lock_sheet_if_no_locked_columns_present)
   PROCEDURE test_get_worksheet_template_does_not_lock_sheet_if_no_locked_columns_present IS 
      expected      VARCHAR2(32767);
      actual        VARCHAR2(32767);
   BEGIN
      
      expected := '%' ||
         '"lock_sheet": false, %';

      UPDATE pi_worksheet_column_templates
         SET lock_column = 'N'
       WHERE worksheet_id = 
             (SELECT worksheet_id
                FROM pi_worksheet_templates
               WHERE worksheet_name = 'Claims');

      actual := spreadsheet.get_worksheet_template('Open Payables' , 'Claims');

      ut.expect(actual,
         'test_get_worksheet_template_does_not_lock_sheet_if_no_locked_columns_present'
         ).to_be_like(expected);  

   END  test_get_worksheet_template_does_not_lock_sheet_if_no_locked_columns_present;


   -- %test(test_get_worksheet_template_returns_hidden_columns_section)
   PROCEDURE test_get_worksheet_template_returns_hidden_columns_section IS 
      expected      VARCHAR2(32767);
      actual        VARCHAR2(32767);
   BEGIN
      
       expected := '%' ||
         '"hidden_columns": [' ||
         '  "record_no"%';

      actual := spreadsheet.get_worksheet_template('Open Payables' , 'Instructions');

      ut.expect(actual,
         'test_get_worksheet_template_returns_hidden_columns_section'
         ).to_be_like(expected);  

   END  test_get_worksheet_template_returns_hidden_columns_section;


   -- %test(test_get_worksheet_template_returns_lookup_section)
   PROCEDURE test_get_worksheet_template_returns_lookup_section IS 
      expected      VARCHAR2(32767);
      actual        VARCHAR2(32767);
   BEGIN
      
       expected := '%' ||
         '"lookup_sources": {' ||
         '  "States": ' ||
         '    [ "AL", "AK", "AZ", "AR", "CA", "CO", "CT", "DE", "FL", "GA", ' ||
         '"HI", "ID", "IL", "IN", "IA", "KS", "KY", "LA", "ME", "MD", "MA", ' ||
         '"MI", "MN", "MS", "MO", "MT", "NE", "NV", "NH", "NJ", "NM", "NY", ' ||
         '"NC", "ND", "OH", "OK", "OR", "PA", "RI", "SC", "SD", "TN", "TX", ' ||
         '"UT", "VT", "VA", "WA", "WV", "WI", "WY" ],%';


      actual := spreadsheet.get_worksheet_template('Open Payables' , 'Claims');

      ut.expect(actual,
         'test_get_worksheet_template_returns_lookup_section'
         ).to_be_like(expected);  

   END  test_get_worksheet_template_returns_lookup_section;


   -- %test(test_get_worksheet_template_returns_dynamic_lookup_section)
   PROCEDURE test_get_worksheet_template_returns_dynamic_lookup_section IS 
      expected      VARCHAR2(32767);
      actual        VARCHAR2(32767);
   BEGIN
      
       expected := '%' ||
         '"Providers": {' ||
         '  "headers": [ "ProviderID", "ProviderName" ]%';


      actual := spreadsheet.get_worksheet_template('Open Payables' , 'Claims');

      ut.expect(actual,
         'test_get_worksheet_template_returns_dynamic_lookup_section'
         ).to_be_like(expected);  

   END  test_get_worksheet_template_returns_dynamic_lookup_section;



   -- %test(test_get_worksheet_template_returns_data_validation_section)
   PROCEDURE test_get_worksheet_template_returns_data_validation_section IS 
      expected      VARCHAR2(32767);
      actual        VARCHAR2(32767);
   BEGIN
      
       expected := '%' ||
         '"data_validations": [' ||
         '  { "column": "StateID", "source": "States", "allow_blank": false }%';


      actual := spreadsheet.get_worksheet_template('Open Payables' , 'Claims');

      ut.expect(actual,
         'test_get_worksheet_template_returns_data_validation_section'
         ).to_be_like(expected);  

   END  test_get_worksheet_template_returns_data_validation_section;



   -- %test(test_get_worksheet_template_returns_xlookups_section)
   PROCEDURE test_get_worksheet_template_returns_xlookups_section IS 
      expected      VARCHAR2(32767);
      actual        VARCHAR2(32767);
   BEGIN
      
       expected := '%' ||
         '"xlookups": [' ||
         '  { "result_column": "ProviderName", "key_column": "ProviderID", "source": "Providers", "if_not_found": "" }%';

      actual := spreadsheet.get_worksheet_template('Open Payables' , 'Claims');

      ut.expect(actual,
         'test_get_worksheet_template_returns_xlookups_section'
         ).to_be_like(expected);  

   END  test_get_worksheet_template_returns_xlookups_section;



   -- %test(test_get_open_payables_instructions_cursor_creates_cursor)
   PROCEDURE test_get_open_payables_instructions_cursor_creates_cursor IS 
      expected      SYS_REFCURSOR;
      actual        SYS_REFCURSOR;
   BEGIN
      
      OPEN expected FOR
         SELECT *
           FROM pi_open_payables_instructions_staging
          ORDER BY record_no;

      actual := spreadsheet.get_open_payables_instructions_cursor;

      ut.expect(actual,
         'test_get_open_payables_instructions_cursor_creates_cursor'
         ).to_equal(expected);

      IF expected%ISOPEN THEN
         CLOSE expected;
      END IF;

      IF actual%ISOPEN THEN
         CLOSE actual;
      END IF;

   END test_get_open_payables_instructions_cursor_creates_cursor;
   


   -- %test(test_get_worksheets_cursor_creates_cursor)
   PROCEDURE test_get_worksheets_cursor_creates_cursor IS 
      expected      SYS_REFCURSOR;
      actual        SYS_REFCURSOR;
   BEGIN
      
      OPEN expected FOR
         SELECT *
           FROM pi_worksheet_templates
          ORDER BY worksheet_order;

      actual := spreadsheet.get_worksheets_cursor('Open Payables');

      ut.expect(actual,
         'test_get_worksheets_cursor_creates_cursor'
         ).to_equal(expected);

      IF expected%ISOPEN THEN
         CLOSE expected;
      END IF;

      IF actual%ISOPEN THEN
         CLOSE actual;
      END IF;

   END test_get_worksheets_cursor_creates_cursor;




   -- %test(test_get_spreadsheet_filename_returns_filename)
   PROCEDURE test_get_spreadsheet_filename_returns_filename IS 
      expected      VARCHAR2(100) := 'Open_Payables.xlsx';
      actual        VARCHAR2(100);
   BEGIN
      
      actual := spreadsheet.get_spreadsheet_filename('Open Payables');

      ut.expect(actual,
         'test_get_spreadsheet_filename_returns_filename'
         ).to_equal(expected);

   END test_get_spreadsheet_filename_returns_filename;

END test_spreadsheet;
/
  

SHOW ERRORS

SET SERVEROUTPUT ON SIZE 1000000

BEGIN
   ut.run('test_spreadsheet');
END;
/

/*
BEGIN
   ut.run(ut_coverage_html_reporter());
END;
/
*/

SPOOL OFF

-- Complete
