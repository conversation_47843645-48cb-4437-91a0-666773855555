------------------------------------------------------------------------------
--
--   PROCEDURE:      pi_create_letters_pkg.sql
--
--   PURPOSE:        This script is used to create the LETTERS package.  
--                   This package is a number of Letters functions and
--                   procedures used by the application. 
--
--   PARAMETERS:     None. 
--
--   INSTRUCTIONS:   From within SQL*Plus, type:
--                      START pi_create_letters_pkg.sql
--
--   AUTHOR:         <PERSON>.
--
--   INSTALL SCHEMA: APPS
--
--
--   Copyright (c) 2025 <PERSON>
--   All rights reserved.
--
--   This software is proprietary and confidential. Unauthorized copying of this
--   file, via any medium, is strictly prohibited without the express permission
--   of the author or owning organization.
--
------------------------------------------------------------------------------

SET ECHO ON


SPOOL pi_create_letters_pkg

CREATE OR REPLACE PACKAGE letters AUTHID CURRENT_USER AS

   
   -- This function returns the data source query string used in the call to 
   -- APEX Office Print (AOP) to generate the overpayment letters
   FUNCTION get_aop_letter_data_source(p_letter_number IN VARCHAR2) 
      RETURN CLOB;     

   -- This overloaded function will restrict the query to the given claim.
   -- It's used to generate single letters for each individual claim.
   FUNCTION get_aop_letter_data_source(p_letter_number IN VARCHAR2, 
                                       p_claimNumber   IN VARCHAR2)
      RETURN CLOB;

   -- This function returns the Letter file name
   FUNCTION generate_file_name (p_letter_number IN VARCHAR2)
      RETURN VARCHAR2;


   -- This procedure generates letter file
   PROCEDURE generate_letter_file(p_letter_number IN VARCHAR2);  

   -- This overloaded procedure will restrict the letter to the given claim.
   -- It's used to generate single letters for each individual claim.
   PROCEDURE generate_letter_file(p_letter_number IN VARCHAR2, 
                                  p_claimNumber   IN VARCHAR2);

   -- This function returns the "to" contact info for the PostGrid curl 
   -- command to send the given letter
   FUNCTION get_postgrid_to_contact(p_letter_number IN VARCHAR2) 
      RETURN VARCHAR2;


   -- This function returns the "from" contact info for the PostGrid curl 
   -- command for the give provider.
   -- NOTE: This is hard-coded to "Alignment Health Plan" for now. It will
   -- be changed to get it from a database table.
   FUNCTION get_postgrid_from_contact(p_provider IN VARCHAR2) 
      RETURN VARCHAR2;


   -- This function returns the API key used for the PostGrid curl 
   -- command.
   FUNCTION get_postgrid_api_key(p_mode IN VARCHAR2) 
      RETURN VARCHAR2;


   -- This procedure sets the status of the given the letter to 'SENT'.
   PROCEDURE mark_letter_sent(p_letter_number IN VARCHAR2, p_transmit_file VARCHAR2, 
      p_postgrid_letter_id IN VARCHAR2); 


   -- This function returns the Letter Number for the given file name
   FUNCTION get_letter_number_from_filename (p_filename IN VARCHAR2) 
      RETURN VARCHAR2;


   -- This function uses APEX Web Services to return the PostGrid contact.
   -- The results are returned as JSON.
   FUNCTION get_postgrid_contact(p_contact_id  IN VARCHAR2, 
                                 p_environment IN VARCHAR2 DEFAULT 'TEST')
      RETURN CLOB;


   -- This function uses APEX Web Services to create a PostGrid contact.
   -- It returns the PostGrid Contact ID.
   FUNCTION create_postgrid_contact(p_letter_number IN VARCHAR2, 
                                    p_environment   IN VARCHAR2 DEFAULT 'TEST')
      RETURN VARCHAR2;   


   -- This function uses APEX Web Services to create a PostGrid "from" contact  
   -- for the client.
   -- NOTE: This is hard-coded to "Alignment Health Plan" for now. It will
   -- be changed to get it from a database table.
   FUNCTION create_postgrid_client_contact(p_client IN VARCHAR2, 
                                           p_environment IN VARCHAR2 DEFAULT 'TEST') 
      RETURN VARCHAR2;

   -- This function checks if an AOP letter file exists in the AOP_FILE_DIR 
   -- directory. 
   FUNCTION aop_file_exists(p_filename IN VARCHAR2)
      RETURN BOOLEAN;


   -- This function uses APEX Web Services to send a PDF letter to PostGrid.
   -- It returns the PostGrid command status.
   FUNCTION send_letter_to_postgrid(p_letter_number IN VARCHAR2, 
                                    p_environment   IN VARCHAR2 DEFAULT 'TEST')
      RETURN VARCHAR2;



   -- This function loads the given AOP letter file from the AOP_FILE_DIR into
   -- a BLOB. 
   FUNCTION load_aop_file_to_blob(p_filename IN VARCHAR2)
      RETURN BLOB;

   -- This procedure loads the given AOP letter file from the AOP_FILE_DIR into
   -- the RP_AOP_FILES table.  
   PROCEDURE load_aop_file_to_table(p_filename IN VARCHAR2);


   -- This procedure loads the given email fields into pi_email table.
   PROCEDURE log_email(p_subject   IN VARCHAR2, 
                       p_sender    IN VARCHAR2, 
                       p_date_sent IN VARCHAR2 DEFAULT NULL,
                       p_load_date IN DATE DEFAULT SYSDATE);



   -- This procedure loads the given scanned image file from the 
   -- AOP_FILE_DIR into the RP_SCANNED_DOCUMENTS table.  
   PROCEDURE load_scanned_file(p_filename IN VARCHAR2, 
                               p_pdf_file IN VARCHAR2);   




   -- This function returns the document ID for the given image and PDF file.  
   FUNCTION get_scanned_document_id(p_filename IN VARCHAR2, 
                                    p_pdf_file IN VARCHAR2) 
      RETURN NUMBER;                                                


   -- This procedure loads one line of scanned text to the document.
   PROCEDURE load_scanned_line(p_document_id IN NUMBER, 
                               p_line_number IN NUMBER, 
                               p_line_text   IN VARCHAR2);

   -- This procedure resubmits given letter to a new address
   PROCEDURE resend_letter_newaddr(p_notificationrefnum IN VARCHAR2,
                                   p_provider_tin       IN NUMBER,
                                   p_new_provideraddr1  IN VARCHAR2,
                                   p_new_provideraddr2  IN VARCHAR2,
                                   p_new_providercity   IN VARCHAR2,
                                   p_new_providerstate  IN VARCHAR2,
                                   p_new_providerzip    IN VARCHAR2);


   -- This procedure loads the given correspondence file from the 
   -- RPI_INBOUND Oracle directory into the FND_ATTACHMENTS table.  
  PROCEDURE load_correspondence_document(p_filename IN VARCHAR2, 
                                         p_description IN VARCHAR2 DEFAULT NULL);


   -- This function returns the document ID (attachmentid) for the given file.
   -- If there are multiple matching files, it returns the latest ID. 
   FUNCTION get_correspondence_document_id(p_filename IN VARCHAR2) 
      RETURN NUMBER;                   


   -- This procedure loads one line of text to the correspondence document.
   PROCEDURE load_correspondence_document_line(p_document_id IN NUMBER, 
                                               p_line_number IN NUMBER, 
                                               p_line_text   IN VARCHAR2,
                                               p_page_number IN NUMBER DEFAULT NULL,
                                               p_page_line   IN NUMBER DEFAULT NULL);




   -- This procedure loads the given data file from the 
   -- RPI_INBOUND into the RP_DATA_FILES table.  
   PROCEDURE load_data_file(p_file_name IN VARCHAR2);



   -- This function replaces the given string with the given replacement string
   -- and returns the new blob.
   FUNCTION replace_in_blob(p_blob IN BLOB, 
                            p_search IN VARCHAR2, 
                            p_replace IN VARCHAR2) 
      RETURN BLOB;


   -- The procedure writes the given BLOB to a file in the RPI_INBOUND 
   -- directory (unless specified differently).
   PROCEDURE write_blob_to_file (p_blob      IN  BLOB,
                                 p_filename  IN  VARCHAR2,
                                 p_directory IN  VARCHAR2 DEFAULT 'RPI_INBOUND');


   -- The function reads the given file form the RPI_INBOUND directory (unless specified differently) 
   -- directly into a blob.  
   FUNCTION read_blob_from_file (p_filename  IN  VARCHAR2,
                                 p_directory IN  VARCHAR2 DEFAULT 'RPI_INBOUND')
      RETURN BLOB;





   -- This procedure uses the given SQL statement to generate and export the 
   -- data to the given Excel file in the RPI_INBOUND directory (unless 
   -- specified differently).
   PROCEDURE export_data_to_excel_file(p_sql_statement IN VARCHAR2, 
                                       p_filename      IN  VARCHAR2,
                                       p_directory     IN  VARCHAR2 DEFAULT 'RPI_INBOUND');


   -- This procedure is used to produce the Excel tracker file for the given upload file
   PROCEDURE export_tracker_file(p_upload_file       IN VARCHAR2, 
                                 p_tracker_file_name IN VARCHAR2,
                                 p_directory         IN VARCHAR2 DEFAULT 'RPI_INBOUND');   


   -- This procedure is used to produce the Excel Symkey files for the given upload file
   PROCEDURE export_symkey_files(p_upload_file       IN VARCHAR2, 
                                 p_directory         IN VARCHAR2 DEFAULT 'RPI_INBOUND');      


   -- This procedure is used to export the claims letter files to the given 
   -- directory for the given upload file
   PROCEDURE export_claim_letter_files(p_upload_file       IN VARCHAR2, 
                                       p_directory         IN VARCHAR2 DEFAULT 'RPI_INBOUND');                                                                                        



   -- This function returns a cursor of the letter data.
   FUNCTION get_provider_letter_cursor(p_letter_number IN VARCHAR2)
      RETURN SYS_REFCURSOR;


   -- This function returns a cursor of the letter details (claims).
   FUNCTION get_provider_letter_details_cursor(p_letter_number IN VARCHAR2)
      RETURN SYS_REFCURSOR;


   -- This function returns a 10 character string based, on the provider group 
   -- name, with no spaces or special characters. It is used to support file 
   -- name generation.
   FUNCTION get_provider_short_name(p_provider_group_name IN VARCHAR2)
      RETURN VARCHAR2;

   -- This function returns a cursor of the individual claim letter data.
   FUNCTION get_claim_letter_cursor(p_letter_number IN VARCHAR2, 
                                    p_claim_number  IN VARCHAR2)
      RETURN SYS_REFCURSOR;

   -- This function returns a cursor of the individual claim letter details.
   FUNCTION get_claim_letter_details_cursor(p_letter_number IN VARCHAR2, 
                                            p_claim_number  IN VARCHAR2)
      RETURN SYS_REFCURSOR;


END letters;
/   
SHOW ERRORS


CREATE OR REPLACE PACKAGE BODY letters AS

   -- This function returns the data source query string used in the call to 
   -- APEX Office Print (AOP) to generate the overpayment letters
   FUNCTION get_aop_letter_data_source(p_letter_number IN VARCHAR2) 
      RETURN CLOB IS

      data_source CLOB;

   BEGIN
      data_source := q'[SELECT
   'pi_overpayments' AS "filename",
   CURSOR(
      SELECT
          CURSOR(
             SELECT del.notificationrefnumber letternumber,  
                    del.letterdate, 
                    del.providertin, 
                    NVL(del.providerattention, 'REFUND UNIT') providerattention,
                    del.providergroupname,
                    del.provideraddressline1,
                    del.provideraddressline2,
                    DECODE(provideraddressline2, NULL, 'false', 'true') provideraddressline2_available,
                    del.providercity, 
                    del.providerstatecode,
                    del.providerzip,
                    (SELECT SUM(overpaidamount) 
                       FROM ntf_notificationrequests 
                      WHERE notificationdeliveryid = del.notificationdeliveryid) totaloverpayment,
                    del.appealpobox,
                    del.appealaddressline1,
                    del.appealaddressline2,
                    DECODE(appealaddressline2, NULL, 'false', 'true') appealaddressline2_available, 
                    del.appealcity,
                    del.appealstatecode,
                    del.appealzipcode,
                    del.LetterText1 blurb,  
                    CURSOR(
                       SELECT req.notificationdeliveryid,
                              req.claimnumber,
                              req.claimlinenumber,
                              req.membername,
                              req.memberid,
                              req.patientaccountnumber,
                              req.dateofservicefrom,
                              req.dateofserviceto,
                              req.totalbillamount,
                              req.paidbyplan,
                              req.claimpaiddate,
                              req.checknumber,
                              req.overpaidamount,
                              req.overpaymentdescription,
                              req.overpaymentconcept,
                              req.submitdate,
                              req.statuscode,
                              req.hspceffdate,  
                              req.hspcenddate
                         FROM ntf_notificationrequests req
                        WHERE req.notificationdeliveryid = del.notificationdeliveryid
                        ORDER BY req.membername, req.claimnumber, req.claimlinenumber
                    ) AS "details"
              FROM ntf_notificationdeliveries del
             WHERE del.notificationrefnumber = ']' || p_letter_number || q'['
             ORDER BY del.notificationbatchid, del.notificationdeliveryid
          ) AS "letters"
        FROM dual
   ) AS "data"
  FROM dual    
  ]';

      RETURN data_source;

   END get_aop_letter_data_source; 


   -- This overloaded function will restrict the query to the given request ID.
   -- It's used to generate single letters for each individual claim.
   FUNCTION get_aop_letter_data_source(p_letter_number IN VARCHAR2, 
                                       p_claimNumber   IN VARCHAR2)
      RETURN CLOB IS

      data_source CLOB;

   BEGIN
      data_source := q'[SELECT
   'pi_overpayments' AS "filename",
   CURSOR(
      SELECT
          CURSOR(select del.notificationrefnumber letternumber,  
                        del.letterdate, 
                        del.providertin, 
                        NVL(del.providerattention, 'REFUND UNIT') providerattention,
                        del.providergroupname,
                        del.provideraddressline1,
                        del.provideraddressline2,
                        DECODE(provideraddressline2, NULL, 'false', 'true') provideraddressline2_available,
                        del.providercity, 
                        del.providerstatecode,
                        del.providerzip,
                        del.appealpobox,
                        del.appealaddressline1,
                        del.appealaddressline2,
                        DECODE(appealaddressline2, NULL, 'false', 'true') appealaddressline2_available, 
                        del.appealcity,
                        del.appealstatecode,
                        del.appealzipcode,
                        del.LetterText1 blurb,
                        req.membername,
                        req.memberid,
                        req.Claimnumber,
                        req.patientaccountnumber,
                        req.dateofservicefrom,
                        req.dateofserviceto,
                        req.totalbillamount,
                        req.paidbyplan,
                        req.claimpaiddate,
                        req.checknumber,
                        req.overpaidamount,
                        req.hspceffdate,  
                        req.hspcenddate,
                        CURSOR(SELECT nnr.overpaymentdescription||DECODE(claimlinenumber,'0',NULL,' on DOS '||TO_CHAR(dateofservicefrom,'dd-Mon-yyyy')) OVERPAYMENTDESCRIPTION
                                 FROM ntf_notificationrequests nnr
                                WHERE nnr.notificationdeliveryid = del.notificationdeliveryid
                                  AND nnr.ClaimNumber = req.ClaimNumber
                                ORDER BY nnr.claimlinenumber
                              ) AS "details"
                  from ntf_notificationdeliveries del
                  join (SELECT req.notificationdeliveryid,
                               req.ClaimNumber,
                               MAX(req.membername) membername,
                               MAX(req.memberid) memberid,
                               MAX(req.patientaccountnumber) patientaccountnumber,
                               MAX(req.dateofservicefrom) dateofservicefrom,
                               MAX(req.dateofserviceto) dateofserviceto,
                               MAX(req.totalbillamount) totalbillamount,
                               SUM(req.paidbyplan) paidbyplan,
                               MAX(req.claimpaiddate) claimpaiddate,
                               MAX(req.checknumber) checknumber,
                               SUM(req.overpaidamount) overpaidamount,
                               MAX(req.hspceffdate) hspceffdate,  
                               MAX(req.hspcenddate) hspcenddate
                   FROM ntf_notificationrequests req 
                  WHERE ClaimNumber = ']' || p_claimNumber || q'['
                  GROUP BY req.notificationdeliveryid
                          ,req.claimnumber
                 ) req on del.notificationdeliveryid = req.notificationdeliveryid
                 where del.notificationrefnumber = ']' || p_letter_number || q'['
                   and req.ClaimNumber = ']' || p_claimNumber || q'['
          ) AS "letters"
        FROM dual
   ) AS "data"
  FROM dual
  ]';

      RETURN data_source;

   END get_aop_letter_data_source;


   -- This function returns the Letter file name
   FUNCTION generate_file_name (p_letter_number IN VARCHAR2) 
      RETURN VARCHAR2 IS

      output_filename   VARCHAR2(200);

   BEGIN  

      SELECT transmitfilename
        INTO output_filename
        FROM NTF_NotificationDeliveries
       WHERE NotificationRefNumber = p_letter_number;

       RETURN output_filename;

   EXCEPTION
      WHEN NO_DATA_FOUND THEN
         RETURN NULL;

   END generate_file_name;



   -- This procedure generates letter file
   PROCEDURE generate_letter_file(p_letter_number IN VARCHAR2) IS

   BEGIN  

      generate_letter_file(p_letter_number, 0);

   END generate_letter_file;


   -- This overloaded procedure will restrict the letter to the given request ID.
   -- It's used to generate single letters for each individual claim.
   PROCEDURE generate_letter_file(p_letter_number IN VARCHAR2, 
                                  p_claimNumber   IN VARCHAR2) IS

      l_return          BLOB;
      l_output_filename VARCHAR2(100);
      l_output_file     UTL_FILE.FILE_TYPE;
      l_buffer          RAW(32767);
      l_amount          BINARY_INTEGER := 32767;
      l_pos             INTEGER := 1;
      l_blob            BLOB;
      l_blob_len        INTEGER;
      l_data_source     CLOB;
      l_template_source VARCHAR2(100);

   BEGIN  

      IF NVL(p_claimNumber, 0) = 0 THEN

         --Select the filename from the Delivery table
         SELECT transmitfilename
           INTO l_output_filename
           FROM NTF_NotificationDeliveries
          WHERE NotificationRefNumber = p_letter_number;

         l_data_source := get_aop_letter_data_source(p_letter_number);
         l_template_source := 'Provider_Overpayment_Template_02.docx';

      ELSE

         --Select the Filename from the Request Table
         SELECT MAX(nnr.transmitfilename)
           INTO l_output_filename
           FROM NTF_NotificationRequests nnr
           JOIN NTF_NotificationDeliveries nnd ON nnd.NotificationDeliveryID = nnr.NotificationDeliveryID
          WHERE nnd.NotificationRefNumber = p_letter_number
            AND nnr.ClaimNumber = p_claimNumber;

         l_data_source := get_aop_letter_data_source(p_letter_number, p_claimNumber);
         --dbms_output.put_line('Data Source >'||l_data_source);
         l_template_source := 'Claim_Overpayment_Template_03.docx';

      END IF;

      apex_session.create_session(p_app_id=>200, p_page_id=>26, p_username=>'RPADMIN'); 

      -- apex_debug.enable(p_level => apex_debug.c_log_level_info);
      -- for more details, use: c_log_level_app_trace
      -- apex_debug.message(p_message => 'Debug enabled.');


      l_return := aop_api_pkg.plsql_call_to_aop (
                p_data_type       => aop_api_pkg.c_source_type_sql,
                p_data_source     => l_data_source, 
                p_template_type   => aop_api_pkg.c_source_type_apex,
                p_template_source => l_template_source,
                --p_output_type     => aop_api_pkg.c_word_docx,
                p_output_type     => aop_api_pkg.c_pdf_pdf,
                p_output_filename => l_output_filename,
                p_aop_url         => 'http://api.apexofficeprint.com',
                p_api_key         => '2492586DA6B46C98E0637203000A2407', -- New Production key
                --p_api_key         => '249158A1AAA4677BE0637203000A7947', --AHC 100 Free
                --p_api_key         => '2129A2872EDF1142E0637203000AA225', --RPIE Key
                --p_aop_mode        => 'development',
                p_app_id          => 200);
                --p_aop_mode        => 'development');  


      -- Open file on the server to write
      l_blob_len := dbms_lob.getlength(l_return);
      l_output_file := utl_file.fopen('AOP_FILE_DIR', l_output_filename, 'wb', 32767);  -- /opt/aopdocs

      -- Read chunks of the BLOB and write them to the file
      -- until complete.
      WHILE l_pos <= l_blob_len LOOP
         DBMS_LOB.read(l_return, l_amount, l_pos, l_buffer);
         UTL_FILE.put_raw(l_output_file, l_buffer, TRUE);
         l_pos := l_pos + l_amount;
      END LOOP;

      -- Close the file.
      utl_file.fclose(l_output_file);

      -- Log or do other tasks as needed
      -- apex_debug.log('PDF file created at local drive'); 

      -- dbms_output.put_line('To view debug messages:');
      -- dbms_output.put_line('select * from apex_debug_messages where session_id = '
      --    || apex_util.get_session_state('APP_SESSION') || ' order by message_timestamp');

      apex_session.detach;

   END generate_letter_file;



   -- This function returns the "to" contact info for the PostGrid curl 
   -- command to send the given letter
   FUNCTION get_postgrid_to_contact(p_letter_number IN VARCHAR2) 
      RETURN VARCHAR2 IS

      delivery_rec        ntf_notificationdeliveries%ROWTYPE;
      postgrid_contact   VARCHAR2(1000) := '';

      CURSOR delivery_cursor IS
         SELECT * 
           FROM ntf_notificationdeliveries
          WHERE notificationrefnumber = p_letter_number
          ORDER BY notificationdeliveryid;

      -- The PostGrid "to" Contact fields are:
      -- firstName       <- Set this to PROVIDERATTENTION ("REFUND UNIT" if NULL)
      -- lastName 
      -- companyName     <- PROVIDERGROUPNAME
      -- addressLine1    <- PROVIDERADDRESSLINE1
      -- addressLine2    <- PROVIDERADDRESSLINE2 (leave off if NULL)
      -- city            <- PROVIDERCITY
      -- provinceOrState <- PROVIDERSTATECODE
      -- email
      -- phoneNumber
      -- jobTitle
      -- Manager
      -- postalOrZip     <- PROVIDERZIP
      -- country
      -- countryCode     <- US
      -- description
      -- metadata[friend]
      -- skipVerification
      -- forceVerifiedStatus

   BEGIN

      OPEN delivery_cursor;
      LOOP

         FETCH delivery_cursor INTO delivery_rec;
         EXIT WHEN delivery_cursor%NOTFOUND;

         -- firstName       <- Set this to PROVIDERATTENTION ("REFUND UNIT" if NULL)
         postgrid_contact := postgrid_contact ||
            '--data-urlencode firstName=''' || 
            NVL(delivery_rec.providerattention, 'REFUND UNIT')  || ''' '; 

         -- companyName     <- PROVIDERGROUPNAME
         postgrid_contact := postgrid_contact ||
            '--data-urlencode companyName=''' || 
            delivery_rec.providergroupname  || ''' ';

         -- addressLine1    <- PROVIDERADDRESSLINE1
         postgrid_contact := postgrid_contact ||
            '--data-urlencode addressLine1=''' || 
            delivery_rec.provideraddressline1  || ''' ';


         -- addressLine2    <- PROVIDERADDRESSLINE2 (leave off if NULL)
         IF delivery_rec.provideraddressline2 IS NOT NULL THEN
            postgrid_contact := postgrid_contact ||
               '--data-urlencode addressLine2=''' || 
               delivery_rec.provideraddressline2  || ''' ';
         END IF;

         -- city            <- PROVIDERCITY
         postgrid_contact := postgrid_contact ||
            '--data-urlencode city=''' || 
            delivery_rec.providercity  || ''' ';

         -- provinceOrState <- PROVIDERSTATECODE
         postgrid_contact := postgrid_contact ||
            '--data-urlencode provinceOrState=''' || 
            delivery_rec.providerstatecode  || ''' ';

         -- postalOrZip     <- PROVIDERZIP
         postgrid_contact := postgrid_contact ||
            '--data-urlencode postalOrZip=''' || 
            delivery_rec.providerzip  || ''' ';

         -- countryCode     <- US
         postgrid_contact := postgrid_contact ||
            '--data-urlencode ''countryCode=US'' ';   

      END LOOP;

      --dbms_output.put_line(postgrid_contact);

      RETURN postgrid_contact;

   END get_postgrid_to_contact;



   -- This function returns the "from" contact info for the PostGrid curl 
   -- command for the give provider.
   -- NOTE: This is hard-coded to "Alignment Health Plan" for now. It will
   -- be changed to get it from a database table.
   FUNCTION get_postgrid_from_contact(p_provider IN VARCHAR2) 
      RETURN VARCHAR2 IS

      postgrid_contact   VARCHAR2(1000) := '';

      -- The PostGrid contact fields are:
      -- firstName       <- Claims Recovery Department
      -- lastName 
      -- companyName     <- Alignment Health Plan
      -- addressLine1    <- P.O. Box 14010
      -- addressLine2    <- 
      -- city            <- Orange
      -- provinceOrState <- CA
      -- email
      -- phoneNumber     <- (*************
      -- jobTitle
      -- Manager
      -- postalOrZip     <- 92863
      -- country
      -- countryCode     <- US
      -- description
      -- metadata[friend]
      -- skipVerification
      -- forceVerifiedStatus

   BEGIN

      IF UPPER(p_provider) = 'ALIGNMENT HEALTH PLAN' THEN

         -- firstName       <- Claims Recovery Department
         postgrid_contact := postgrid_contact ||
            '--data-urlencode ''firstName=Claims Recovery Department'' '; 

         -- companyName     <- Alignment Health Plan
         postgrid_contact := postgrid_contact ||
            '--data-urlencode ''companyName=Alignment Health Plan'' '; 

         -- addressLine1    <- P.O. Box 14010
         postgrid_contact := postgrid_contact ||
            '--data-urlencode ''addressLine1=P.O. Box 14010'' '; 

         -- city            <- Orange
         postgrid_contact := postgrid_contact ||
            '--data-urlencode ''city=Orange'' '; 

         -- provinceOrState <- CA
         postgrid_contact := postgrid_contact ||
            '--data-urlencode ''provinceOrState=CA'' '; 

         -- postalOrZip     <- 92863
         postgrid_contact := postgrid_contact ||
            '--data-urlencode ''postalOrZip=92863'' '; 

         -- countryCode     <- US
         postgrid_contact := postgrid_contact ||
            '--data-urlencode ''countryCode=US'' '; 

         -- phoneNumber     <- (*************
         postgrid_contact := postgrid_contact || 
            '--data-urlencode ''phoneNumber=(*************'' ';

      END IF;

      -- dbms_output.put_line(postgrid_contact);

      RETURN postgrid_contact;

   END get_postgrid_from_contact;



   -- This function returns the API key used for the PostGrid curl 
   -- command.
   FUNCTION get_postgrid_api_key(p_mode IN VARCHAR2) 
      RETURN VARCHAR2 IS

      api_key   VARCHAR2(100);

   BEGIN

      SELECT v.keyvalue
        INTO api_key
        FROM fnd_key_values v,
             fnd_keys k
       WHERE v.keyid = k.keyid
         AND k.keycode = 'POSTGRID_API'
         AND v.keyvaluecode = UPPER(p_mode);

      RETURN api_key;

   END get_postgrid_api_key;



   -- This procedure sets the status of the given the letter to 'SENT'.
   PROCEDURE mark_letter_sent(p_letter_number IN VARCHAR2, p_transmit_file VARCHAR2, 
      p_postgrid_letter_id IN VARCHAR2) IS
   BEGIN

      UPDATE ntf_notificationdeliveries
         SET currentstatuscode = 'SENT_TO_POSTGRID',
             transmitrefid = p_postgrid_letter_id,
             actualtransmitdate = SYSDATE
       WHERE notificationrefnumber = p_letter_number
         AND transmitfilename = p_transmit_file
         AND NVL(currentstatuscode, 'NOT SENT') <> 'SENT_TO_POSTGRID'; -- Keep the original transmission date

   END mark_letter_sent;



   -- This function returns the Letter Number for the given file name
   FUNCTION get_letter_number_from_filename (p_filename IN VARCHAR2) 
      RETURN VARCHAR2 IS

      letter_number   VARCHAR2(200);

   BEGIN  

      SELECT notificationrefnumber
        INTO letter_number
        FROM ntf_notificationdeliveries
       WHERE transmitfilename = p_filename;

       RETURN letter_number;

   EXCEPTION
      WHEN NO_DATA_FOUND THEN
         RETURN NULL;

   END get_letter_number_from_filename;





   -- This function uses APEX Web Services to return the PostGrid contact.
   -- The results are returned as JSON.
   FUNCTION get_postgrid_contact(p_contact_id  IN VARCHAR2, 
                                 p_environment IN VARCHAR2 DEFAULT 'TEST')
      RETURN CLOB IS

       contact_info   CLOB;

   BEGIN
       apex_web_service.clear_request_headers;
       apex_web_service.g_request_headers(1).name := 'x-api-key';
       apex_web_service.g_request_headers(1).value := get_postgrid_api_key(p_environment); 

      contact_info := apex_web_service.make_rest_request(
         p_url => 'http://api.postgrid.com/print-mail/v1/contacts/' || p_contact_id,
	     p_http_method => 'GET');


      RETURN contact_info;

   END get_postgrid_contact;



   -- This function uses APEX Web Services to create a PostGrid contact.
   -- It returns the PostGrid Contact ID.
   FUNCTION create_postgrid_contact(p_letter_number IN VARCHAR2, 
                                    p_environment IN VARCHAR2 DEFAULT 'TEST')
      RETURN VARCHAR2 IS

      contact_info   CLOB;
      contact_id     VARCHAR2(40);
      parm_names     apex_application_global.VC_ARR2; 
      parm_values    apex_application_global.VC_ARR2;
      delivery_rec        ntf_notificationdeliveries%ROWTYPE;

      CURSOR delivery_cursor IS
         SELECT * 
           FROM ntf_notificationdeliveries
          WHERE notificationrefnumber = p_letter_number
          ORDER BY notificationdeliveryid;

      -- The PostGrid "to" Contact fields are:
      -- firstName       <- Set this to PROVIDERATTENTION ("REFUND UNIT" if NULL)
      -- lastName 
      -- companyName     <- PROVIDERGROUPNAME
      -- addressLine1    <- PROVIDERADDRESSLINE1
      -- addressLine2    <- PROVIDERADDRESSLINE2 (leave off if NULL)
      -- city            <- PROVIDERCITY
      -- provinceOrState <- PROVIDERSTATECODE
      -- email
      -- phoneNumber
      -- jobTitle
      -- Manager
      -- postalOrZip     <- PROVIDERZIP
      -- country
      -- countryCode     <- US
      -- description
      -- metadata[friend]
      -- skipVerification
      -- forceVerifiedStatus

   BEGIN

      apex_web_service.clear_request_headers;

      apex_web_service.g_request_headers(1).name := 'x-api-key';
      apex_web_service.g_request_headers(1).value := get_postgrid_api_key(p_environment); 
      apex_web_service.g_request_headers(2).name := 'Content-Type';
      apex_web_service.g_request_headers(2).value := 'application/x-www-form-urlencoded';

      OPEN delivery_cursor;
      LOOP

         FETCH delivery_cursor INTO delivery_rec;
         EXIT WHEN delivery_cursor%NOTFOUND;

         -- firstName       <- Set this to PROVIDERATTENTION ("REFUND UNIT" if NULL)
         parm_names(1)  := 'firstName'; 
         parm_values(1) := NVL(delivery_rec.providerattention, 'REFUND UNIT');

         -- companyName     <- PROVIDERGROUPNAME
         parm_names(2)  := 'companyName'; 
         parm_values(2) := delivery_rec.providergroupname;

         -- addressLine1    <- PROVIDERADDRESSLINE1
         parm_names(3)  := 'addressLine1'; 
         parm_values(3) := delivery_rec.provideraddressline1;


         -- addressLine2    <- PROVIDERADDRESSLINE2 
         parm_names(4)  := 'addressLine2'; 
         parm_values(4) := delivery_rec.provideraddressline2;

         -- city            <- PROVIDERCITY
         parm_names(5)  := 'city'; 
         parm_values(5) := delivery_rec.providercity;

         -- provinceOrState <- PROVIDERSTATECODE
         parm_names(6)  := 'provinceOrState'; 
         parm_values(6) := delivery_rec.providerstatecode;

         -- postalOrZip     <- PROVIDERZIP
         parm_names(7)  := 'postalOrZip'; 
         parm_values(7) := delivery_rec.providerzip;

         -- countryCode     <- US
         parm_names(8)  := 'countryCode'; 
         parm_values(8) := 'US'; 

      END LOOP;

      contact_info := apex_web_service.make_rest_request(
         p_url => 'http://api.postgrid.com/print-mail/v1/contacts/',
	      p_http_method => 'POST',
         p_parm_name => parm_names,
         p_parm_value => parm_values);

      -- dbms_output.put_line(apex_web_service.g_status_code);
      -- dbms_output.put_line('Response: ' || contact_info);

      -- Get the contact_id from the response
      apex_json.parse(contact_info);
      contact_id := apex_json.get_varchar2(p_path=>'id');

      RETURN contact_id;

   END create_postgrid_contact; 


   -- This function uses APEX Web Services to create a PostGrid "from" contact  
   -- for the client.
   -- NOTE: This is hard-coded to "Alignment Health Plan" for now. It will
   -- be changed to get it from a database table.
   FUNCTION create_postgrid_client_contact(p_client IN VARCHAR2, 
                                           p_environment IN VARCHAR2 DEFAULT 'TEST') 
      RETURN VARCHAR2 IS

      contact_info   CLOB;
      contact_id     VARCHAR2(40);
      parm_names     apex_application_global.VC_ARR2; 
      parm_values    apex_application_global.VC_ARR2;

      -- The PostGrid contact fields are:
      -- firstName       <- Claims Recovery Department
      -- lastName 
      -- companyName     <- Alignment Health Plan
      -- addressLine1    <- P.O. Box 14010
      -- addressLine2    <- 
      -- city            <- Orange
      -- provinceOrState <- CA
      -- email
      -- phoneNumber     <- (*************
      -- jobTitle
      -- Manager
      -- postalOrZip     <- 92863
      -- country
      -- countryCode     <- US
      -- description
      -- metadata[friend]
      -- skipVerification
      -- forceVerifiedStatus

   BEGIN

      apex_web_service.clear_request_headers;

      apex_web_service.g_request_headers(1).name := 'x-api-key';
      apex_web_service.g_request_headers(1).value := get_postgrid_api_key(p_environment); 
      apex_web_service.g_request_headers(2).name := 'Content-Type';
      apex_web_service.g_request_headers(2).value := 'application/x-www-form-urlencoded';

      IF UPPER(p_client) = 'ALIGNMENT HEALTH PLAN' THEN

         parm_names(1)  := 'firstName'; 
         parm_values(1) := 'Claims Recovery Department';

         parm_names(2)  := 'companyName'; 
         parm_values(2) := 'Alignment Health Plan';

         parm_names(3)  := 'addressLine1'; 
         parm_values(3) := 'P.O. Box 14010';

         parm_names(4)  := 'city'; 
         parm_values(4) := 'Orange';

         parm_names(5)  := 'provinceOrState'; 
         parm_values(5) := 'CA';

         parm_names(6)  := 'postalOrZip'; 
         parm_values(6) := '92863';

         parm_names(7)  := 'countryCode'; 
         parm_values(7) := 'US';

         parm_names(8)  := 'phoneNumber'; 
         parm_values(8) := '(*************';

         contact_info := apex_web_service.make_rest_request(
         p_url => 'http://api.postgrid.com/print-mail/v1/contacts/',
	      p_http_method => 'POST',
         p_parm_name => parm_names,
         p_parm_value => parm_values);

         -- dbms_output.put_line(apex_web_service.g_status_code);
         -- dbms_output.put_line('Response: ' || contact_info);

         -- Get the contact_id from the response
         apex_json.parse(contact_info);
         contact_id := apex_json.get_varchar2(p_path=>'id');

      END IF;

      RETURN contact_id;

   END create_postgrid_client_contact;






   -- This function checks if an AOP letter file exists in the AOP_FILE_DIR 
   -- directory. 
   FUNCTION aop_file_exists(p_filename IN VARCHAR2)
      RETURN BOOLEAN IS

      file_exists   BOOLEAN;
      file_length   NUMBER;
      block_size    NUMBER;

   BEGIN

      utl_file.fgetattr('AOP_FILE_DIR', p_filename, file_exists, file_length, block_size);

      RETURN file_exists;

   END aop_file_exists;



   -- This function uses APEX Web Services to send a PDF letter to PostGrid.
   -- It returns the PostGrid command status.
   FUNCTION send_letter_to_postgrid(p_letter_number IN VARCHAR2, 
                                    p_environment   IN VARCHAR2 DEFAULT 'TEST')
      RETURN VARCHAR2 IS

      upload_status         VARCHAR2(200);
      letter_filename       VARCHAR2(100);
      provider_contact_id   VARCHAR2(50);
      client_contact_id     VARCHAR2(50);
      upload_results        VARCHAR2(3000);
      letter_id             VARCHAR2(50);
      service_multipart     apex_web_service.t_multipart_parts;
      body_blob             BLOB := EMPTY_BLOB();

   BEGIN
      apex_web_service.clear_request_headers;
      apex_web_service.g_request_headers(1).name := 'x-api-key';
      apex_web_service.g_request_headers(1).value := get_postgrid_api_key(p_environment); 

      SELECT transmitfilename || '.pdf'
        INTO letter_filename
        FROM ntf_notificationdeliveries
       WHERE notificationrefnumber = p_letter_number
       ORDER BY notificationdeliveryid;

      --dbms_output.put_line('Picked File '||letter_filename);

      IF aop_file_exists(letter_filename) THEN

         --dbms_output.put_line('File Exists ');

         -- Create the Provider contact
         provider_contact_id := 
            create_postgrid_contact(p_letter_number, p_environment);

         --dbms_output.put_line('provider_contact_id : '||provider_contact_id);

         -- Create the Client contact
         client_contact_id := 
            create_postgrid_client_contact('Alignment Health Plan', p_environment);         

         --dbms_output.put_line('client_contact_id : '||client_contact_id);

         -- Send the letter to Postgrid
         -- Example:
         -- curl --location 'https://api.postgrid.com/print-mail/v1/letters' \
         --   --header 'x-api-key: test_sk_aEDYn7sZmXgGqvAa6KqXA2' \
         --   --form to="contact_th6gti46bsWkTmk3n9efz6" \
         --   --form from="contact_1b1gh33j5QSUf8h11YmcCQ" \
         --   --form forceVerifiedStatus=true
         --   --form addressPlacement=insert_blank_page \
         --   --form pdf=@"/opt/aopdocs/CCHP_951684089_SCRIPPSMER_20241028.pdf"
         apex_web_service.append_to_multipart (
                p_multipart    => service_multipart,
                p_name         => 'to',
                p_content_type => 'text/plain',
                p_body         => provider_contact_id );

         apex_web_service.append_to_multipart (
                p_multipart    => service_multipart,
                p_name         => 'from',
                p_content_type => 'text/plain',
                p_body         => client_contact_id );

         apex_web_service.append_to_multipart (
                p_multipart    => service_multipart,
                p_name         => 'forceVerifiedStatus',
                p_content_type => 'text/plain',
                p_body         => 'true' );

         apex_web_service.append_to_multipart (
                p_multipart    => service_multipart,
                p_name         => 'addressPlacement',
                p_content_type => 'text/plain',
                p_body         => 'insert_blank_page' );


         -- Add the PDF file
         body_blob := load_aop_file_to_blob(letter_filename);

         apex_web_service.append_to_multipart (
                p_multipart    => service_multipart,
                p_name         => 'pdf',
                p_filename     => letter_filename,
                p_content_type => 'application/octet-stream',
                p_body_blob    => body_blob ); 


         -- Debugging: Echo the request 
         -- body_blob := apex_web_service.generate_request_body(service_multipart);
         -- dbms_output.put_line('Body blob length: ' || dbms_lob.getlength(body_blob));
         -- dbms_output.put_line('the blob is read: ' ||
         --    utl_raw.cast_to_varchar2(dbms_lob.substr(body_blob, 1000, 1 ) ));

         upload_results := apex_web_service.make_rest_request(
            p_url => 'http://api.postgrid.com/print-mail/v1/letters/',
	         p_http_method => 'POST',
            p_body_blob => apex_web_service.generate_request_body(service_multipart));   

          --dbms_output.put_line(apex_web_service.g_status_code);
          --dbms_output.put_line('Response: ' || upload_results);

         -- Get the letter ID from the response
         apex_json.parse(upload_results);
         letter_id := apex_json.get_varchar2(p_path=>'id');

         -- If successful, mark the letter as "sent", and return the Letter ID
         -- (The transmitfilename does not include the ".pdf" file extension)    
         mark_letter_sent(p_letter_number, 
            SUBSTR(letter_filename, 1, INSTR(letter_filename, '.pdf') - 1), 
            letter_id);

         upload_status := letter_id;

      ELSE
         upload_status := 'ERROR: Letter file for ' || p_letter_number || ' not found.';
      END IF;

      RETURN upload_status;

   END send_letter_to_postgrid;


   -- This function loads the given AOP letter file from the AOP_FILE_DIR into
   -- a BLOB. 
   FUNCTION load_aop_file_to_blob(p_filename IN VARCHAR2)
      RETURN BLOB IS

      directory_name   VARCHAR2 (12) := 'AOP_FILE_DIR';
      file_handle      BFILE;
      return_blob      BLOB := empty_blob();

   BEGIN

      INSERT INTO pi_aop_files 
      (filename, blob_data)
      VALUES (p_filename, empty_blob())
         RETURN blob_data INTO return_blob;

      file_handle := bfilename(directory_name, p_filename);
      dbms_lob.fileopen(file_handle, dbms_lob.file_readonly);
      dbms_lob.loadfromfile(return_blob, file_handle, dbms_lob.getlength(file_handle));
      dbms_lob.fileclose(file_handle);

      RETURN return_blob;

   END load_aop_file_to_blob;



   -- This procedure loads the given AOP letter file from the AOP_FILE_DIR into
   -- the RP_AOP_FILES table.  
   PROCEDURE load_aop_file_to_table(p_filename IN VARCHAR2) IS

      directory_name   VARCHAR2 (12) := 'AOP_FILE_DIR';
      file_handle      BFILE;
      return_blob      BLOB := empty_blob();

   BEGIN

      INSERT INTO pi_aop_files 
      (filename, blob_data)
      VALUES (p_filename, empty_blob())
         RETURN blob_data INTO return_blob;

      file_handle := bfilename(directory_name, p_filename);
      dbms_lob.fileopen(file_handle, dbms_lob.file_readonly);
      dbms_lob.loadfromfile(return_blob, file_handle, dbms_lob.getlength(file_handle));
      dbms_lob.fileclose(file_handle);

   END load_aop_file_to_table;




   -- This procedure loads the given email fields into pi_email table.
   PROCEDURE log_email(p_subject   IN VARCHAR2, 
                       p_sender    IN VARCHAR2, 
                       p_date_sent IN VARCHAR2 DEFAULT NULL,
                       p_load_date IN DATE DEFAULT SYSDATE) IS

      return_id         NUMBER;
      translated_date   DATE;

   BEGIN

      IF p_date_sent IS NOT NULL THEN
         -- Email date formats will be like: Tue, 26 Nov 2024 14:44:08 -0600
         -- Remove the timezone Day prefix and the timezone suffix
         translated_date := TO_DATE(SUBSTR(p_date_sent, 5, 21), 'DD Mon YYYY HH24:MI:SS');
      END IF;

      INSERT INTO pi_email_log 
      (subject, sender, email_time, load_date)
      VALUES 
      (p_subject, p_sender, translated_date, p_load_date);

   END log_email;



   -- This procedure loads the given scanned image file from the 
   -- RPI_INBOUND into the RP_SCANNED_DOCUMENTS table.  
   PROCEDURE load_scanned_file(p_filename IN VARCHAR2, 
                               p_pdf_file IN VARCHAR2) IS

      directory_name   VARCHAR2 (12) := 'RPI_INBOUND';
      file_handle      BFILE;
      return_blob      BLOB := empty_blob();

   BEGIN

      INSERT INTO pi_scanned_documents 
      (original_filename, pdf_filename, pdf_file)
      VALUES (p_filename, p_pdf_file, empty_blob())
         RETURN pdf_file INTO return_blob;

      file_handle := bfilename(directory_name, p_pdf_file);

      IF dbms_lob.fileexists(file_handle) = 1 THEN
         dbms_lob.fileopen(file_handle, dbms_lob.file_readonly);
         dbms_lob.loadfromfile(return_blob, file_handle, dbms_lob.getlength(file_handle));
         dbms_lob.fileclose(file_handle);
      END IF;   

   END load_scanned_file;




   -- This function returns the document ID for the given image and PDF file.  
   FUNCTION get_scanned_document_id(p_filename IN VARCHAR2, 
                                    p_pdf_file IN VARCHAR2) 
      RETURN NUMBER IS

      return_doc_id   NUMBER;

   BEGIN

      SELECT document_id
        INTO return_doc_id
        FROM pi_scanned_documents
       WHERE original_filename = p_filename
         AND pdf_filename = p_pdf_file;

      RETURN return_doc_id;   

   EXCEPTION
      WHEN NO_DATA_FOUND THEN
         RETURN 0;   

      WHEN TOO_MANY_ROWS THEN

         SELECT MAX(document_id)
           INTO return_doc_id
          FROM pi_scanned_documents
         WHERE original_filename = p_filename
           AND pdf_filename = p_pdf_file;

         RETURN return_doc_id; 

   END get_scanned_document_id;




   -- This procedure loads one line of scanned text to the document.
   PROCEDURE load_scanned_line(p_document_id IN NUMBER, 
                               p_line_number IN NUMBER, 
                               p_line_text   IN VARCHAR2) IS

   BEGIN

      INSERT INTO pi_scanned_document_lines 
      (document_id, line_number, line_text)
      VALUES 
      (p_document_id, p_line_number, p_line_text);

   END load_scanned_line;


   -- This procedure resends the letter to a new address
   PROCEDURE resend_letter_newaddr(p_notificationrefnum IN VARCHAR2,
                                   p_provider_tin       IN NUMBER,
                                   p_new_provideraddr1  IN VARCHAR2,
                                   p_new_provideraddr2  IN VARCHAR2,
                                   p_new_providercity   IN VARCHAR2,
                                   p_new_providerstate  IN VARCHAR2,
                                   p_new_providerzip    IN VARCHAR2) IS

      l_NewNotificationRefNum NUMBER;
      l_NewNotificationDeliveryID NUMBER;

      CURSOR c_Not (p_notificationrefnum IN VARCHAR2
                   ,p_provider_tin IN VARCHAR2) IS
         SELECT *
           FROM NTF_NotificationDeliveries
          WHERE NotificationRefNumber = p_notificationrefnum
            AND ProviderTIN = p_provider_tin;

   BEGIN

      FOR r_not IN c_not (p_notificationrefnum
                          ,p_provider_tin) LOOP

         --Get the latest NotificationRefNumber
         SELECT NVL(TO_NUMBER(MAX(NotificationRefNumber))+1,TO_NUMBER(TO_CHAR(SYSDATE,'YYYYMMDD')||'00001'))
           INTO l_NewNotificationRefNum
           FROM ntf_notificationdeliveries
          WHERE SUBSTR(NotificationRefNumber,1,8) = TO_CHAR(SYSDATE,'YYYYMMDD');

         INSERT INTO NTF_NotificationDeliveries
                  (NotificationBatchID
                  ,NotificationRefNumber
                  ,ParentNotificationRefNum
                  ,transmitfilename
                  ,SourceSystemCode
                  ,ProviderTIN
                  ,ProviderAttention
                  ,ProviderGroupName
                  ,ProviderAddressLine1
                  ,ProviderAddressLine2
                  ,ProviderCity
                  ,ProviderStateCode
                  ,ProviderZip
                  ,LetterText1
                  ,LetterDate
                  )
           VALUES (0 --Put logic in for batch ID
                  ,TO_CHAR(l_NewNotificationRefNum)
                  ,r_not.NotificationRefNumber
                  ,SUBSTR(r_not.transmitfilename,1,LENGTH(r_not.transmitfilename)-13)||l_NewNotificationRefNum
                  ,r_not.SourceSystemCode
                  ,r_not.ProviderTIN
                  ,r_not.ProviderAttention
                  ,r_not.ProviderGroupName
                  ,p_new_provideraddr1
                  ,p_new_provideraddr2
                  ,p_new_providercity
                  ,p_new_providerstate
                  ,p_new_providerzip
                  ,r_not.LetterText1
                  ,SYSDATE)
         RETURNING NotificationDeliveryID INTO l_NewNotificationDeliveryID;

         INSERT INTO NTF_NotificationRequests
                  (NotificationDeliveryID
                  ,SourceSystemCode
                  ,NotificationBatchID
                  ,ClaimNumber
                  ,ClaimLineNumber
                  ,MemberName
                  ,MemberID
                  ,PatientAccountNumber
                  ,DateOfServiceFrom
                  ,DateOfServiceTo
                  ,TotalBillAmount
                  ,PaidByPlanName
                  ,ClaimPaidDate
                  ,CheckNumber
                  ,HspcEffDate
                  ,HspcEndDate
                  ,OverpaidAmount
                  ,OverpaymentDescription
                  ,OverpaymentConcept
                  ,statuscode
                  ,recordsource
                  ,recordsourcerefid
                  ,memberdob
                  ,paidbyplan
                  )
            SELECT l_NewNotificationDeliveryID
                  ,SourceSystemCode
                  ,0
                  ,ClaimNumber
                  ,ClaimLineNumber
                  ,MemberName
                  ,MemberID
                  ,PatientAccountNumber
                  ,DateOfServiceFrom
                  ,DateOfServiceTo
                  ,TotalBillAmount
                  ,PaidByPlanName
                  ,ClaimPaidDate
                  ,CheckNumber
                  ,HspcEffDate
                  ,HspcEndDate
                  ,OverpaidAmount
                  ,OverpaymentDescription
                  ,OverpaymentConcept
                  ,'NEW'
                  ,recordsource
                  ,recordsourcerefid
                  ,memberdob
                  ,paidbyplan
              FROM NTF_NotificationRequests
             WHERE NotificationDeliveryID = r_not.NotificationDeliveryID
               AND NVL(DeleteFlag,'N') = 'N';

      END LOOP;

   EXCEPTION WHEN OTHERS THEN
       dbms_output.put_line('FATAL ERROR >>'||SQLERRM);
   END resend_letter_newaddr;





   -- This procedure loads the given correspondence file from the 
   -- RPI_INBOUND Oracle dirrectory into the FND_ATTACHMENTS table.  
   PROCEDURE load_correspondence_document(p_filename IN VARCHAR2, 
                                          p_description IN VARCHAR2 DEFAULT NULL) IS

      directory_name        VARCHAR2 (12) := 'RPI_INBOUND';   -- /opt/inbound
      file_handle           BFILE;
      return_blob           BLOB := empty_blob();

      source_system_code    VARCHAR2(100);
      claim_number          VARCHAR2(100);
      document_identifier   VARCHAR2(100);

   BEGIN

      -- Get the file attributes
      -- Format: source_system_code || '_' || claim_number || '_' || document_identifier || '.PDF'
      -- Ex. 'AZHMO_20231101821019500006_APD243582436100154_KL.PDF'
      source_system_code := SUBSTR(p_filename, 1, INSTR(p_filename, '_') - 1);
      claim_number := SUBSTR(p_filename, INSTR(p_filename, '_') + 1, 
         INSTR(p_filename, '_', 1, 2) - INSTR(p_filename, '_', 1, 1) - 1);
      document_identifier := SUBSTR(p_filename, INSTR(p_filename, '_', 1, 2) + 1, 
         INSTR(p_filename, '.') - INSTR(p_filename, '_', 1, 2) - 1);  

      -- dbms_output.put_line('source_system_code:  ' || source_system_code);
      -- dbms_output.put_line('claim_number:        ' || claim_number);
      -- dbms_output.put_line('document_identifier: ' || document_identifier);


      INSERT INTO fnd_attachments 
      (attachmentsource, attachmentsourceid1, attachmentsourceid2, 
       attachmentsourceid3, documentcomments, filename, mimetype, 
       attachmentblob)
      VALUES 
      ('INBOUND', source_system_code, claim_number, document_identifier,
       p_description, p_filename, 'application/pdf', 
       empty_blob())
         RETURN attachmentblob INTO return_blob;

      file_handle := bfilename(directory_name, p_filename);

      IF dbms_lob.fileexists(file_handle) = 1 THEN
         dbms_lob.fileopen(file_handle, dbms_lob.file_readonly);
         dbms_lob.loadfromfile(return_blob, file_handle, dbms_lob.getlength(file_handle));
         dbms_lob.fileclose(file_handle);
      END IF;   

   END load_correspondence_document;



   -- This function returns the document ID (attachmentid) for the given file.
   -- If there are multiple matching files, it returns the latest ID. 
   FUNCTION get_correspondence_document_id(p_filename IN VARCHAR2) 
      RETURN NUMBER IS

      return_doc_id   NUMBER;

   BEGIN

      SELECT attachmentid
        INTO return_doc_id
        FROM fnd_attachments
       WHERE filename = p_filename;

      RETURN return_doc_id;   

   EXCEPTION
      WHEN NO_DATA_FOUND THEN
         RETURN 0;   

      WHEN TOO_MANY_ROWS THEN

         SELECT MAX(attachmentid)
           INTO return_doc_id
           FROM fnd_attachments
          WHERE filename = p_filename;

         RETURN return_doc_id; 

   END get_correspondence_document_id;  



   -- This procedure loads one line of text to the correspondence document.
   PROCEDURE load_correspondence_document_line(p_document_id IN NUMBER, 
                                               p_line_number IN NUMBER, 
                                               p_line_text   IN VARCHAR2,
                                               p_page_number IN NUMBER DEFAULT NULL,
                                               p_page_line   IN NUMBER DEFAULT NULL) IS

   BEGIN

      INSERT INTO fnd_attachment_lines 
      (attachmentid, line_number, line_text, page_number, page_line)
      VALUES 
      (p_document_id, p_line_number, p_line_text, NVL(p_page_number, 1), 
       NVL(p_page_line, p_line_number));

   END load_correspondence_document_line;





   -- This procedure loads the given data file from the 
   -- RPI_INBOUND into the RP_DATA_FILES table.  
   PROCEDURE load_data_file(p_file_name IN VARCHAR2) IS

      directory_name   VARCHAR2 (12) := 'RPI_INBOUND';
      file_handle      BFILE;
      return_blob      BLOB := empty_blob();
      file_extension   VARCHAR2(4);

   BEGIN

      file_extension := UPPER(SUBSTR(p_file_name, INSTR(p_file_name, '.') + 1));

      INSERT INTO pi_data_files 
      (file_name, file_type, data_file)
      VALUES (p_file_name, file_extension, empty_blob())
         RETURN data_file INTO return_blob;

      file_handle := bfilename(directory_name, p_file_name);

      IF dbms_lob.fileexists(file_handle) = 1 THEN
         dbms_lob.fileopen(file_handle, dbms_lob.file_readonly);
         dbms_lob.loadfromfile(return_blob, file_handle, dbms_lob.getlength(file_handle));
         dbms_lob.fileclose(file_handle);
      END IF;   

   END load_data_file;



 -- This function replaces the given string with the given replacement string
   -- and returns the new blob.
   FUNCTION replace_in_blob(p_blob IN BLOB, 
                            p_search IN VARCHAR2, 
                            p_replace IN VARCHAR2) 
      RETURN BLOB IS

      new_blob   BLOB; 
      position   NUMBER := 0;
      pre_blob   RAW(32767);
      post_blob  RAW(32767);

   BEGIN
      -- Check if the BLOB contains the search string
      position := DBMS_LOB.INSTR(p_blob, utl_raw.cast_to_raw(p_search), 1, 1);
      IF position > 0 THEN

         pre_blob := DBMS_LOB.SUBSTR(p_blob, position - 1, 1);
         post_blob := DBMS_LOB.SUBSTR(p_blob, LENGTH(p_blob), position + LENGTH(p_search));
         -- Debug
         -- DBMS_output.put_line('pre_blob:  ' || pre_blob || '  Length:' || DBMS_LOB.getlength(pre_blob));
         -- DBMS_output.put_line('post_blob: ' || post_blob || '  Length:' || DBMS_LOB.getlength(post_blob));
         -- DBMS_output.put_line('p_search:  ' || utl_raw.cast_to_raw(p_search));
         -- DBMS_output.put_line('p_replace: ' || utl_raw.cast_to_raw(p_replace));
      
         -- Replace the search string with the replacement string
         DBMS_LOB.CREATETEMPORARY(new_blob, TRUE);
         DBMS_LOB.OPEN(new_blob, DBMS_LOB.LOB_READWRITE);
         DBMS_LOB.WRITEAPPEND(new_blob, DBMS_LOB.getlength(pre_blob), pre_blob);
         DBMS_LOB.WRITEAPPEND(new_blob, LENGTH(p_replace), utl_raw.cast_to_raw(p_replace));
         DBMS_LOB.WRITEAPPEND(new_blob, DBMS_LOB.getlength(post_blob), post_blob);
         DBMS_LOB.CLOSE(new_blob);
      ELSE
         -- If the search string is not found, just return the original blob
         new_blob := p_blob;
      END IF;

      -- Check for more matches. If found, recursively call the function
      position := DBMS_LOB.INSTR(new_blob, utl_raw.cast_to_raw(p_search), 1, 1);
      IF position > 0 THEN
          new_blob := replace_in_blob(new_blob, p_search, p_replace);
      END IF;    

      RETURN new_blob;

   END replace_in_blob;


   -- The procedure writes the given BLOB to a file in the RPI_INBOUND 
   -- directory (unless specified differently).
   PROCEDURE write_blob_to_file (p_blob      IN  BLOB,
                                 p_filename  IN  VARCHAR2,
                                 p_directory IN  VARCHAR2 DEFAULT 'RPI_INBOUND') IS
   
      new_file  UTL_FILE.FILE_TYPE;
      buffer    RAW(32767);
      amount    BINARY_INTEGER := 32767;
      pos       INTEGER := 1;
      blob_len  INTEGER;
   BEGIN
      blob_len := DBMS_LOB.getlength(p_blob);
  
      -- Open the destination file.
      new_file := UTL_FILE.fopen(p_directory, p_filename,'wb', 32767);

      -- Read chunks of the BLOB and write them to the file until complete.
      WHILE pos <= blob_len LOOP
        DBMS_LOB.read(p_blob, amount, pos, buffer);
        UTL_FILE.put_raw(new_file, buffer, TRUE);
        pos := pos + amount;
      END LOOP;
  
      -- Close the file.
      UTL_FILE.fclose(new_file);
  
   EXCEPTION
       WHEN OTHERS THEN
       -- Close the file if something goes wrong.
       IF UTL_FILE.is_open(new_file) THEN
          UTL_FILE.fclose(new_file);
       END IF;
       RAISE;

   END write_blob_to_file;



   -- The function reads the given file form the RPI_INBOUND directory (unless specified differently) 
   -- directly into a blob.  
   FUNCTION read_blob_from_file (p_filename  IN  VARCHAR2,
                                 p_directory IN  VARCHAR2 DEFAULT 'RPI_INBOUND')
      RETURN BLOB IS

      file_handle      BFILE;
      return_blob      BLOB := empty_blob();

   BEGIN
      DBMS_LOB.CREATETEMPORARY(return_blob, TRUE);
      file_handle := bfilename(p_directory, p_filename);
      dbms_lob.fileopen(file_handle, dbms_lob.file_readonly);
      dbms_lob.loadfromfile(return_blob, file_handle, dbms_lob.getlength(file_handle));
      dbms_lob.fileclose(file_handle);

      RETURN return_blob;

   END read_blob_from_file;



   -- This procedure uses the given SQL statement to generate and export the 
   -- data to the given Excel file in the RPI_INBOUND directory (unless 
   -- specified differently).
   PROCEDURE export_data_to_excel_file(p_sql_statement IN VARCHAR2, 
                                       p_filename      IN VARCHAR2,
                                       p_directory     IN VARCHAR2 DEFAULT 'RPI_INBOUND') 
   IS 
 
      l_context    apex_exec.t_context; 
      l_export     apex_data_export.t_export;    

   BEGIN
      -- An APEX session must be created to use the APEX_EXEC package.
      apex_session.create_session(p_app_id=>200, p_page_id=>26, p_username=>'RPADMIN');

      /*
      type t_export is record (
         file_name               varchar2(32767),
         format                  t_format,
         mime_type               varchar2(32767),
         as_clob                 boolean,
         content_blob            blob,
         content_clob            clob );
      */
      l_context := apex_exec.open_query_context(
                      p_location    => apex_exec.c_location_local_db,
                      p_sql_query   => p_sql_statement);

      -- Use the APEX_DATA_EXPORT package EXPORT function to export the data 
      -- to an Excel "BLOB"
      l_export := apex_data_export.export(
                     p_context   => l_context,
                     p_format    => apex_data_export.c_format_xlsx,
                     p_file_name => p_filename);

       apex_exec.close( l_context );

      -- Write the Excel BLOB to a file
      write_blob_to_file(p_blob => l_export.content_blob,
                         p_filename => p_filename,
                         p_directory => p_directory);
    
   EXCEPTION
      WHEN OTHERS THEN
         apex_exec.close( l_context );
         RAISE;

   END export_data_to_excel_file;



   -- This procedure is used to produce the Excel tracker file for the given upload file
   PROCEDURE export_tracker_file(p_upload_file       IN VARCHAR2, 
                                 p_tracker_file_name IN VARCHAR2,
                                 p_directory         IN VARCHAR2 DEFAULT 'RPI_INBOUND') 
   IS 

      sql_statement     VARCHAR2(4000);

   BEGIN 

      sql_statement := 'SELECT claim_number, market, c_nc, overpaymentamount, ' ||
                              'from_date, to_date, paid_date, vendor, ' ||
                              'vendor_tin, overpaymentreason, root_cause, ' ||
                              'letter_date, offset_date ' ||
                         'FROM xxadt_ahc_filetracker_v ' ||
                        'WHERE uploadfullfilename = ''' || p_upload_file || ''' ';

  
      export_data_to_excel_file(sql_statement, p_tracker_file_name, p_directory);

   END export_tracker_file;   




   -- This procedure is used to produce the Excel Symkey files for the given upload file
   PROCEDURE export_symkey_files(p_upload_file       IN VARCHAR2, 
                                 p_directory         IN VARCHAR2 DEFAULT 'RPI_INBOUND') 
   IS 

      sql_statement       VARCHAR2(4000);
      target_file_file    VARCHAR2(100);
      process_status      VARCHAR2(100);
      source_system_code  VARCHAR2(100);
      run_date            VARCHAR2(100) := TO_CHAR(SYSDATE, 'MMDDRR');

      CURSOR markets (filename IN VARCHAR2) IS
         SELECT DISTINCT procstatus, sourcesystemcode
           FROM xxadt_ahc_simkey_v
          WHERE loadfilefullname = filename; 

   BEGIN 

      OPEN markets (p_upload_file);
      LOOP
         FETCH markets INTO process_status, source_system_code;
         EXIT WHEN markets%NOTFOUND;
    
         target_file_file := process_status || '_' || source_system_code || '_' || run_date || '.xlsx';
         -- dbms_output.put_line('Target File: ' || target_file_file);

         sql_statement := 'SELECT claimno, procstatus, procstatusnote ' ||
                            'FROM xxadt_ahc_simkey_v ' ||
                           'WHERE loadfilefullname = ''' || p_upload_file || ''' ' ||
                             'AND sourcesystemcode = ''' || source_system_code || '''';

         export_data_to_excel_file(sql_statement, target_file_file, p_directory);

      END LOOP;

      CLOSE markets;

   END export_symkey_files;




   -- This procedure is used to export the claims letter files to the given 
   -- directory for the given upload file
   PROCEDURE export_claim_letter_files(p_upload_file IN VARCHAR2, 
                                       p_directory   IN VARCHAR2 DEFAULT 'RPI_INBOUND') 
   IS 
      source_system_code  VARCHAR2(100);
      provider_id         VARCHAR2(100);
      claim_number        VARCHAR2(100);
      letter_number       VARCHAR2(100);
      output_filename     VARCHAR2(100);
      run_date            VARCHAR2(100) := TO_CHAR(SYSDATE, 'MMDDRR');
      letter_file         VARCHAR2(100);
      claim_letter        BLOB;


      -- This is a TEMPORARY source for getting the claim letter file name. 
      -- It will be replaced with the actual source once the new architecture 
      -- is defined.
      CURSOR claim_letters (filename IN VARCHAR2) IS
         SELECT DISTINCT l.sourcesystemcode, l.providerid, l.claimnumber, 
                d.NotificationRefNumber letter_number, 
                r.transmitfilename output_filename
           FROM NTF_NotificationRequests r,
                NTF_NotificationDeliveries d,
                (SELECT DISTINCT sourcesystemcode, providerid, claimnumber
                   FROM xxadt_ahc_letterinfoload
                  WHERE loadfilename = filename) l
          WHERE d.NotificationDeliveryID = r.NotificationDeliveryID
            AND r.ClaimNumber = l.claimnumber
          ORDER BY l.sourcesystemcode, l.providerid, l.claimnumber;

   BEGIN 

      OPEN claim_letters (p_upload_file);
      LOOP
         FETCH claim_letters INTO source_system_code, provider_id, claim_number, letter_number, output_filename;
         EXIT WHEN claim_letters%NOTFOUND;
    
         -- dbms_output.put_line('Source: ' || source_system_code || ' Provider ID: ' || provider_id || 
         --  ' Claim Letter: ' || letter_number || ' Output Filename: ' || output_filename);

         BEGIN

            SELECT filename, attachmentblob
              INTO letter_file, claim_letter
              FROM fnd_attachments
             WHERE filename LIKE output_filename || '%';

            -- dbms_output.put_line('Writing ' || letter_file || ' to ' || p_directory || '/' || letter_file);
            write_blob_to_file (claim_letter, letter_file, p_directory);

         EXCEPTION
            WHEN NO_DATA_FOUND THEN
               dbms_output.put_line('No letter file found for ' || output_filename);
            WHEN OTHERS THEN
               dbms_output.put_line('Error: ' || SQLERRM);
         END;   
 
      END LOOP;

      CLOSE claim_letters;

   END export_claim_letter_files;




   -- This function returns a cursor of the letter data.
   FUNCTION get_provider_letter_cursor(p_letter_number IN VARCHAR2)
      RETURN SYS_REFCURSOR IS

      l_letter_data     SYS_REFCURSOR;
      l_sql_statement   VARCHAR2(32767);

   BEGIN

      l_sql_statement := 
         'SELECT del.notificationrefnumber letternumber, ' ||
                'del.sourcesystemcode, ' ||
                'TO_CHAR(del.letterdate, ''MM/DD/YYYY'') letterdate, ' ||
                'del.providertin, ' ||
                'NVL(del.providerattention, ''REFUND UNIT'') providerattention, ' ||
                'del.providergroupname, ' ||
                'del.provideraddressline1, ' ||
                'del.provideraddressline2, ' ||
                'DECODE(provideraddressline2, NULL, ''false'', ''true'') provideraddressline2_available, ' ||
                'del.providercity, ' ||
                'del.providerstatecode, ' ||
                'del.providerzip, ' ||
                '(SELECT TO_CHAR(SUM(overpaidamount), ''FM$999,999,999.00'') ' ||
                  'FROM ntf_notificationrequests ' ||
                 'WHERE notificationdeliveryid = del.notificationdeliveryid) totaloverpayment, ' ||
                'del.appealpobox, ' ||
                'del.appealaddressline1, ' ||
                'del.appealaddressline2, ' ||
                'DECODE(appealaddressline2, NULL, ''false'', ''true'') appealaddressline2_available, ' ||
                'del.appealcity, ' ||
                'del.appealstatecode, ' ||
                'del.appealzipcode, ' ||
                'del.LetterText1 blurb, ' ||
                'REPLACE(del.providerid, ''-'', '''') providercode, ' ||
                'letters.get_provider_short_name(del.providergroupname) provider_short_name ' ||
           'FROM ntf_notificationdeliveries del ' ||
          'WHERE del.notificationrefnumber = ''' || p_letter_number || ''' ' ||
          'ORDER BY del.notificationbatchid, del.notificationdeliveryid';

      -- dbms_output.put_line('SQL: ' || l_sql_statement);

      OPEN l_letter_data FOR l_sql_statement;
   

      RETURN l_letter_data;

   EXCEPTION
      WHEN OTHERS THEN
         IF l_letter_data%ISOPEN THEN
            CLOSE l_letter_data;
         END IF;
         RAISE_APPLICATION_ERROR(-20001, 'An error occurred: ' || SQLERRM);

   END get_provider_letter_cursor; 



   -- This function returns a cursor of the letter details (claims).  
   FUNCTION get_provider_letter_details_cursor(p_letter_number IN VARCHAR2)
      RETURN SYS_REFCURSOR IS

      l_letter_details   SYS_REFCURSOR;
      l_sql_statement    VARCHAR2(32767);

   BEGIN

      l_sql_statement := 
         'SELECT req.notificationdeliveryid, ' ||
                'req.claimnumber, ' ||
                'req.claimlinenumber, ' ||
                'req.membername, ' ||
                'req.memberid, ' ||
                'req.patientaccountnumber, ' ||
                'TO_CHAR(req.dateofservicefrom, ''MM/DD/YYYY'') dateofservicefrom, ' ||
                'TO_CHAR(req.dateofserviceto, ''MM/DD/YYYY'') dateofserviceto, ' ||
                'TO_CHAR(req.totalbillamount, ''FM$999,999,999.00'') totalbillamount, ' ||
                'TO_CHAR(req.paidbyplan, ''FM$999,999,999.00'') paidbyplan, ' ||
                'TO_CHAR(req.claimpaiddate, ''MM/DD/YYYY'') claimpaiddate, ' ||
                'req.checknumber, ' ||
                'TO_CHAR(req.overpaidamount, ''FM$999,999,999.00'') overpaidamount, ' ||
                'req.overpaymentdescription, ' ||
                'req.overpaymentconcept, ' ||
                'TO_CHAR(req.submitdate, ''MM/DD/YYYY'') submitdate, ' ||
                'req.statuscode, ' ||
                'TO_CHAR(req.hspceffdate, ''MM/DD/YYYY'') hspceffdate, ' ||
                'TO_CHAR(req.hspcenddate, ''MM/DD/YYYY'') hspcenddate ' ||
           'FROM ntf_notificationrequests req ' ||
          'WHERE req.notificationdeliveryid =  ' ||
                '(SELECT del.notificationdeliveryid ' ||
                   'FROM ntf_notificationdeliveries del ' ||
                  'WHERE del.notificationrefnumber = ''' || p_letter_number || ''') ' ||
          'ORDER BY req.membername, req.claimnumber, req.claimlinenumber';


      -- dbms_output.put_line('SQL: ' || l_sql_statement);

      OPEN l_letter_details FOR l_sql_statement;
   

      RETURN l_letter_details;

   EXCEPTION
      WHEN OTHERS THEN
         IF l_letter_details%ISOPEN THEN
            CLOSE l_letter_details;
         END IF;
         RAISE_APPLICATION_ERROR(-20001, 'An error occurred: ' || SQLERRM);

   END get_provider_letter_details_cursor;




   -- This function returns a 10 character string based, on the provider group 
   -- name, with no spaces or special characters. It is used to support file 
   -- name generation.
   FUNCTION get_provider_short_name(p_provider_group_name IN VARCHAR2)
      RETURN VARCHAR2 IS

   BEGIN
      
      RETURN SUBSTR(REGEXP_REPLACE(p_provider_group_name, '[^A-Za-z0-9]', ''), 1, 10);

   END get_provider_short_name;


   -- This function returns a cursor of the individual claim letter data.
   FUNCTION get_claim_letter_cursor(p_letter_number IN VARCHAR2, 
                                    p_claim_number  IN VARCHAR2)
      RETURN SYS_REFCURSOR IS

      l_letter_data     SYS_REFCURSOR;
      l_sql_statement   VARCHAR2(32767);

   BEGIN

      l_sql_statement := 
         'SELECT del.notificationrefnumber letternumber, ' ||
                'del.sourcesystemcode, ' ||      
                'TO_CHAR(del.letterdate, ''MM/DD/YYYY'') letterdate, ' || 
                'del.providertin, ' || 
                'NVL(del.providerattention, ''REFUND UNIT'') providerattention, ' ||
                'del.providergroupname, ' ||
                'del.provideraddressline1, ' ||
                'del.provideraddressline2, ' ||
                'del.providercity, ' || 
                'del.providerstatecode, ' ||
                'del.providerzip, ' ||
                'del.appealpobox, ' ||
                'del.appealaddressline1, ' ||
                'del.appealaddressline2, ' ||
                'del.appealcity, ' ||
                'del.appealstatecode, ' ||
                'del.appealzipcode, ' ||
                'del.LetterText1 blurb, ' ||
                'req.membername, ' ||
                'req.memberid, ' ||
                'TO_CHAR(req.memberdob, ''MM/DD/YYYY'') memberdob, ' ||
                'req.claimnumber, ' ||
                'req.patientaccountnumber, ' ||
                'TO_CHAR(req.dateofservicefrom, ''MM/DD/YYYY'') dateofservicefrom, ' ||
                'TO_CHAR(req.dateofserviceto, ''MM/DD/YYYY'') dateofserviceto, ' ||
                'TO_CHAR(req.totalbillamount, ''FM$999,999,999.00'') totalbillamount, ' ||
                'TO_CHAR(req.paidbyplan, ''FM$999,999,999.00'') paidbyplan, ' ||
                'TO_CHAR(req.claimpaiddate, ''MM/DD/YYYY'') claimpaiddate, ' ||
                'req.checknumber, ' ||
                'TO_CHAR(req.overpaidamount, ''FM$999,999,999.00'') overpaidamount, ' ||
                'TO_CHAR(req.hspceffdate, ''MM/DD/YYYY'') hspceffdate, ' ||  
                'TO_CHAR(req.hspcenddate, ''MM/DD/YYYY'') hspcenddate, ' ||
                'SUBSTR(REGEXP_REPLACE(membername, ''[^A-Za-z0-9]'', ''''), 1, 10) membershortname ' ||
           'FROM ntf_notificationdeliveries del, ' ||
                '(SELECT notificationdeliveryid, ' ||
                        'claimnumber, ' ||
                        'MAX(membername) membername, ' ||
                        'MAX(memberid) memberid, ' ||
                        'MAX(memberdob) memberdob, ' ||
                        'MAX(patientaccountnumber) patientaccountnumber, ' ||
                        'MAX(dateofservicefrom) dateofservicefrom, ' ||
                        'MAX(dateofserviceto) dateofserviceto, ' ||
                        'MAX(totalbillamount) totalbillamount, ' ||
                        'SUM(paidbyplan) paidbyplan, ' ||
                        'MAX(claimpaiddate) claimpaiddate, ' ||
                        'MAX(checknumber) checknumber, ' ||
                        'SUM(overpaidamount) overpaidamount, ' ||
                        'MAX(hspceffdate) hspceffdate, ' ||  
                        'MAX(hspcenddate) hspcenddate ' ||
                   'FROM ntf_notificationrequests req ' ||
                  'WHERE claimnumber = ''' || p_claim_number || ''' ' ||
                  'GROUP BY notificationdeliveryid, claimnumber) req ' ||
          'WHERE del.notificationdeliveryid = req.notificationdeliveryid ' ||
            'AND del.notificationrefnumber = ''' || p_letter_number || ''' ' ||
            'AND req.claimnumber = ''' || p_claim_number || '''';

      -- dbms_output.put_line('SQL: ' || l_sql_statement);

      OPEN l_letter_data FOR l_sql_statement;
   

      RETURN l_letter_data;

   EXCEPTION
      WHEN OTHERS THEN
         IF l_letter_data%ISOPEN THEN
            CLOSE l_letter_data;
         END IF;
         RAISE_APPLICATION_ERROR(-20001, 'An error occurred: ' || SQLERRM);

   END get_claim_letter_cursor;



   -- This function returns a cursor of the individual claim letter details.
   FUNCTION get_claim_letter_details_cursor(p_letter_number IN VARCHAR2, 
                                            p_claim_number  IN VARCHAR2)
      RETURN SYS_REFCURSOR IS

      l_letter_data     SYS_REFCURSOR;
      l_sql_statement   VARCHAR2(32767);

   BEGIN

      l_sql_statement := 
         'SELECT overpaymentdescription || ' ||
                'DECODE(claimlinenumber, ''0'', NULL,'' on DOS '' ||  ' ||
                'TO_CHAR(dateofservicefrom,''dd-Mon-yyyy'')) overpaymentdescription ' ||
           'FROM ntf_notificationrequests ' || 
          'WHERE notificationdeliveryid = ' || 
                '(SELECT notificationdeliveryid ' ||
                   'FROM ntf_notificationdeliveries ' ||
                  'WHERE notificationrefnumber = ''' || p_letter_number || ''') ' ||
            'AND claimnumber = ''' || p_claim_number || ''' ' ||
          'ORDER BY claimlinenumber ';


      -- dbms_output.put_line('SQL: ' || l_sql_statement);

      OPEN l_letter_data FOR l_sql_statement;
   

      RETURN l_letter_data;

   EXCEPTION
      WHEN OTHERS THEN
         IF l_letter_data%ISOPEN THEN
            CLOSE l_letter_data;
         END IF;
         RAISE_APPLICATION_ERROR(-20001, 'An error occurred: ' || SQLERRM);

   END get_claim_letter_details_cursor;

END letters;
/   
SHOW ERRORS

SPOOL OFF

-- Complete
