------------------------------------------------------------------------------
--
--   PROCEDURE:      pi_create_test_host_request_pkg.sql
--
--   PURPOSE:        This script is used to create the TEST_HOST_REQUEST
--                   package. This package contains the unit tests for the
--                   HOST_REQUEST package.
--
--   PARAMETERS:     None. 
--
--   INSTRUCTIONS:   From within SQL*Plus, type:
--                      START pi_create_test_host_request_pkg.sql
--
--   AUTHOR:         <PERSON>.
--
--   INSTALL SCHEMA: APPS
--
--
--   Copyright (c) 2025 Kevin <PERSON>
--   All rights reserved.
--
--   This software is proprietary and confidential. Unauthorized copying of this
--   file, via any medium, is strictly prohibited without the express permission
--   of the author or owning organization.
--
------------------------------------------------------------------------------
SET ECHO ON


SPOOL pi_create_test_host_request_pkg

CREATE OR REPLACE PACKAGE test_host_request AUTHID CURRENT_USER AS

   --%suite(test_host_request Unit Test Package (pi_create_test_host_request_pkg.sql))
   /* --%rollback(manual) */

   test_credential VARCHAR2(30) := 'rp_migrateTest';

   /* -- %beforeall (Turned this off due to DDL) */
   PROCEDURE create_test_credential;

   /* -- %afterall (Turned this off due to DDL) */
   PROCEDURE remove_test_credential;

   /* 
      The host_request functions and procedures require the credential exist.
      
      Use the DBA_CREDENTIAL.CREATE_CREDENTIAL procedure when connected as 
      APPS as SYSDBA:

      BEGIN
  
         DBMS_CREDENTIAL.CREATE_CREDENTIAL(
            credential_name => 'rp_migrateTest',
            username        => 'rp_migrateTest',
            password        => '<password>');

      END;

      These units tests use the "rp_migrateTest" credential. 
   */

   --%context(-- create_credential Unit Tests)

   -- %test(test_create_credential_creates_a_credential)
   -- %disabled 
   PROCEDURE test_create_credential_creates_a_credential;
   -- The CREATE_CREDENTIAL procedure is DDL, which can't be rolled back.
   -- Use "--%rollback(manual)" 

   --%endcontext


   --%context(-- credential_exists Unit Tests)

   -- %test(test_credential_exists_detects_existing_credential)
   -- %disabled 
   PROCEDURE test_credential_exists_detects_existing_credential;

   -- %test(test_credential_exists_detects_misssing_credential)
   PROCEDURE test_credential_exists_detects_misssing_credential;

   --%endcontext


   --%context(-- run_host_command Unit Tests)

   -- %test(test_run_host_command_creates_schedule_job)
   -- %disabled 
   PROCEDURE test_run_host_command_creates_schedule_job;

   --%endcontext


   --%context(-- get_new_requests Unit Tests)

   -- %test(test_get_new_requests_gets_single_request)
   PROCEDURE test_get_new_requests_gets_single_request;

   -- %test(test_get_new_requests_gets_multiple_requests)
   PROCEDURE test_get_new_requests_gets_multiple_requests;

   -- %test(test_get_new_requests_handles_no_new_requests)
   PROCEDURE test_get_new_requests_handles_no_new_requests;
   
   --%endcontext


   --%context(-- create_request Unit Tests)

   -- %test(test_create_request_creates_a_new_host_request)
   PROCEDURE test_create_request_creates_a_new_host_request;

   --%endcontext




   --%context(-- hold_new_requests Unit Tests)

   -- %test(test_hold_new_requests_set_new_requests_on_hold)
   PROCEDURE test_hold_new_requests_set_new_requests_on_hold;
   
   --%endcontext




   --%context(-- release_held_requests Unit Tests)

   -- %test(test_release_held_requests_set_status_back_to_new)
   PROCEDURE test_release_held_requests_set_status_back_to_new;

   --%endcontext




   --%context(-- delete_request Unit Tests)

   -- %test(test_delete_request_removes_request)
   PROCEDURE test_delete_request_removes_request;

   -- %test(test_delete_request_removes_correct_request)
   PROCEDURE test_delete_request_removes_correct_request;

   --%endcontext




   --%context(-- get_request_id Unit Tests)

   -- %test(test_get_request_id_finds_request)
   PROCEDURE test_get_request_id_finds_request;

   --%endcontext




   --%context(-- mark_status Unit Tests)

   -- %test(test_mark_status_updates_request_status)
   PROCEDURE test_mark_status_updates_request_status;
   
   --%endcontext




   --%context(-- get_status Unit Tests)

   -- %test(test_get_status_retrieves_request_status)
   PROCEDURE test_get_status_retrieves_request_status;
   
   --%endcontext




   --%context(-- copy_staged_results Unit Tests)

   -- %test(test_copy_staged_results_loads_execution_results)
   PROCEDURE test_copy_staged_results_loads_execution_results;

   --%endcontext



END test_host_request;
/   
SHOW ERRORS




CREATE OR REPLACE PACKAGE BODY test_host_request AS

   PROCEDURE create_test_credential IS
   BEGIN

      IF NOT host_request.credential_exists(test_credential) THEN

         host_request.create_credential(test_credential, test_credential, 'Welcome#123' );

      END IF;

   END create_test_credential;

   
   PROCEDURE remove_test_credential IS
   BEGIN

      IF host_request.credential_exists(test_credential) THEN

         host_request.remove_credential(test_credential);

      END IF;

   END remove_test_credential;
   
   


   -- %test(test_create_credential_creates_a_credential)
   PROCEDURE test_create_credential_creates_a_credential IS 

      actual          BOOLEAN;
      test_credential VARCHAR2(20) := 'rp_migrateTest'; 
      record_count    NUMBER := 0;

   BEGIN

      SELECT COUNT(*)
        INTO record_count
        FROM all_credentials
       WHERE credential_name = UPPER(test_credential);

      IF record_count = 1 THEN
         actual := TRUE;
      ELSE
         actual := FALSE;
      END IF;

      ut.expect(actual,
         'test_create_credential_creates_a_credential'
         ).to_be_true;

   END test_create_credential_creates_a_credential;
   


   -- %test(test_credential_exists_detects_existing_credential)
   PROCEDURE test_credential_exists_detects_existing_credential IS
   BEGIN

      ut.expect(host_request.credential_exists(test_credential),
         'test_credential_exists_detects_existing_credential'
         ).to_be_true;

   END test_credential_exists_detects_existing_credential;



    -- %test(test_credential_exists_detects_misssing_credential)
   PROCEDURE test_credential_exists_detects_misssing_credential IS 

      test_credential VARCHAR2(30) := 'non-existing_credential'; 

   BEGIN
              
      ut.expect(host_request.credential_exists(test_credential),
         'test_credential_exists_detects_misssing_credential'
         ).to_be_false;

   END test_credential_exists_detects_misssing_credential;





   -- %test(test_run_host_command_creates_schedule_job)
   PROCEDURE test_run_host_command_creates_schedule_job IS 

      expected VARCHAR2(30) := 'Test_run_host_command'; 
      actual   VARCHAR2(30);
      job_name VARCHAR2(50); 

   BEGIN

      job_name := host_request.run_host_command('Test_run_host_command', test_credential, FALSE);

      SELECT job_action 
        INTO actual
        FROM user_scheduler_jobs
       WHERE job_action = expected;              

      ut.expect(actual,
         'test_run_host_command_creates_schedule_job'
         ).to_equal(expected);

      -- Clean up the job
      host_request.remove_job(job_name);

   END test_run_host_command_creates_schedule_job;



   -- %test(test_get_new_requests_gets_single_request)
   PROCEDURE test_get_new_requests_gets_single_request IS 

      expected VARCHAR2(100) := '%pi_test.sh%'; 
      actual   VARCHAR2(1000);

   BEGIN

      INSERT INTO pi_host_requests
      (script_name)
      VALUES ('pi_test.sh');
      
      actual := host_request.get_new_requests();
             
      ut.expect(actual,
         'test_get_new_requests_gets_single_request'
         ).to_be_like(expected);

   END test_get_new_requests_gets_single_request;



      -- %test(test_get_new_requests_gets_multiple_requests)
   PROCEDURE test_get_new_requests_gets_multiple_requests IS 

      expected VARCHAR2(100) := '%pi_test.sh%second_pi_test.sh%'; 
      actual   VARCHAR2(1000);

   BEGIN

      INSERT INTO pi_host_requests
      (script_name)
      VALUES ('pi_test.sh');

      INSERT INTO pi_host_requests
      (script_name)
      VALUES ('second_pi_test.sh');

      actual := host_request.get_new_requests();
             
      ut.expect(actual,
         'test_get_new_requests_gets_multiple_requests'
         ).to_be_like(expected);

   END test_get_new_requests_gets_multiple_requests;


   -- %test(test_get_new_requests_handles_no_new_requests)
   PROCEDURE test_get_new_requests_handles_no_new_requests IS 

      expected VARCHAR2(100) := '{"host_requests":[]}'; 
      actual   VARCHAR2(1000);

   BEGIN

      -- Remove any new requests
      UPDATE pi_host_requests
         SET status = 'HOLD'
       WHERE status = 'NEW';

      actual := host_request.get_new_requests();
             
      ut.expect(actual,
         'test_get_new_requests_handles_no_new_requests'
         ).to_be_like(expected);

   END test_get_new_requests_handles_no_new_requests;




   -- %test(test_create_request_creates_a_new_host_request)
   PROCEDURE test_create_request_creates_a_new_host_request IS 

      expected     VARCHAR2(100) := 'pi_dummy_test.sh'; 
      actual       VARCHAR2(1000);
      request_id   NUMBER;

   BEGIN
      request_id := 
         host_request.create_request(p_script => 'pi_dummy_test.sh', 
                                            p_parameters => '-v parm1 parm3');
      
      SELECT script_name
        INTO actual
        FROM pi_host_requests
       WHERE status = 'NEW'
         AND id = request_id;
             
      ut.expect(actual,
         'test_create_request_creates_a_new_host_request'
         ).to_equal(expected);

   END test_create_request_creates_a_new_host_request;



   -- %test(test_hold_new_requests_set_new_requests_on_hold)
   PROCEDURE test_hold_new_requests_set_new_requests_on_hold IS 

      expected     VARCHAR2(100) := 'pi_dummy_test.sh'; 
      actual       VARCHAR2(1000);
      request_id   NUMBER;

   BEGIN
      request_id := 
         host_request.create_request(p_script => 'pi_dummy_test.sh', 
                                            p_parameters => '-v parm1 parm3');
      
      host_request.hold_new_requests;

      SELECT script_name
        INTO actual
        FROM pi_host_requests
       WHERE status = 'HOLD'
         AND id = request_id;
             
      ut.expect(actual,
         'test_hold_new_requests_set_new_requests_on_hold'
         ).to_equal(expected);

   END test_hold_new_requests_set_new_requests_on_hold;



   -- %test(test_release_held_requests_set_status_back_to_new)
   PROCEDURE test_release_held_requests_set_status_back_to_new IS 

      expected     VARCHAR2(100) := 'pi_dummy_test.sh'; 
      actual       VARCHAR2(1000);
      request_id   NUMBER;

   BEGIN
      request_id := 
         host_request.create_request(p_script => 'pi_dummy_test.sh', 
                                            p_parameters => '-v parm1 parm3');
      
      host_request.hold_new_requests;

      host_request.release_held_requests;

      SELECT script_name
        INTO actual
        FROM pi_host_requests
       WHERE status = 'NEW'
         AND id = request_id;
             
      ut.expect(actual,
         'test_release_held_requests_set_status_back_to_new'
         ).to_equal(expected);

   END test_release_held_requests_set_status_back_to_new;



   -- %test(test_delete_request_removes_request)
   PROCEDURE test_delete_request_removes_request IS 

      expected     NUMBER := 0; 
      actual       NUMBER;
      request_id   NUMBER;

   BEGIN
      request_id := 
         host_request.create_request(p_script => 'pi_delete_test.sh', 
                                            p_parameters => '-v parm1 parm3');
      
      host_request.delete_request(p_id => request_id);

      SELECT COUNT(*)
        INTO actual
        FROM pi_host_requests
       WHERE script_name = 'pi_delete_test.sh';
             
      ut.expect(actual,
         'test_delete_request_removes_request'
         ).to_equal(expected);

   END test_delete_request_removes_request;


   -- %test(test_delete_request_removes_correct_request)
   PROCEDURE test_delete_request_removes_correct_request IS 

      expected      NUMBER := 1; 
      actual        NUMBER;
      request_id1   NUMBER;
      request_id2   NUMBER;

   BEGIN

      request_id1 := 
         host_request.create_request(p_script => 'pi_delete_test.sh', 
                                            p_parameters => '-v parm1 parm3');
      request_id2 := 
         host_request.create_request(p_script => 'pi_delete_test.sh', 
                                            p_parameters => '-v parm1 parm3');                                      
      -- Delete the oldest
      host_request.delete_request(p_id => request_id1);

      SELECT COUNT(*)
        INTO actual
        FROM pi_host_requests
       WHERE id = request_id2;
             
      ut.expect(actual,
         'test_delete_request_removes_correct_request'
         ).to_equal(expected);

   END test_delete_request_removes_correct_request;




   -- %test(test_get_request_id_finds_request)
   PROCEDURE test_get_request_id_finds_request IS 

      expected     NUMBER := 0; 
      actual       NUMBER;

   BEGIN
      expected := 
         host_request.create_request(p_script => 'pi_dummy_test.sh', 
                                            p_parameters => '-v parm1 parm3');
      
      actual := 
         host_request.find_request_id(p_script => 'pi_dummy_test.sh', 
                                             p_parameters => '-v parm1 parm3');
             
      ut.expect(actual,
         'test_get_request_id_finds_request'
         ).to_equal(expected);

   END test_get_request_id_finds_request;




   -- %test(test_mark_status_updates_request_status)
   PROCEDURE test_mark_status_updates_request_status IS 

      expected     VARCHAR2(20) := 'MODIFIED'; 
      actual       VARCHAR2(20);
      request_id   NUMBER;

   BEGIN
      request_id := 
         host_request.create_request(p_script => 'pi_dummy_test.sh', 
                                            p_parameters => '-v parm1 parm3');
      
      host_request.mark_status(p_id => request_id, 
                                      p_status => expected);

      SELECT status                                
        INTO actual
        FROM pi_host_requests
       WHERE id = request_id;
             
      ut.expect(actual,
         'test_mark_status_updates_request_status'
         ).to_equal(expected);

   END test_mark_status_updates_request_status;



   -- %test(test_get_status_retrieves_request_status)
   PROCEDURE test_get_status_retrieves_request_status IS 

      expected     VARCHAR2(20) := 'NEW'; 
      actual       VARCHAR2(20);
      request_id   NUMBER;

   BEGIN
      request_id := 
         host_request.create_request(p_script => 'pi_dummy_test.sh', 
                                            p_parameters => '-v parm1 parm3');
      
      actual := host_request.get_status(p_id => request_id);

      ut.expect(actual,
         'test_get_status_retrieves_request_status'
         ).to_equal(expected);

   END test_get_status_retrieves_request_status;



   -- %test(test_copy_staged_results_loads_execution_results)
   PROCEDURE test_copy_staged_results_loads_execution_results IS 

      expected     CLOB := TO_CLOB('This is the results of the script execution.'); 
      actual       CLOB;
      request_id   NUMBER;

   BEGIN
      request_id := 
         host_request.create_request(p_script => 'pi_dummy_test.sh', 
                                            p_parameters => '-v parm1 parm3');

      INSERT INTO pi_host_request_log_stg
      (request_id, log_file, log_contents)
      VALUES (request_id, 'pi_dummy_test.sh', expected);

      host_request.copy_staged_results(p_request_id => request_id);   

      SELECT execution_results
        INTO actual
        FROM pi_host_requests
       WHERE id = request_id;                                   

      ut.expect(actual,
         'test_copy_staged_results_loads_execution_results'
         ).to_equal(expected);

   END test_copy_staged_results_loads_execution_results;






END test_host_request;
/
  

SHOW ERRORS



SET SERVEROUTPUT ON SIZE 1000000

BEGIN
   ut.run('test_host_request');
END;
/

SPOOL OFF

-- Complete
