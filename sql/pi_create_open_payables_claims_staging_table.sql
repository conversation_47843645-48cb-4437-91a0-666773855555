------------------------------------------------------------------------------
--
--   PROCEDURE:      pi_create_open_payables_claims_staging_table.sql
--
--   PURPOSE:        This procedure is used to create the 
--                   PI_OPEN_PAYABLES_CLAIMS_STAGING table. 
--                   This table is used to "stage" the Claims worksheet data 
--                   for the generated Open Payables Excel data files.
-- 
--   PARAMETERS:     None. 
--
--   INSTRUCTIONS:   From within SQL*Plus (connected as APPS), type:
--                      START pi_create_open_payables_claims_staging_table.sql
--
--   AUTHOR:         Kevin LeQuire.
--
--   INSTALL SCHEMA: APPS
--
--
--   Copyright (c) 2025 Kevin LeQuire
--   All rights reserved.
--
--   This software is proprietary and confidential. Unauthorized copying of this
--   file, via any medium, is strictly prohibited without the express permission
--   of the author or owning organization.
--
------------------------------------------------------------------------------

WHENEVER OSERROR EXIT

SET ECHO ON


-- Open the log file
SPOOL pi_create_open_payables_claims_staging_table


-- Drop the current table 
DROP TABLE pi_open_payables_claims_staging;


CREATE TABLE pi_open_payables_claims_staging AS
SELECT col001 SourceRecordID, 
       col002 SeqNo, 
       col003 StateID, 
       col004 ProviderName, 
       col005 ProviderID, 
       col006 ProviderTIN, 
       col007 LOB, 
       col008 ProviderAccountID, 
       col009 ClaimNumber, 
       col010 ClaimPaidDate, 
       col011 ClaimPaidAmount, 
       col012 OpenPayableAmount, 
       col013 OpenPayableDate, 
       col014 OpenPayableAge, 
       col015 ShiftToAccount
  FROM pi_spreadsheetstaging
 WHERE file_name = 'SampleXL_Claims_Template.xlsx'
  -- AND line_number > 2 -- Line 1 is the internal column names. Restrict the data in the function.
ORDER BY line_number;



DESCRIBE pi_open_payables_claims_staging


-- Stop logging
SPOOL OFF 


-- Complete
