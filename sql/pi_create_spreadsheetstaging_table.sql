------------------------------------------------------------------------------
--
--   PROCEDURE:      pi_create_spreadsheetstaging_table.sql
--
--   PURPOSE:        This procedure is used to create the PI_SPREADSHEETSTAGING table. 
--                   This table is used to Excel data files.
-- 
--   PARAMETERS:     None. 
--
--   INSTRUCTIONS:   From within SQL*Plus (connected as APPS), type:
--                      START pi_create_spreadsheetstaging_table.sql
--
--   AUTHOR:         <PERSON>uire.
--
--   INSTALL SCHEMA: APPS
--
--
--   Copyright (c) 2025 Kevin LeQuire
--   All rights reserved.
--
--   This software is proprietary and confidential. Unauthorized copying of this
--   file, via any medium, is strictly prohibited without the express permission
--   of the author or owning organization.
--
------------------------------------------------------------------------------

WHENEVER OSERROR EXIT

SET ECHO ON


-- Open the log file
SPOOL pi_create_spreadsheetstaging_table


-- Drop the current table 
DROP TABLE pi_spreadsheetstaging;

-- DESCRIBE pi_spreadsheetstaging

/*
CREATE TABLE pi_spreadsheetstaging AS
SELECT xl.*
  FROM pi_data_files df,
       TABLE(apex_data_parser.parse(p_content => df.data_file, p_file_name => df.file_name)) xl
  WHERE df.file_name = 'LetterInfoUpdateTemplate.xlsx'
  ORDER BY line_number;
*/

-- Create the table
CREATE TABLE pi_spreadsheetstaging
(
   id             NUMBER GENERATED ALWAYS AS IDENTITY 
                  MINVALUE 1 MAXVALUE 9999999999999999999999999999 
                  INCREMENT BY 1 START WITH 1 NOT NULL ENABLE PRIMARY KEY,
   file_name      VARCHAR2(100) NOT NULL,
   line_number    NUMBER        NOT NULL,          
   col001         VARCHAR2(100), 
   col002         VARCHAR2(100), 
   col003         VARCHAR2(100), 
   col004         VARCHAR2(100), 
   col005         VARCHAR2(100), 
   col006         VARCHAR2(100), 
   col007         VARCHAR2(100), 
   col008         VARCHAR2(100), 
   col009         VARCHAR2(100), 
   col010         VARCHAR2(100), 
   col011         VARCHAR2(100), 
   col012         VARCHAR2(100), 
   col013         VARCHAR2(100), 
   col014         VARCHAR2(100), 
   col015         VARCHAR2(100), 
   col016         VARCHAR2(100), 
   col017         VARCHAR2(100), 
   col018         VARCHAR2(100), 
   col019         VARCHAR2(100), 
   col020         VARCHAR2(100), 
   col021         VARCHAR2(100), 
   col022         VARCHAR2(100), 
   col023         VARCHAR2(100), 
   col024         VARCHAR2(100), 
   col025         VARCHAR2(100), 
   col026         VARCHAR2(100), 
   col027         VARCHAR2(100), 
   col028         VARCHAR2(100), 
   col029         VARCHAR2(100), 
   col030         VARCHAR2(100), 
   col031         VARCHAR2(100), 
   col032         VARCHAR2(100), 
   col033         VARCHAR2(100), 
   col034         VARCHAR2(100), 
   col035         VARCHAR2(100), 
   col036         VARCHAR2(100), 
   col037         VARCHAR2(100), 
   col038         VARCHAR2(100), 
   col039         VARCHAR2(100), 
   col040         VARCHAR2(100), 
   col041         VARCHAR2(100), 
   col042         VARCHAR2(100), 
   col043         VARCHAR2(100), 
   col044         VARCHAR2(100), 
   col045         VARCHAR2(100), 
   col046         VARCHAR2(100), 
   col047         VARCHAR2(100), 
   col048         VARCHAR2(100), 
   col049         VARCHAR2(100), 
   col050         VARCHAR2(100), 
   col051         VARCHAR2(100), 
   col052         VARCHAR2(100), 
   col053         VARCHAR2(100), 
   col054         VARCHAR2(100), 
   col055         VARCHAR2(100), 
   col056         VARCHAR2(100), 
   col057         VARCHAR2(100), 
   col058         VARCHAR2(100), 
   col059         VARCHAR2(100), 
   col060         VARCHAR2(100), 
   col061         VARCHAR2(100), 
   col062         VARCHAR2(100), 
   col063         VARCHAR2(100), 
   col064         VARCHAR2(100), 
   col065         VARCHAR2(100), 
   col066         VARCHAR2(100), 
   col067         VARCHAR2(100), 
   col068         VARCHAR2(100), 
   col069         VARCHAR2(100), 
   col070         VARCHAR2(100), 
   col071         VARCHAR2(100), 
   col072         VARCHAR2(100), 
   col073         VARCHAR2(100), 
   col074         VARCHAR2(100), 
   col075         VARCHAR2(100), 
   col076         VARCHAR2(100), 
   col077         VARCHAR2(100), 
   col078         VARCHAR2(100), 
   col079         VARCHAR2(100), 
   col080         VARCHAR2(100), 
   col081         VARCHAR2(100), 
   col082         VARCHAR2(100), 
   col083         VARCHAR2(100), 
   col084         VARCHAR2(100), 
   col085         VARCHAR2(100), 
   col086         VARCHAR2(100), 
   col087         VARCHAR2(100), 
   col088         VARCHAR2(100), 
   col089         VARCHAR2(100), 
   col090         VARCHAR2(100), 
   col091         VARCHAR2(100), 
   col092         VARCHAR2(100), 
   col093         VARCHAR2(100), 
   col094         VARCHAR2(100), 
   col095         VARCHAR2(100), 
   col096         VARCHAR2(100), 
   col097         VARCHAR2(100), 
   col098         VARCHAR2(100), 
   col099         VARCHAR2(100), 
   col100         VARCHAR2(100),
   created_by     VARCHAR2(200) DEFAULT USER,
   created_date   DATE DEFAULT SYSDATE,
   modified_by    VARCHAR2(200) DEFAULT USER,
   modified_date  DATE DEFAULT SYSDATE,
   --
   CONSTRAINT pi_spreadsheetstaging_u1
      UNIQUE (file_name, line_number)
      USING INDEX
);


-- Stop logging
SPOOL OFF 


-- Complete
