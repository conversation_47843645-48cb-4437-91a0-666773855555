------------------------------------------------------------------------------
--
--   PROCEDURE:      pi_create_common_pkg.sql
--
--   PURPOSE:        This script is used to create the COMMON package.  
--                   This package is a number of utility functions and
--                   procedures used by the Rapid Pie interfaces. 
--
--   PARAMETERS:     None. 
--
--   INSTRUCTIONS:   From within SQL*Plus, type:
--                      START pi_create_common_pkg.sql
--
--   AUTHOR:         <PERSON>.
--
--   INSTALL SCHEMA: APPS
--
--
--   Copyright (c) 2025 <PERSON>
--   All rights reserved.
--
--   This software is proprietary and confidential. Unauthorized copying of this
--   file, via any medium, is strictly prohibited without the express permission
--   of the author or owning organization.
--
------------------------------------------------------------------------------

SET ECHO ON


SPOOL pi_create_common_pkg

create or replace PACKAGE common AUTHID CURRENT_USER AS

   -- Declare the record types
   TYPE email_rec IS REF CURSOR RETURN pi_interface_email_addresses%ROWTYPE;

   -- Get the email addresses for the given interface. 
   -- The returned string will be of the format: 
   -- address1,address2, ...
   FUNCTION get_email_addresses(p_interface IN VARCHAR2) 
      RETURN VARCHAR2;

   FUNCTION get_email_addresses(p_interface IN VARCHAR2, p_address_type IN VARCHAR2) 
      RETURN VARCHAR2;



   -- This procedure returns the list of new public key requests
   PROCEDURE get_new_public_key_requests (public_key_list OUT VARCHAR2);

   -- Without parameter (for shell execution)
   PROCEDURE get_new_public_key_requests;


   -- This procedure is used to update the processing results for the public key
   -- common.update_public_key($key_id, 'PROCESSED', '$key_fingerprint', '$key_expire_date', '$key_file')
   PROCEDURE update_public_key(p_key_id IN NUMBER, 
                               p_status IN VARCHAR2, 
                               p_fingerprint IN VARCHAR2 DEFAULT NULL, 
                               p_expire_date IN VARCHAR2 DEFAULT NULL, 
                               p_key_file IN VARCHAR2 DEFAULT NULL);

END common;
/   
SHOW ERRORS


CREATE OR REPLACE PACKAGE BODY common AS

    
   FUNCTION get_email_addresses(p_interface IN VARCHAR2) 
      RETURN VARCHAR2 IS

      return_list   VARCHAR2(1000) := 'X';

   BEGIN
      return_list := get_email_addresses(p_interface, 'ALL');

      RETURN return_list;

   END get_email_addresses ;



   FUNCTION get_email_addresses(p_interface IN VARCHAR2, p_address_type IN VARCHAR2) 
      RETURN VARCHAR2 IS

      return_list   VARCHAR2(1000) := 'X';
      email_rec     pi_interface_email_addresses%ROWTYPE;

      CURSOR address_cursor (p_interface VARCHAR2, p_address_type VARCHAR2) IS
         SELECT * --interface, email_address, address_type, enabled
           FROM pi_interface_email_addresses
          WHERE UPPER(interface) = UPPER(p_interface)
            AND enabled = 'Y'
            AND address_type = 
                DECODE(UPPER(p_address_type), 'ALL', address_type, UPPER(p_address_type))
          ORDER BY interface, 
                DECODE(UPPER(address_type), 'TO', 1, 'CC', 2, 'BCC', 3, 4),
                email_address;

   BEGIN
      OPEN address_cursor (p_interface, p_address_type);
      LOOP

         FETCH address_cursor INTO email_rec;
         EXIT WHEN address_cursor%NOTFOUND;

         IF return_list = 'X' THEN
            return_list := email_rec.email_address; 
         ELSE
            return_list := return_list || ',' || email_rec.email_address;
         END IF;


      END LOOP;

      RETURN return_list;

   END get_email_addresses;




   -- This procedure returns the list of new public key requests
   PROCEDURE get_new_public_key_requests (public_key_list OUT VARCHAR2) IS

      public_key_rec   pi_public_keys%ROWTYPE;

      CURSOR key_cursor IS
         SELECT * 
           FROM pi_public_keys
          WHERE status = 'NEW'
          ORDER BY key_id;

   BEGIN
      OPEN key_cursor;
      LOOP

         FETCH key_cursor INTO public_key_rec;
         EXIT WHEN key_cursor%NOTFOUND;

         dbms_output.put_line(public_key_rec.key_id  || ',' || 
               public_key_rec.key_name  || ',' || 
               public_key_rec.key_email  || ',' || 
               public_key_rec.key_comment  || ',' || 
               public_key_rec.expiration  || ',' || 
               public_key_rec.status);

         public_key_list := public_key_list ||
            public_key_rec.key_id  || ',' || 
            public_key_rec.key_name  || ',' || 
            public_key_rec.key_email  || ',' || 
            public_key_rec.key_comment  || ',' || 
            public_key_rec.expiration  || ',' || 
            public_key_rec.status || ';';

      END LOOP;

   END get_new_public_key_requests;



   -- Without parameter (for shell execution)
   PROCEDURE get_new_public_key_requests IS 
      key_list   VARCHAR2(1000);
   BEGIN

      get_new_public_key_requests(key_list);

   END get_new_public_key_requests;


   -- This procedure is used to update the processing results for the public key
   -- common.update_public_key($key_id, 'PROCESSED', '$key_fingerprint', '$key_expire_date', '$key_file')
   PROCEDURE update_public_key(p_key_id IN NUMBER, 
                               p_status IN VARCHAR2, 
                               p_fingerprint IN VARCHAR2 DEFAULT NULL, 
                               p_expire_date IN VARCHAR2 DEFAULT NULL, 
                               p_key_file IN VARCHAR2 DEFAULT NULL) IS 

   BEGIN

      UPDATE pi_public_keys
         SET status = p_status,
             fingerprint = p_fingerprint,
             expires = TO_DATE(p_expire_date, 'YYYY-MM-DD'),
             key_file = p_key_file
       WHERE key_id = p_key_id;

   END update_public_key;



END common;
/   
SHOW ERRORS

SPOOL OFF

-- Complete
