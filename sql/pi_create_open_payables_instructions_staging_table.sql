------------------------------------------------------------------------------
--
--   PROCEDURE:      pi_create_open_payables_instructions_staging_table.sql
--
--   PURPOSE:        This procedure is used to create the 
--                   PI_OPEN_PAYABLES_INSTRUCTIONS_STAGING table. 
--                   This table is used to "stage" the Instructions worksheet data 
--                   for the generated Open Payables Excel data file.
-- 
--   PARAMETERS:     None. 
--
--   INSTRUCTIONS:   From within SQL*Plus (connected as APPS), type:
--                      START pi_create_open_payables_instructions_staging_table.sql
--
--   AUTHOR:         Kevin <PERSON>uire.
--
--   INSTALL SCHEMA: APPS
--
--
--   Copyright (c) 2025 Kevin LeQuire
--   All rights reserved.
--
--   This software is proprietary and confidential. Unauthorized copying of this
--   file, via any medium, is strictly prohibited without the express permission
--   of the author or owning organization.
--
------------------------------------------------------------------------------

WHENEVER OSERROR EXIT

SET ECHO ON


-- Open the log file
SPOOL pi_create_open_payables_instructions_staging_table


-- Drop the current table 
DROP TABLE pi_open_payables_instructions_staging;


CREATE TABLE pi_open_payables_instructions_staging AS
SELECT rownum record_no, col001 instruction
  FROM pi_spreadsheetstaging
 WHERE file_name = 'SampleXL_Instructions_Template.xlsx'
ORDER BY line_number;




DESCRIBE pi_open_payables_instructions_staging


-- Stop logging
SPOOL OFF 


-- Complete
