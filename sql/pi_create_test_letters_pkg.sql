------------------------------------------------------------------------------
--
--   PROCEDURE:      pi_create_test_letters_pkg.sql
--
--   PURPOSE:        This script is used to create the TEST_LETTERS package. 
--
--   PARAMETERS:     None. 
--
--   INSTRUCTIONS:   From within SQL*Plus, type:
--                      START pi_create_test_letters_pkg.sql
--
--   AUTHOR:         <PERSON>.
--
--   INSTALL SCHEMA: APPS
--
--
--   Copyright (c) 2025 <PERSON>
--   All rights reserved.
--
--   This software is proprietary and confidential. Unauthorized copying of this
--   file, via any medium, is strictly prohibited without the express permission
--   of the author or owning organization.
--
------------------------------------------------------------------------------

SET ECHO ON
SET DEFINE ~


SPOOL pi_create_test_letters_pkg

CREATE OR REPLACE PACKAGE test_letters AUTHID CURRENT_USER AS

   --%suite(LETTERS Unit Test Package (pi_create_test_letters_pkg.sql))
   
   -- This procedure copies the TEST_LETTER.pdf BLOB to the given
   -- test filename in the AOP_FILE_DIR.
   PROCEDURE create_test_pdf_file (test_filename IN VARCHAR2);

   -- This procedure copies the test_file.xlsx BLOB to the given
   -- test filename in the RPI_INBOUND directory.
   PROCEDURE create_test_excel_file (test_filename IN VARCHAR2);


   -- %beforeall
   PROCEDURE create_test_letter_data;
   

   --%context(-- get_aop_letter_data_source Unit Tests)
   
   -- %test(test_get_aop_letter_data_source_uses_letter_parameter)
   PROCEDURE test_get_aop_letter_data_source_uses_letter_parameter;

   -- %test(test_get_aop_letter_data_source_uses_second_parameter)
   PROCEDURE test_get_aop_letter_data_source_uses_second_parameter;
  --%endcontext



   --%context(-- generate_letter_name Unit Tests)
   
   -- %test(test_generate_file_name_gets_provider_letter_file_name)
   PROCEDURE test_generate_file_name_gets_provider_letter_file_name;

   -- %test(test_generate_file_name_returns_null_if_letter_not_found)
   PROCEDURE test_generate_file_name_returns_null_if_letter_not_found;

   --%endcontext


   
   --%context(-- get_postgrid_to_contact Unit Tests)

   -- %test(test_get_postgrid_to_contact_returns_attention)
   PROCEDURE test_get_postgrid_to_contact_returns_attention;

   -- %test(test_get_postgrid_to_contact_returns_refund_unit_if_attention_null)
   PROCEDURE test_get_postgrid_to_contact_returns_refund_unit_if_attention_null;

   -- %test(test_get_postgrid_to_contact_returns_company_name)
   PROCEDURE test_get_postgrid_to_contact_returns_company_name;

   -- %test(test_get_postgrid_to_contact_returns_address_line1)
   PROCEDURE test_get_postgrid_to_contact_returns_address_line1;

   -- %test(test_get_postgrid_to_contact_returns_address_line2)
   PROCEDURE test_get_postgrid_to_contact_returns_address_line2;

   -- %test(test_get_postgrid_to_contact_skips_address_line2_if_null)
   PROCEDURE test_get_postgrid_to_contact_skips_address_line2_if_null;

   -- %test(test_get_postgrid_to_contact_returns_city)
   PROCEDURE test_get_postgrid_to_contact_returns_city;

   -- %test(test_get_postgrid_to_contact_returns_state)
   PROCEDURE test_get_postgrid_to_contact_returns_state;

   -- %test(test_get_postgrid_to_contact_returns_zip_code)
   PROCEDURE test_get_postgrid_to_contact_returns_zip_code;

   -- %test(test_get_postgrid_to_contact_returns_country_code)
   PROCEDURE test_get_postgrid_to_contact_returns_country_code;

   --%endcontext



   --%context(-- get_postgrid_from_contact Unit Tests)
   
   -- %test(test_get_postgrid_from_contact_returns_ahp_info)
   PROCEDURE test_get_postgrid_from_contact_returns_ahp_info;

   -- %test(test_get_postgrid_from_contact_returns_blank_if_provider_not_found)
   PROCEDURE test_get_postgrid_from_contact_returns_blank_if_provider_not_found;

   --%endcontext


   --%context(-- get_postgrid_api_key Unit Tests)
   
   -- %test(test_get_postgrid_api_key_gets_test_key)
   PROCEDURE test_get_postgrid_api_key_gets_test_key;

   -- %test(test_get_postgrid_api_key_gets_production_key)
   PROCEDURE test_get_postgrid_api_key_gets_production_key;

   --%endcontext



   --%context(-- mark_letter_sent Unit Tests)
   
   -- %test(test_mark_letter_sent_sets_status_to_sent)
   PROCEDURE test_mark_letter_sent_sets_status_to_sent;

   -- %test(test_mark_letter_sent_saves_postgrid_letter_id)
   PROCEDURE test_mark_letter_sent_saves_postgrid_letter_id;

   --%endcontext


   --%context(-- get_letter_number_from_filename Unit Tests)
   
   -- %test(test_get_letter_number_from_filename_gets_letter_number)
   PROCEDURE test_get_letter_number_from_filename_gets_letter_number;

   -- %test(test_get_letter_number_from_filename_returns_null_if_letter_not_found)
   PROCEDURE test_get_letter_number_from_filename_returns_null_if_letter_not_found;
   
   --%endcontext


   --%context(-- get_postgrid_contact Unit Tests)
   
   -- %test(test_get_postgrid_contact_retrieves_contact)
   PROCEDURE test_get_postgrid_contact_retrieves_contact;

   -- %test(test_get_postgrid_contact_returns_not_found_for_nonexisting_contact)
   PROCEDURE test_get_postgrid_contact_returns_not_found_for_nonexisting_contact;

   -- %test(test_get_postgrid_contact_defaults_to_test_environment)
   PROCEDURE test_get_postgrid_contact_defaults_to_test_environment;

   -- %test(test_get_postgrid_contact_runs_in_production_environment)
   PROCEDURE test_get_postgrid_contact_runs_in_production_environment;

   --%endcontext
   
   
   --%context(-- create_postgrid_contact Unit Tests)
   
   -- %test(test_create_postgrid_contact_creates_contact)
   PROCEDURE test_create_postgrid_contact_creates_contact;

   --%endcontext


   --%context(-- create_postgrid_client_contact Unit Tests)
   
   -- %test(test_create_postgrid_client_contact_creates_contact)
   PROCEDURE test_create_postgrid_client_contact_creates_contact;

   --%endcontext


   --%context(-- aop_file_exists Unit Tests)
   
   -- %test(test_aop_file_exists_finds_letter_file)
   PROCEDURE test_aop_file_exists_finds_letter_file;

   -- %test(test_aop_file_exists_returns_not_found)
   PROCEDURE test_aop_file_exists_returns_not_found;

   --%endcontext



   --%context(-- send_letter_to_postgrid Unit Tests)
   
   -- %test(test_send_letter_to_postgrid_checks_for_pdf_letter)
   PROCEDURE test_send_letter_to_postgrid_checks_for_pdf_letter;

   -- %test(test_send_letter_to_postgrid_sends_the_letter)
   PROCEDURE test_send_letter_to_postgrid_sends_the_letter;

   --%endcontext



   --%context(-- load_aop_file_to_blob Unit Tests)
   
   -- %test(test_load_aop_file_to_blob_loads_aop_file)
   PROCEDURE test_load_aop_file_to_blob_loads_aop_file;

   --%endcontext



   --%context(-- load_aop_file_to_table Unit Tests)
   
   -- %test(test_load_aop_file_to_table_loads_pdf_file)
   PROCEDURE test_load_aop_file_to_table_loads_pdf_file;

   --%endcontext



   --%context(-- log_email Unit Tests)
   
   -- %test(test_log_email_saves_email)
   PROCEDURE test_log_email_saves_email;

   -- %test(test_log_email_translates_given_date)
   PROCEDURE test_log_email_translates_given_date;

   --%endcontext
   



   --%context(-- load_scanned_file Unit Tests)

   -- %test(test_load_scanned_file_loads_pdf_filename)
   PROCEDURE test_load_scanned_file_loads_pdf_filename;

   --%endcontext


   --%context(-- get_scanned_document_id Unit Tests)

   -- %test(test_get_scanned_document_id_gets_last_matching_id)
   PROCEDURE test_get_scanned_document_id_gets_last_matching_id;

   -- %test(test_get_scanned_document_id_gets_id_for_one_match)
   PROCEDURE test_get_scanned_document_id_gets_id_for_one_match;

   -- %test(test_get_scanned_document_id_returns_zero_if_no_match)
   PROCEDURE test_get_scanned_document_id_returns_zero_if_no_match;

   --%endcontext


   --%context(-- load_scanned_line Unit Tests)

   -- %test(test_load_scanned_line_loads_text_line)
   PROCEDURE test_load_scanned_line_loads_text_line;

   --%endcontext



   --%context(-- load_correspondence_document Unit Tests)

   -- %test(test_load_correspondence_document_loads_file_attributes)
   PROCEDURE test_load_correspondence_document_loads_file_attributes;

   -- %test(test_load_correspondence_document_loads_document_comments)
   PROCEDURE test_load_correspondence_document_loads_document_comments;

   --%endcontext



   --%context(-- get_correspondence_document_id Unit Tests)

   -- %test(test_get_correspondence_document_id_gets_last_matching_id)
   PROCEDURE test_get_correspondence_document_id_gets_last_matching_id;

   -- %test(test_get_correspondence_document_id_gets_id_for_one_match)
   PROCEDURE test_get_correspondence_document_id_gets_id_for_one_match;

   -- %test(test_get_correspondence_document_id_returns_zero_if_no_match)
   PROCEDURE test_get_correspondence_document_id_returns_zero_if_no_match;

   --%endcontext



   --%context(-- load_correspondence_document_line Unit Tests)

   -- %test(test_load_correspondence_document_line_loads_text_line)
   PROCEDURE test_load_correspondence_document_line_loads_text_line;

   -- %test(test_load_correspondence_document_line_defaults_page_number)
   PROCEDURE test_load_correspondence_document_line_defaults_page_number;

   -- %test(test_load_correspondence_document_line_defaults_page_line_to_line_number)
   PROCEDURE test_load_correspondence_document_line_defaults_page_line_to_line_number;

   --%endcontext



   --%context(-- load_data_file Unit Tests)

   -- %test(test_load_data_file_loads_file_name)
   PROCEDURE test_load_data_file_loads_file_name;

   -- %test(test_load_data_file_loads_file_type)
   PROCEDURE test_load_data_file_loads_file_type;

    --%endcontext



   --%context(-- replace_in_blob Unit Tests)

   -- %test(test_replace_in_blob_replaces_text_in_blob)
   PROCEDURE test_replace_in_blob_replaces_text_in_blob;

   -- %test(test_replace_in_blob_returns_same_blob_if_no_match)
   PROCEDURE test_replace_in_blob_returns_same_blob_if_no_match;

   -- %test(test_replace_in_blob_replaces_multiple_matches)
   PROCEDURE test_replace_in_blob_replaces_multiple_matches;

   -- %test(test_replace_in_blob_replaces_id_pdf)
   -- Does not work with the current implementation of replace_in_blob
   -- PROCEDURE test_replace_in_blob_replaces_id_pdf;

    --%endcontext



   --%context(--  read_blob_from_file Unit Tests)

   -- %test(test_read_blob_from_file_reads_file)
   PROCEDURE test_read_blob_from_file_reads_file;

   --%endcontext



   --%context(-- write_blob_to_file Unit Tests)

   -- %test(test_write_blob_to_file_creates_pdf_file)
   PROCEDURE test_write_blob_to_file_creates_pdf_file;

   -- %test(test_write_blob_to_file_creates_excel_file)
   PROCEDURE test_write_blob_to_file_creates_excel_file;

   --%endcontext



   --%context(-- export_data_to_excel_file Unit Tests)

   -- %test(test_export_data_to_excel_file_creates_file)
   PROCEDURE test_export_data_to_excel_file_creates_file;
   
   --%endcontext



   --%context(-- export_tracker_file Unit Tests)

   -- %test(test_export_tracker_file_creates_file)
   PROCEDURE test_export_tracker_file_creates_file;

   -- %test(test_export_tracker_file_creates_file_specified_directory)
   PROCEDURE test_export_tracker_file_creates_file_specified_directory;
   
    --%endcontext



   --%context(-- export_symkey_files Unit Tests)

   -- %test(test_export_symkey_files_creates_multiple_files)
   PROCEDURE test_export_symkey_files_creates_multiple_files;
   
    --%endcontext



   --%context(-- export_claim_letter_files Unit Tests)

   -- %test(test_export_claim_letter_files_uploads_file)
   PROCEDURE test_export_claim_letter_files_uploads_file;
   
   --%endcontext



   --%context(-- get_provider_letter_cursor Unit Tests)

   -- %test(test_get_provider_letter_cursor_creates_cursor)
   PROCEDURE test_get_provider_letter_cursor_creates_cursor;

   --%endcontext



   --%context(-- get_provider_letter_details_cursor Unit Tests)

   -- %test(test_get_provider_letter_details_cursor_creates_cursor)
   PROCEDURE test_get_provider_letter_details_cursor_creates_cursor;

   --%endcontext



   --%context(-- get_provider_short_name Unit Tests)

   -- %test(test_get_provider_short_name_returns_10_characters_without_spaces)
   PROCEDURE test_get_provider_short_name_returns_10_characters_without_spaces;


   -- %test(test_get_provider_short_name_removes_special_characters)
   PROCEDURE test_get_provider_short_name_removes_special_characters;
   
   --%endcontext



   --%context(-- get_claim_letter_cursor Unit Tests)

   -- %test(test_get_claim_letter_cursor_creates_cursor)
   PROCEDURE test_get_claim_letter_cursor_creates_cursor;

   --%endcontext



   --%context(-- get_claim_letter_details_cursor Unit Tests)

   -- %test(test_get_claim_letter_details_cursor_creates_cursor)
   PROCEDURE test_get_claim_letter_details_cursor_creates_cursor;
   
   --%endcontext



END test_letters;
/   
SHOW ERRORS




CREATE OR REPLACE PACKAGE BODY test_letters AS

   -- This procedure copies the TEST_LETTER.pdf BLOB to the given
   -- test filename in the AOP_FILE_DIR.
   PROCEDURE create_test_pdf_file (test_filename IN VARCHAR2) IS 

      test_file     UTL_FILE.FILE_TYPE; 
      test_blob     BLOB := EMPTY_BLOB();
      blob_length   INTEGER;
      blob_buffer   RAW(32767);
      block_amount  BINARY_INTEGER := 32767;
      read_position INTEGER := 1;

   BEGIN
      /* Test PDF file (TEST_LETTER.pdf) loaded into PI_AOP_FILES for unit tests
      CREATE TABLE pi_aop_files 
      (
         id         NUMBER,
         filename   VARCHAR2(100),
         blob_data  BLOB
      ); 
      -- INSERT a Letter PDF file into the table and rename it to 'TEST_LETTER.pdf'
      -- (The file need to be in AOP_FILE_DIR)
      EXECUTE letters.load_aop_file_to_table('AZHMO_474132507_HCHTUCSONH_2025033000181.pdf');

      UPDATE pi_aop_files
         SET filename = 'TEST_LETTER.pdf'
       WHERE filename = 'AZHMO_474132507_HCHTUCSONH_2025033000181.pdf'; 
       
      */
      SELECT blob_data 
        INTO test_blob
        FROM pi_aop_files 
       WHERE filename = 'TEST_LETTER.pdf';

      test_file := utl_file.fopen('AOP_FILE_DIR', test_filename, 'wb', block_amount);
      blob_length := dbms_lob.getlength(test_blob);

      -- Read chunks of the BLOB and write them to the file until complete.
      WHILE read_position <= blob_length LOOP
         DBMS_LOB.read(test_blob, block_amount, read_position, blob_buffer);
         utl_file.put_raw(test_file, blob_buffer, TRUE);
         read_position := read_position + block_amount;
      END LOOP;

      utl_file.fclose(test_file);

   END create_test_pdf_file;




   -- This procedure copies the test_file.xlsx BLOB to the given
   -- test filename in the RPI_INBOUND directory.
   PROCEDURE create_test_excel_file (test_filename IN VARCHAR2) IS 

      test_file     UTL_FILE.FILE_TYPE; 
      test_blob     BLOB := EMPTY_BLOB();
      blob_length   INTEGER;
      blob_buffer   RAW(32767);
      block_amount  BINARY_INTEGER := 32767;
      read_position INTEGER := 1;

   BEGIN
      /* Test Set-up: 
      Copy the test Excel file (test_file.xlsx) from the rapidpie/test
      subdirectory to the RPI_INBOUND (/opt/inbound) directory.

         cp test/test_file.xlsx /opt/inbound/

      Run the load_data_file procedure to load the file into the PI_DATA_FILES table.

         EXECUTE letters.load_data_file('test_file.xlsx');

      Check it:
         SELECT *
           FROM pi_data_files
          WHERE file_name = 'test_file.xlsx';   
      */
      SELECT data_file 
        INTO test_blob  
        FROM pi_data_files 
       WHERE file_name = 'test_file.xlsx';

      test_file := utl_file.fopen('RPI_INBOUND', test_filename, 'wb', block_amount);
      blob_length := dbms_lob.getlength(test_blob);

      -- Read chunks of the BLOB and write them to the file until complete.
      WHILE read_position <= blob_length LOOP
         DBMS_LOB.read(test_blob, block_amount, read_position, blob_buffer);
         utl_file.put_raw(test_file, blob_buffer, TRUE);
         read_position := read_position + block_amount;
      END LOOP;

      utl_file.fclose(test_file);

   END create_test_excel_file;



   PROCEDURE create_test_letter_data IS
   BEGIN

      -- Populated Attention; Null address line 2
      INSERT INTO ntf_notificationdeliveries
      (notificationbatchid, notificationrefnumber, sourcesystemcode, letterdate, 
       providertin, providerattention, providergroupname, provideraddressline1, 
       provideraddressline2, providercity, providerstatecode, providerzip, transmitfilename)
      VALUES
      (-99, 'Test_Letter_1', 'AHS', TO_DATE('06/30/24', 'MM/DD/RR'), '123456789', 
       'PROCESS UNIT', 'ACME ANVIL CO', '123 ROAD RUNNER RD', NULL, 'TOMBSTONE',
       'AZ', '**********', 'ACME_123456789_ACMEANVILC_20241028');

      -- Null Attention; populated address line 2
      INSERT INTO ntf_notificationdeliveries
      (notificationbatchid, notificationrefnumber, sourcesystemcode, letterdate, 
       providertin, providerattention, providergroupname, provideraddressline1, 
       provideraddressline2, providercity, providerstatecode, providerzip, transmitfilename)
      VALUES
      (-99, 'Test_Letter_2', 'AHS', TO_DATE('06/30/24', 'MM/DD/RR'), '987654321', 
       NULL, 'CORPORATE AMERICA', '1776 WASHINGTON ST', 'SUITE 102', 'PARUMPH',
       'NV', '**********', 'CORP_987654321_CORPAMERIC_20241028');

   END create_test_letter_data;


   

   -- %test(test_get_aop_letter_data_source_uses_letter_parameter)
   PROCEDURE test_get_aop_letter_data_source_uses_letter_parameter IS 
      expected      VARCHAR2(100) := '%AHS-Align-1%';
      actual        CLOB;
   BEGIN
      
      SELECT letters.get_aop_letter_data_source('AHS-Align-1')
        INTO actual
        FROM dual;

      ut.expect(TO_CHAR(actual),
         'test_get_aop_letter_data_source_uses_letter_parameter'
         ).to_be_like(expected);

   END test_get_aop_letter_data_source_uses_letter_parameter;



   -- %test(test_get_aop_letter_data_source_uses_second_parameter)
   PROCEDURE test_get_aop_letter_data_source_uses_second_parameter IS 
      expected      VARCHAR2(100) := '%-99%';
      actual        CLOB;
   BEGIN
      
      SELECT letters.get_aop_letter_data_source('AHS-Align-2', -99)
        INTO actual
        FROM dual;

      ut.expect(TO_CHAR(actual),
         'test_get_aop_letter_data_source_uses_second_parameter'
         ).to_be_like(expected);

   END test_get_aop_letter_data_source_uses_second_parameter;


   -- %test(test_generate_file_name_gets_provider_letter_file_name)
   PROCEDURE test_generate_file_name_gets_provider_letter_file_name IS 
      expected      VARCHAR2(1000);
      actual        VARCHAR2(1000);
   BEGIN
      
      expected := 'ACME_123456789_ACMEANVILC_20241028';
     
      actual := letters.generate_file_name('Test_Letter_1');

      ut.expect(actual,
         'test_generate_file_name_gets_provider_letter_file_name'
         ).to_equal(expected);

   END test_generate_file_name_gets_provider_letter_file_name;



   -- %test(test_generate_file_name_returns_null_if_letter_not_found)
   PROCEDURE test_generate_file_name_returns_null_if_letter_not_found IS 
      expected      VARCHAR2(1000);
      actual        VARCHAR2(1000);
   BEGIN
      
      expected := '';
     
      actual := letters.generate_file_name('non-existing_letter');

      ut.expect(actual,
         'test_generate_file_name_returns_null_if_letter_not_found'
         ).to_equal(expected);

   END test_generate_file_name_returns_null_if_letter_not_found;



   -- %test(test_get_postgrid_to_contact_returns_attention)
   PROCEDURE test_get_postgrid_to_contact_returns_attention IS 
      expected      VARCHAR2(1000);
      actual        VARCHAR2(1000);
   BEGIN
      expected := '--data-urlencode firstName=''PROCESS UNIT''%';
      actual := letters.get_postgrid_to_contact('Test_Letter_1');

      ut.expect(actual,
         'test_get_postgrid_to_contact_returns_attention'
         ).to_be_like(expected);

   END test_get_postgrid_to_contact_returns_attention;



   -- %test(test_get_postgrid_to_contact_returns_refund_unit_if_attention_null)
   PROCEDURE test_get_postgrid_to_contact_returns_refund_unit_if_attention_null IS 
      expected      VARCHAR2(1000) := '%REFUND UNIT%';
      actual        VARCHAR2(1000);
   BEGIN

      actual := letters.get_postgrid_to_contact('Test_Letter_2');

      ut.expect(actual,
         'test_get_postgrid_to_contact_returns_refund_unit_if_attention_null'
         ).to_be_like(expected);

   END test_get_postgrid_to_contact_returns_refund_unit_if_attention_null;



   -- %test(test_get_postgrid_to_contact_returns_company_name)
   PROCEDURE test_get_postgrid_to_contact_returns_company_name IS 
      expected      VARCHAR2(1000) := '%companyName=''ACME ANVIL CO%';
      actual        VARCHAR2(1000);
   BEGIN
      
      actual := letters.get_postgrid_to_contact('Test_Letter_1');

      ut.expect(actual,
         'test_get_postgrid_to_contact_returns_company_name'
         ).to_be_like(expected);

   END test_get_postgrid_to_contact_returns_company_name;



   -- %test(test_get_postgrid_to_contact_returns_address_line1)
   PROCEDURE test_get_postgrid_to_contact_returns_address_line1 IS 
      expected      VARCHAR2(1000) := '%addressLine1=''123 ROAD RUNNER RD%';
      actual        VARCHAR2(1000);
   BEGIN
      
      actual := letters.get_postgrid_to_contact('Test_Letter_1');

      ut.expect(actual,
         'test_get_postgrid_to_contact_returns_address_line1'
         ).to_be_like(expected);

   END test_get_postgrid_to_contact_returns_address_line1;



   -- %test(test_get_postgrid_to_contact_returns_address_line2)
   PROCEDURE test_get_postgrid_to_contact_returns_address_line2 IS 
      expected      VARCHAR2(1000) := '%addressLine2=''SUITE 102%';
      actual        VARCHAR2(1000);
   BEGIN
      
      actual := letters.get_postgrid_to_contact('Test_Letter_2');

      ut.expect(actual,
         'test_get_postgrid_to_contact_returns_address_line2'
         ).to_be_like(expected);

   END test_get_postgrid_to_contact_returns_address_line2;


   -- %test(test_get_postgrid_to_contact_skips_address_line2_if_null)
   PROCEDURE test_get_postgrid_to_contact_skips_address_line2_if_null IS 
      expected      VARCHAR2(1000) := '%addressLine2%';
      actual        VARCHAR2(1000);
   BEGIN
      
      actual := letters.get_postgrid_to_contact('Test_Letter_1');

      ut.expect(actual,
         'test_get_postgrid_to_contact_skips_address_line2_if_null'
         ).not_to_be_like(expected);

   END test_get_postgrid_to_contact_skips_address_line2_if_null;


   -- %test(test_get_postgrid_to_contact_returns_city)
   PROCEDURE test_get_postgrid_to_contact_returns_city IS 
      expected      VARCHAR2(1000) := '%city=''TOMBSTONE%';
      actual        VARCHAR2(1000);
   BEGIN
      
      actual := letters.get_postgrid_to_contact('Test_Letter_1');

      ut.expect(actual,
         'test_get_postgrid_to_contact_returns_city'
         ).to_be_like(expected);

   END test_get_postgrid_to_contact_returns_city;



   -- %test(test_get_postgrid_to_contact_returns_state)
   PROCEDURE test_get_postgrid_to_contact_returns_state IS 
      expected      VARCHAR2(1000) := '%provinceOrState=''AZ%';
      actual        VARCHAR2(1000);
   BEGIN
      
      actual := letters.get_postgrid_to_contact('Test_Letter_1');

      ut.expect(actual,
         'test_get_postgrid_to_contact_returns_state'
         ).to_be_like(expected);

   END test_get_postgrid_to_contact_returns_state;


   -- %test(test_get_postgrid_to_contact_returns_zip_code)
   PROCEDURE test_get_postgrid_to_contact_returns_zip_code IS 
      expected      VARCHAR2(1000) := '%postalOrZip=''**********%';
      actual        VARCHAR2(1000);
   BEGIN
      
      actual := letters.get_postgrid_to_contact('Test_Letter_1');

      ut.expect(actual,
         'test_get_postgrid_to_contact_returns_zip_code'
         ).to_be_like(expected);

   END test_get_postgrid_to_contact_returns_zip_code;


   -- %test(test_get_postgrid_to_contact_returns_country_code)
   PROCEDURE test_get_postgrid_to_contact_returns_country_code IS 
      expected      VARCHAR2(1000) := '%countryCode=US%';
      actual        VARCHAR2(1000);
   BEGIN
      
      actual := letters.get_postgrid_to_contact('Test_Letter_1');

      ut.expect(actual,
         'test_get_postgrid_to_contact_returns_country_code'
         ).to_be_like(expected);

   END test_get_postgrid_to_contact_returns_country_code;




   -- %test(test_get_postgrid_from_contact_returns_ahp_info)
   PROCEDURE test_get_postgrid_from_contact_returns_ahp_info IS 
      expected      VARCHAR2(1000);
      actual        VARCHAR2(1000);
   BEGIN
      
      expected := '--data-urlencode ''firstName=Claims Recovery Department'' ' ||
         '--data-urlencode ''companyName=Alignment Health Plan'' ' ||
         '--data-urlencode ''addressLine1=P.O. Box 14010'' ' ||
         '--data-urlencode ''city=Orange'' ' ||
         '--data-urlencode ''provinceOrState=CA'' ' ||
         '--data-urlencode ''postalOrZip=92863'' ' ||
         '--data-urlencode ''countryCode=US'' ' ||
         '--data-urlencode ''phoneNumber=(*************'' ';
     
      actual := letters.get_postgrid_from_contact('Alignment Health Plan');

      ut.expect(actual,
         'test_get_postgrid_from_contact_returns_ahp_info'
         ).to_equal(expected);

   END test_get_postgrid_from_contact_returns_ahp_info;



   -- %test(test_get_postgrid_from_contact_returns_blank_if_provider_not_found)
   PROCEDURE test_get_postgrid_from_contact_returns_blank_if_provider_not_found IS 
      expected      VARCHAR2(1000);
      actual        VARCHAR2(1000);
   BEGIN
      
      expected := '';
     
      actual := letters.get_postgrid_from_contact('Non-existing Health Plan');

      ut.expect(actual,
         'test_get_postgrid_from_contact_returns_blank_if_provider_not_found'
         ).to_equal(expected);

   END test_get_postgrid_from_contact_returns_blank_if_provider_not_found;



   -- %test(test_get_postgrid_api_key_gets_test_key)
   PROCEDURE test_get_postgrid_api_key_gets_test_key IS 
      expected      VARCHAR2(1000);
      actual        VARCHAR2(1000);
   BEGIN
      
      expected := 'test_%_qXA2';
     
      actual := letters.get_postgrid_api_key('Test');

      ut.expect(actual,
         'test_get_postgrid_api_key_gets_test_key'
         ).to_be_like(expected);

   END test_get_postgrid_api_key_gets_test_key;


   -- %test(test_get_postgrid_api_key_gets_production_key)
   PROCEDURE test_get_postgrid_api_key_gets_production_key IS 
      expected      VARCHAR2(1000);
      actual        VARCHAR2(1000);
   BEGIN
      
      expected := 'live_%_pcWT';
     
      actual := letters.get_postgrid_api_key('production');

      ut.expect(actual,
         'test_get_postgrid_api_key_gets_production_key'
         ).to_be_like(expected);

   END test_get_postgrid_api_key_gets_production_key;




   -- %test(test_mark_letter_sent_sets_status_to_sent)
   PROCEDURE test_mark_letter_sent_sets_status_to_sent IS 
      expected      VARCHAR2(1000);
      actual        VARCHAR2(1000);
   BEGIN
   
      expected := 'SENT_TO_POSTGRID';
     
      letters.mark_letter_sent('Test_Letter_1', 'ACME_123456789_ACMEANVILC_20241028', 'letter_0123456789');

      SELECT currentstatuscode
        INTO actual
        FROM ntf_notificationdeliveries
       WHERE notificationrefnumber = 'Test_Letter_1';

      ut.expect(actual,
         'test_mark_letter_sent_sets_status_to_sent'
         ).to_equal(expected);

   END test_mark_letter_sent_sets_status_to_sent;



-- %test(test_mark_letter_sent_saves_postgrid_letter_id)
   PROCEDURE test_mark_letter_sent_saves_postgrid_letter_id IS 
      expected      VARCHAR2(1000);
      actual        VARCHAR2(1000);
   BEGIN
      

      -- Use any letter; "Alignment Health Plan" info is hard-coded for now
      expected := 'letter_0123456789';
     
      letters.mark_letter_sent('Test_Letter_1', 'ACME_123456789_ACMEANVILC_20241028', 'letter_0123456789');

      SELECT transmitrefid
        INTO actual
        FROM ntf_notificationdeliveries
       WHERE notificationrefnumber = 'Test_Letter_1';

      ut.expect(actual,
         'test_mark_letter_sent_saves_postgrid_letter_id'
         ).to_equal(expected);

   END test_mark_letter_sent_saves_postgrid_letter_id;




   -- %test(test_get_letter_number_from_filename_gets_letter_number)
   PROCEDURE test_get_letter_number_from_filename_gets_letter_number IS 
      expected      VARCHAR2(1000);
      actual        VARCHAR2(1000);
   BEGIN
      
      expected := 'Test_Letter_1';
     
      actual :=
         letters.get_letter_number_from_filename('ACME_123456789_ACMEANVILC_20241028');

      ut.expect(actual,
         'test_get_letter_number_from_filename_gets_letter_number'
         ).to_equal(expected);

   END test_get_letter_number_from_filename_gets_letter_number;
   

   -- %test(test_get_letter_number_from_filename_returns_null_if_letter_not_found)
   PROCEDURE test_get_letter_number_from_filename_returns_null_if_letter_not_found IS 
      expected      VARCHAR2(1000);
      actual        VARCHAR2(1000);
   BEGIN

      expected := '';
     
      actual :=
         letters.get_letter_number_from_filename('NON-EXISTENT_FILENAME');

      ut.expect(actual,
         'test_get_letter_number_from_filename_returns_null_if_letter_not_found'
         ).to_equal(expected);

   END test_get_letter_number_from_filename_returns_null_if_letter_not_found;



-- %test(test_get_postgrid_contact_retrieves_contact)
   PROCEDURE test_get_postgrid_contact_retrieves_contact IS 
      expected      VARCHAR2(1000);
      actual        VARCHAR2(1000);
   BEGIN

      -- "contact_inzzZey9XN7EDH5CGzJPDd" is an example PostGrid contact 
      expected := '{"id":"contact_inzzZey9XN7EDH5CGzJPDd","object":"contact",' ||
         '"live":false,"addressLine1":"750 NORTH SAINT PAUL STREET",' ||
         '"addressLine2":"STE 250","addressStatus":"verified","city":"DALLAS",' ||
         '"country":"UNITED STATES","countryCode":"US","firstName":"Jane",' ||
         '"lastName":"Smith (US) - Example Contact","mailingLists":[],' ||
         '"postalOrZip":"75201","provinceOrState":"TX",' ||
         '"createdAt":"2024-10-21T16:49:28.006Z",' ||
         '"updatedAt":"2024-10-21T16:49:28.006Z"}';
     
      actual :=
         letters.get_postgrid_contact('contact_inzzZey9XN7EDH5CGzJPDd');

      ut.expect(actual,
         'test_get_postgrid_contact_retrieves_contact'
         ).to_equal(expected);

   END test_get_postgrid_contact_retrieves_contact;



  -- %test(test_get_postgrid_contact_returns_not_found_for_nonexisting_contact)
   PROCEDURE test_get_postgrid_contact_returns_not_found_for_nonexisting_contact IS 
      expected      VARCHAR2(1000);
      actual        VARCHAR2(1000);
   BEGIN

      expected := '%contact_not_found_error%';
      -- {"object":"error","error":{"type":"contact_not_found_error","message":"Contact with ID contact_123456789 was not found."}}
      actual :=
         letters.get_postgrid_contact('contact_123456789');

      ut.expect(actual,
         'test_get_postgrid_contact_returns_not_found_for_nonexisting_contact'
         ).to_be_like(expected);

   END test_get_postgrid_contact_returns_not_found_for_nonexisting_contact;


   -- %test(test_get_postgrid_contact_defaults_to_test_environment)
   PROCEDURE test_get_postgrid_contact_defaults_to_test_environment IS 
      expected      VARCHAR2(1000);
      actual        VARCHAR2(1000);
   BEGIN

      -- "contact_inzzZey9XN7EDH5CGzJPDd" is an example PostGrid contact 
      expected := '%"live":false%';
     
      actual :=
         letters.get_postgrid_contact('contact_inzzZey9XN7EDH5CGzJPDd');

      ut.expect(actual,
         'test_get_postgrid_contact_defaults_to_test_environment'
         ).to_be_like(expected);

   END test_get_postgrid_contact_defaults_to_test_environment;



-- %test(test_get_postgrid_contact_runs_in_production_environment)
   PROCEDURE test_get_postgrid_contact_runs_in_production_environment IS 
      expected      VARCHAR2(1000);
      actual        VARCHAR2(1000);
   BEGIN

      expected := '%"live":true%';

      -- Alignment Health Plan
      actual :=
         letters.get_postgrid_contact('contact_gCqJi4msix479HHr3kyQWd', 'Production');

      ut.expect(actual,
         'test_get_postgrid_contact_runs_in_production_environment'
         ).to_be_like(expected);

   END test_get_postgrid_contact_runs_in_production_environment;






   -- %test(test_create_postgrid_contact_creates_contact)
   PROCEDURE test_create_postgrid_contact_creates_contact IS 
      expected         VARCHAR2(1000);
      actual           VARCHAR2(2000);
      new_contact_id   VARCHAR2(30);
   BEGIN

      -- "contact_inzzZey9XN7EDH5CGzJPDd" is an example PostGrid contact 
      expected := '%contact_%';
     
      actual :=
         letters.create_postgrid_contact('Test_Letter_1');

      ut.expect(actual,
         'test_create_postgrid_contact_creates_contact'
         ).to_be_like(expected);

   END test_create_postgrid_contact_creates_contact;


   -- %test(test_create_postgrid_client_contact_creates_contact)
   PROCEDURE test_create_postgrid_client_contact_creates_contact IS 
      expected      VARCHAR2(1000);
      actual        VARCHAR2(1000);
      contact_id    VARCHAR2(50);
      contact_info  VARCHAR2(1000);

   BEGIN
      expected := 'Alignment Health Plan';
     
      contact_id := letters.create_postgrid_client_contact('Alignment Health Plan');

      contact_info := letters.get_postgrid_contact(contact_id);
              
      -- Get the contact_id from the response
      apex_json.parse(contact_info);
      actual := apex_json.get_varchar2(p_path=>'companyName'); 

      ut.expect(actual,
         'test_create_postgrid_client_contact_creates_contact'
         ).to_equal(expected);

   END test_create_postgrid_client_contact_creates_contact;





   -- %test(test_aop_file_exists_finds_letter_file)
   PROCEDURE test_aop_file_exists_finds_letter_file IS 

      expected      VARCHAR2(1000);
      actual        VARCHAR2(1000);
      test_filename VARCHAR2(200) := 'ACME_123456789_ACMEANVILC_20241121.pdf';
      test_file     UTL_FILE.FILE_TYPE;  
      file_exists   BOOLEAN;

   BEGIN
      
      expected := 'File exists';
     
      -- Create test file in /opt/aopdocs
      test_file := utl_file.fopen('AOP_FILE_DIR', test_filename, 'wb', 32767);
      utl_file.fclose(test_file);

      file_exists := letters.aop_file_exists(test_filename);

      IF file_exists THEN
         actual := 'File exists';
      ELSE
         actual := 'File not found';
      END IF;

      ut.expect(actual,
         'test_aop_file_exists_finds_letter_file'
         ).to_equal(expected);
         
      -- Clean-up the test file
      utl_file.fremove('AOP_FILE_DIR', test_filename );


   END test_aop_file_exists_finds_letter_file;



   -- %test(test_aop_file_exists_returns_not_found)
   PROCEDURE test_aop_file_exists_returns_not_found IS 

      expected      VARCHAR2(1000);
      actual        VARCHAR2(1000);
      test_filename VARCHAR2(200) := 'non-existing_file.pdf'; 
      file_exists   BOOLEAN;

   BEGIN
      
      expected := 'File not found';
     
      file_exists := letters.aop_file_exists(test_filename);

      IF file_exists THEN
         actual := 'File exists';
      ELSE
         actual := 'File not found';
      END IF;

      ut.expect(actual,
         'test_aop_file_exists_returns_not_found'
         ).to_equal(expected);
         
   END test_aop_file_exists_returns_not_found;



   -- %test(test_send_letter_to_postgrid_checks_for_pdf_letter)
   PROCEDURE test_send_letter_to_postgrid_checks_for_pdf_letter IS 
      expected      VARCHAR2(1000);
      actual        VARCHAR2(1000);
      
   BEGIN
      
      expected := 'ERROR: Letter file for Test_Letter_2 not found.';
     
      actual :=
         letters.send_letter_to_postgrid('Test_Letter_2');

      ut.expect(actual,
         'test_send_letter_to_postgrid_checks_for_pdf_letter'
         ).to_equal(expected);

   END test_send_letter_to_postgrid_checks_for_pdf_letter;



   -- %test(test_send_letter_to_postgrid_sends_the_letter)
   PROCEDURE test_send_letter_to_postgrid_sends_the_letter IS 

      expected      VARCHAR2(1000);
      actual        VARCHAR2(1000);
      test_filename VARCHAR2(200) := 'ACME_123456789_ACMEANVILC_20241028.pdf';

   BEGIN
           
      expected := 'letter_%';

      create_test_pdf_file(test_filename);

      actual :=
         letters.send_letter_to_postgrid('Test_Letter_1');

      ut.expect(actual,
         'test_send_letter_to_postgrid_sends_the_letter'
         ).to_be_like(expected);

      utl_file.fremove('AOP_FILE_DIR', test_filename);

   END test_send_letter_to_postgrid_sends_the_letter;





   -- %test(test_load_aop_file_to_blob_loads_aop_file)
   PROCEDURE test_load_aop_file_to_blob_loads_aop_file IS 

      expected      VARCHAR2(1000);
      actual        VARCHAR2(1000);
      test_filename VARCHAR2(200) := 'ACME_123456789_ACMEANVILC_20241119.pdf';
      test_blob     BLOB;

   BEGIN
      
      expected := '%PDF%';
         
      create_test_pdf_file(test_filename);
      test_blob := letters.load_aop_file_to_blob(test_filename);

      actual := SUBSTR(utl_raw.cast_to_varchar2(dbms_lob.substr(test_blob)), 1, 100);

      ut.expect(actual,
         'test_load_aop_file_to_blob_loads_aop_file'
         ).to_be_like(expected);
         
      utl_file.fremove('AOP_FILE_DIR', test_filename);

   END test_load_aop_file_to_blob_loads_aop_file;



   -- %test(test_load_aop_file_to_table_loads_pdf_file)
   PROCEDURE test_load_aop_file_to_table_loads_pdf_file IS 

      expected      VARCHAR2(1000);
      actual        VARCHAR2(1000);
      test_filename VARCHAR2(200) := 'test_letter_file.pdf';

   BEGIN
      
      expected := test_filename;
         
      create_test_pdf_file(test_filename);

      letters.load_aop_file_to_table(test_filename);

      SELECT filename
        INTO actual
        FROM pi_aop_files 
       WHERE filename = test_filename;

      ut.expect(actual,
         'test_load_aop_file_to_table_loads_pdf_file'
         ).to_be_like(expected);
         
      utl_file.fremove('AOP_FILE_DIR', test_filename);

   END test_load_aop_file_to_table_loads_pdf_file;




   -- %test(test_log_email_saves_email)
   PROCEDURE test_log_email_saves_email IS 

      expected      NUMBER := 0;
      actual        NUMBER;

   BEGIN
      
      letters.log_email(p_subject => 'Test log_email', p_sender => '<EMAIL>');

      SELECT email_id
        INTO actual   
        FROM pi_email_log
       WHERE subject = 'Test log_email'
         AND sender = '<EMAIL>';

      ut.expect(actual,
         'test_log_email_saves_email'
         ).to_be_greater_than(expected);

   END test_log_email_saves_email;



   -- %test(test_log_email_translates_given_date)
   PROCEDURE test_log_email_translates_given_date IS 
   -- Email date formats will be like:
   -- Tue, 26 Nov 2024 14:44:08 -0600

      expected      DATE := TO_DATE('11/26/24 14:44:08', 'MM/DD/RR HH24:MI:SS');
      actual        DATE;
      test_date     VARCHAR2(200) := 'Tue, 26 Nov 2024 14:44:08 -0600';

   BEGIN
      
      letters.log_email(p_subject => 'Test log_email saves date', 
         p_sender => '<EMAIL>', p_date_sent => test_date);

      SELECT email_time
        INTO actual   
        FROM pi_email_log
       WHERE subject = 'Test log_email saves date'
         AND sender = '<EMAIL>';

      ut.expect(actual,
         'test_log_email_translates_given_date'
         ).to_equal(expected);

   END test_log_email_translates_given_date;





   -- %test(test_load_scanned_file_loads_pdf_filename)
   PROCEDURE test_load_scanned_file_loads_pdf_filename IS 

      expected      VARCHAR2(1000);
      actual        VARCHAR2(1000);
      test_filename VARCHAR2(200) := 'test_image_file.jpg';  
      test_pdf_file VARCHAR2(200) := 'test_image_file.pdf'; 
      -- Files don't have to exist; load_scanned_file checks fileexists.

   BEGIN
      
      expected := test_pdf_file;
         
      letters.load_scanned_file(test_filename, test_pdf_file);

      SELECT pdf_filename
        INTO actual
        FROM pi_scanned_documents
       WHERE original_filename = test_filename;

      ut.expect(actual,
         'test_load_scanned_file_loads_pdf_filename'
         ).to_equal(expected);
         

   END test_load_scanned_file_loads_pdf_filename;




   -- %test(test_get_scanned_document_id_gets_last_matching_id)
   PROCEDURE test_get_scanned_document_id_gets_last_matching_id IS 

      expected      NUMBER;
      actual        NUMBER;
      test_filename VARCHAR2(200) := 'test_image_file.jpg';  
      test_pdf_file VARCHAR2(200) := 'test_image_file.pdf'; 

   BEGIN
      -- Create a couple of entries
      letters.load_scanned_file(test_filename, test_pdf_file);
      letters.load_scanned_file(test_filename, test_pdf_file);

      SELECT MAX(document_id)
        INTO expected
        FROM pi_scanned_documents
       WHERE original_filename = test_filename
         AND pdf_filename = test_pdf_file;

      actual := letters.get_scanned_document_id(test_filename, test_pdf_file);

      ut.expect(actual,
         'test_get_scanned_document_id_gets_last_matching_id'
         ).to_equal(expected);
         
   END test_get_scanned_document_id_gets_last_matching_id;


   -- %test(test_get_scanned_document_id_gets_id_for_one_match)
   PROCEDURE test_get_scanned_document_id_gets_id_for_one_match IS 

      expected      NUMBER;
      actual        NUMBER;
      test_filename VARCHAR2(200) := 'test_image_file.jpg';  
      test_pdf_file VARCHAR2(200) := 'test_image_file.pdf'; 

   BEGIN
      letters.load_scanned_file(test_filename, test_pdf_file);

      SELECT document_id
        INTO expected
        FROM pi_scanned_documents
       WHERE original_filename = test_filename
         AND pdf_filename = test_pdf_file;

      actual := letters.get_scanned_document_id(test_filename, test_pdf_file);

      ut.expect(actual,
         'test_get_scanned_document_id_gets_id_for_one_match'
         ).to_equal(expected);
         
   END test_get_scanned_document_id_gets_id_for_one_match;


   -- %test(test_get_scanned_document_id_returns_zero_if_no_match)
   PROCEDURE test_get_scanned_document_id_returns_zero_if_no_match IS 

      expected      NUMBER;
      actual        NUMBER;
      test_filename VARCHAR2(200) := 'test_image_file.jpg';  
      test_pdf_file VARCHAR2(200) := 'test_image_file.pdf'; 

   BEGIN
      expected := 0;

      actual := letters.get_scanned_document_id(test_filename, test_pdf_file);

      ut.expect(actual,
         'test_get_scanned_document_id_returns_zero_if_no_match'
         ).to_equal(expected);
         
   END test_get_scanned_document_id_returns_zero_if_no_match;




   -- %test(test_load_scanned_line_loads_text_line)
   PROCEDURE test_load_scanned_line_loads_text_line IS 

      expected      VARCHAR2(50) := 'This is a scanned line of text.';
      actual        VARCHAR2(50);
      test_doc_id   NUMBER;

   BEGIN
   
      -- Insert dummy record for the FK constraint
      INSERT INTO pi_scanned_documents 
      (original_filename, pdf_filename, pdf_file)
      VALUES ('dummy.jpg', 'dummy.pdf', empty_blob())
      RETURN document_id INTO test_doc_id;

      -- document_id, line_number, line_text
      letters.load_scanned_line(test_doc_id, 1, expected);

      SELECT line_text
        INTO actual
        FROM pi_scanned_document_lines
       WHERE document_id = test_doc_id
         AND line_number = 1;

      ut.expect(actual,
         'test_load_scanned_line_loads_text_line'
         ).to_equal(expected);
         
   END test_load_scanned_line_loads_text_line;



   -- %test(test_load_correspondence_document_loads_file_attributes)
   PROCEDURE test_load_correspondence_document_loads_file_attributes IS 

      expected      VARCHAR2(1000);
      actual        VARCHAR2(1000);
      test_filename VARCHAR2(200) := 'ABCDEF_20231101821019500006_APD243582436100154_KL.PDF';  

   BEGIN
      
      expected := 'ABCDEF';
         
      letters.load_correspondence_document(test_filename);

      SELECT attachmentsourceid1
        INTO actual
        FROM fnd_attachments
       WHERE attachmentsource = 'INBOUND'
         AND filename = test_filename
         AND attachmentsourceid2 = '20231101821019500006'
         AND attachmentsourceid3 = 'APD243582436100154_KL';

      ut.expect(actual,
         'test_load_correspondence_document_loads_file_attributes'
         ).to_equal(expected);
         
   END test_load_correspondence_document_loads_file_attributes;



   -- %test(test_load_correspondence_document_loads_document_comments)
   PROCEDURE test_load_correspondence_document_loads_document_comments IS 

      expected      VARCHAR2(1000);
      actual        VARCHAR2(1000);
      test_filename VARCHAR2(200) := 'ABCDEF_20231101821019500006_APD243582436100154_KL.PDF';  

   BEGIN
      
      expected := 'This is a document comment.';
         
      letters.load_correspondence_document(test_filename, expected);

      SELECT documentcomments
        INTO actual
        FROM fnd_attachments
       WHERE attachmentsource = 'INBOUND'
         AND filename = test_filename
         AND attachmentsourceid2 = '20231101821019500006'
         AND attachmentsourceid3 = 'APD243582436100154_KL';

      ut.expect(actual,
         'test_load_correspondence_document_loads_document_comments'
         ).to_equal(expected);
         
   END test_load_correspondence_document_loads_document_comments;




   -- %test(test_get_correspondence_document_id_gets_last_matching_id)
   PROCEDURE test_get_correspondence_document_id_gets_last_matching_id IS 

      expected      NUMBER;
      actual        NUMBER;
      test_filename VARCHAR2(200) := 'test_file.pdf';  

   BEGIN
      -- Create a couple of entries
      letters.load_correspondence_document(test_filename);
      letters.load_correspondence_document(test_filename);

      SELECT MAX(attachmentid)
        INTO expected
        FROM fnd_attachments
       WHERE filename = test_filename;

      actual := letters.get_correspondence_document_id(test_filename);

      ut.expect(actual,
         'test_get_correspondence_document_id_gets_last_matching_id'
         ).to_equal(expected);
         
   END test_get_correspondence_document_id_gets_last_matching_id;



   -- %test(test_get_correspondence_document_id_gets_id_for_one_match)
   PROCEDURE test_get_correspondence_document_id_gets_id_for_one_match IS 

      expected      NUMBER;
      actual        NUMBER;
      test_filename VARCHAR2(200) := 'test_file.pdf';  

   BEGIN
      letters.load_correspondence_document(test_filename);

      SELECT MAX(attachmentid)
        INTO expected
        FROM fnd_attachments
       WHERE filename = test_filename;

      actual := letters.get_correspondence_document_id(test_filename);

      ut.expect(actual,
         'test_get_correspondence_document_id_gets_id_for_one_match'
         ).to_equal(expected);
         
   END test_get_correspondence_document_id_gets_id_for_one_match;



   -- %test(test_get_correspondence_document_id_returns_zero_if_no_match)
   PROCEDURE test_get_correspondence_document_id_returns_zero_if_no_match IS 

      expected      NUMBER;
      actual        NUMBER;
      test_filename VARCHAR2(200) := 'test_file.pdf'; 

   BEGIN
      expected := 0;

      actual := letters.get_correspondence_document_id(test_filename);

      ut.expect(actual,
         'test_get_correspondence_document_id_returns_zero_if_no_match'
         ).to_equal(expected);
         
   END test_get_correspondence_document_id_returns_zero_if_no_match;



   -- %test(test_load_correspondence_document_line_loads_text_line)
   PROCEDURE test_load_correspondence_document_line_loads_text_line IS 

      expected      VARCHAR2(50) := 'This is an extracted line of text.';
      actual        VARCHAR2(50);
      test_doc_id   NUMBER;

   BEGIN
   
      -- Insert dummy record for the FK constraint
      INSERT INTO fnd_attachments 
      (attachmentsource, attachmentsourceid1, attachmentsourceid2, 
       attachmentsourceid3, documentcomments, filename, mimetype, 
       attachmentblob)
      VALUES 
      ('INBOUND', 'ABC', '123456', '9876543210',
       'Loaded by test_load_correspondence_document_line_loads_text_line', 
       'dummy.pdf', 'application/pdf', empty_blob())
         RETURN attachmentid INTO test_doc_id;

      -- document_id, line_number, line_text
      letters.load_correspondence_document_line(test_doc_id, 1, expected);

      SELECT line_text
        INTO actual
        FROM fnd_attachment_lines
       WHERE attachmentid = test_doc_id
         AND line_number = 1;

      ut.expect(actual,
         'test_load_correspondence_document_line_loads_text_line'
         ).to_equal(expected);
         
   END test_load_correspondence_document_line_loads_text_line;



   -- %test(test_load_correspondence_document_line_defaults_page_number)
   PROCEDURE test_load_correspondence_document_line_defaults_page_number IS 

      expected      VARCHAR2(50) := 'This is an extracted line of text.';
      actual        VARCHAR2(50);
      test_doc_id   NUMBER;

   BEGIN
   
      -- Insert dummy record for the FK constraint
      INSERT INTO fnd_attachments 
      (attachmentsource, attachmentsourceid1, attachmentsourceid2, 
       attachmentsourceid3, documentcomments, filename, mimetype, 
       attachmentblob)
      VALUES 
      ('INBOUND', 'ABC', '123456', '9876543210',
       'Loaded by test_load_correspondence_document_line_defaults_page_number', 
       'dummy.pdf', 'application/pdf', empty_blob())
         RETURN attachmentid INTO test_doc_id;

      -- document_id, line_number, line_text
      letters.load_correspondence_document_line(test_doc_id, 1, expected);

      SELECT line_text
        INTO actual
        FROM fnd_attachment_lines
       WHERE attachmentid = test_doc_id
         AND line_number = 1
         AND NVL(page_number, 0) = 1;

      ut.expect(actual,
         'test_load_correspondence_document_line_defaults_page_number'
         ).to_equal(expected);
         
   END test_load_correspondence_document_line_defaults_page_number;



   -- %test(test_load_correspondence_document_line_defaults_page_line_to_line_number)
   PROCEDURE test_load_correspondence_document_line_defaults_page_line_to_line_number IS 

      expected      VARCHAR2(50) := 'This is an extracted line of text.';
      actual        VARCHAR2(50);
      test_doc_id   NUMBER;

   BEGIN
   
      -- Insert dummy record for the FK constraint
      INSERT INTO fnd_attachments 
      (attachmentsource, attachmentsourceid1, attachmentsourceid2, 
       attachmentsourceid3, documentcomments, filename, mimetype, 
       attachmentblob)
      VALUES 
      ('INBOUND', 'ABC', '123456', '9876543210',
       'Loaded by test_load_correspondence_document_line_defaults_page_line_to_line_number', 
       'dummy.pdf', 'application/pdf', empty_blob())
         RETURN attachmentid INTO test_doc_id;

      -- document_id, line_number, line_text
      letters.load_correspondence_document_line(test_doc_id, 99, expected);

      SELECT line_text
        INTO actual
        FROM fnd_attachment_lines
       WHERE attachmentid = test_doc_id
         AND line_number = 99
         AND page_line = 99;


      ut.expect(actual,
         'test_load_correspondence_document_line_defaults_page_line_to_line_number'
         ).to_equal(expected);
         
   END test_load_correspondence_document_line_defaults_page_line_to_line_number;





   -- %test(test_load_data_file_loads_file_name)
   PROCEDURE test_load_data_file_loads_file_name IS 

      expected      VARCHAR2(1000);
      actual        VARCHAR2(1000);
      test_filename VARCHAR2(200) := 'test_excel_file.xlsx'; 

   BEGIN
      
      expected := test_filename;
         
      letters.load_data_file(test_filename);

      SELECT file_name
        INTO actual
        FROM pi_data_files
       WHERE file_name = test_filename;

      ut.expect(actual,
         'test_load_data_file_loads_file_name'
         ).to_equal(expected);
         

   END test_load_data_file_loads_file_name;



   -- %test(test_load_data_file_loads_file_type)
   PROCEDURE test_load_data_file_loads_file_type IS 

      expected      VARCHAR2(1000) := 'XLSX';
      actual        VARCHAR2(1000);
      test_filename VARCHAR2(200) := 'test_excel_file.xlsx'; 

   BEGIN
    
      letters.load_data_file(test_filename);

      SELECT file_type
        INTO actual
        FROM pi_data_files
       WHERE file_name = test_filename;

      ut.expect(actual,
         'test_load_data_file_loads_file_type'
         ).to_equal(expected);
         

   END test_load_data_file_loads_file_type;




   -- %test(test_replace_in_blob_replaces_text_in_blob)
   PROCEDURE test_replace_in_blob_replaces_text_in_blob IS 

      expected      BLOB;
      actual        BLOB;
      blob_text     VARCHAR2(100) := 'This is a test blob with #TEXT_TO_REPLACE# imbedded.';
      expected_text VARCHAR2(100) := 'This is a test blob with New Text imbedded.';
      target_blob   BLOB;
      target_string VARCHAR2(100) := '#TEXT_TO_REPLACE#';
      replacement   VARCHAR2(100) := 'New Text';

   BEGIN

      DBMS_LOB.CREATETEMPORARY(expected, TRUE);
      DBMS_LOB.OPEN(expected, DBMS_LOB.LOB_READWRITE);
      DBMS_LOB.WRITEAPPEND(expected, LENGTH(expected_text), utl_raw.cast_to_raw(expected_text));
      DBMS_LOB.CLOSE(expected);

      DBMS_LOB.CREATETEMPORARY(target_blob, TRUE);
      DBMS_LOB.OPEN(target_blob, DBMS_LOB.LOB_READWRITE);
      DBMS_LOB.WRITEAPPEND(target_blob, LENGTH(blob_text), utl_raw.cast_to_raw(blob_text));
      DBMS_LOB.CLOSE(target_blob);
    
      actual := letters.replace_in_blob(target_blob, target_string, replacement);

      ut.expect(actual,
         'test_replace_in_blob_replaces_text_in_blob'
         ).to_equal(expected);
         

   END test_replace_in_blob_replaces_text_in_blob;



   -- %test(test_replace_in_blob_returns_same_blob_if_no_match)
   PROCEDURE test_replace_in_blob_returns_same_blob_if_no_match IS 

      expected      BLOB;
      actual        BLOB;
      expected_text VARCHAR2(100) := 'This is a test blob with without the target string imbedded.';
      target_blob   BLOB;
      target_string VARCHAR2(100) := '#TEXT_TO_REPLACE#';
      replacement   VARCHAR2(100) := 'New Text';

   BEGIN

      DBMS_LOB.CREATETEMPORARY(expected, TRUE);
      DBMS_LOB.OPEN(expected, DBMS_LOB.LOB_READWRITE);
      DBMS_LOB.WRITEAPPEND(expected, LENGTH(expected_text), utl_raw.cast_to_raw(expected_text));
      DBMS_LOB.CLOSE(expected);

      actual := letters.replace_in_blob(expected, target_string, replacement);

      ut.expect(actual,
         'test_replace_in_blob_returns_same_blob_if_no_match'
         ).to_equal(expected);
         

   END test_replace_in_blob_returns_same_blob_if_no_match;




   -- %test(test_replace_in_blob_replaces_multiple_matches)
   PROCEDURE test_replace_in_blob_replaces_multiple_matches IS 

      expected      BLOB;
      actual        BLOB;
      blob_text     VARCHAR2(300) := 'This is a test blob with #TEXT_TO_REPLACE# imbedded. ';
      expected_text VARCHAR2(300) := 'This is a test blob with New Text imbedded. ';
      target_blob   BLOB;
      target_string VARCHAR2(100) := '#TEXT_TO_REPLACE#';
      replacement   VARCHAR2(100) := 'New Text';

   BEGIN
      blob_text := blob_text || 'This is a second occurrence with #TEXT_TO_REPLACE# imbedded.';
      expected_text  := expected_text || 'This is a second occurrence with New Text imbedded.';

      DBMS_LOB.CREATETEMPORARY(expected, TRUE);
      DBMS_LOB.OPEN(expected, DBMS_LOB.LOB_READWRITE);
      DBMS_LOB.WRITEAPPEND(expected, LENGTH(expected_text), utl_raw.cast_to_raw(expected_text));
      DBMS_LOB.CLOSE(expected);

      DBMS_LOB.CREATETEMPORARY(target_blob, TRUE);
      DBMS_LOB.OPEN(target_blob, DBMS_LOB.LOB_READWRITE);
      DBMS_LOB.WRITEAPPEND(target_blob, LENGTH(blob_text), utl_raw.cast_to_raw(blob_text));
      DBMS_LOB.CLOSE(target_blob);
    
      actual := letters.replace_in_blob(target_blob, target_string, replacement);

      ut.expect(actual,
         'test_replace_in_blob_replaces_multiple_matches'
         ).to_equal(expected);
         

   END test_replace_in_blob_replaces_multiple_matches;

   /*
   -- %test(test_replace_in_blob_replaces_id_pdf)
   -- Does not work with the current implementation of replace_in_blob
   PROCEDURE test_replace_in_blob_replaces_id_pdf IS 

      expected      BLOB;
      actual        BLOB;
      test_filename VARCHAR2(200) := 'ACME_123456789_ACMEANVILC_20250403.pdf';

   BEGIN
           
      -- Create the test file in AOP_DIR_FILE
      create_test_pdf_file(test_filename);

      -- Load the file into PI_AOP_FILES
      letters.load_aop_file_to_table(test_filename);

      -- Get the blob 
      SELECT blob_data
        INTO expected
        FROM pi_aop_files
       WHERE filename = test_filename;

 
dbms_output.PUT_LINE('REFUND Position: ' || DBMS_LOB.INSTR(expected, utl_raw.cast_to_raw('REFUND'), 1, 1));

      actual := letters.replace_in_blob(expected, 'REFUND', 'RapidPIE');

      ut.expect(actual,
         'test_replace_in_blob_replaces_id_pdf'
         ).not_to_equal(expected);

      utl_file.fremove('AOP_FILE_DIR', test_filename);

   END test_replace_in_blob_replaces_id_pdf;
   */



   -- %test(test_read_blob_from_file_reads_file)
   PROCEDURE test_read_blob_from_file_reads_file IS 

      expected      BLOB;
      actual        BLOB;
      test_filename VARCHAR2(200) := 'ACME_123456789_ACMEANVILC_20250403.pdf';

   BEGIN
           
      -- Create the test file in AOP_FILE_DIR directory
      create_test_pdf_file(test_filename);

      -- Get the blob 
      SELECT blob_data
        INTO expected
        FROM pi_aop_files
       WHERE filename = 'TEST_LETTER.pdf';

      -- Read the file from the file system
      actual := letters.read_blob_from_file(test_filename, 'AOP_FILE_DIR');

      ut.expect(actual,
         'test_read_blob_from_file_reads_file'
         ).to_equal(expected);

      utl_file.fremove('AOP_FILE_DIR', test_filename);

   END test_read_blob_from_file_reads_file;




   -- %test(test_write_blob_to_file_creates_pdf_file)
   PROCEDURE test_write_blob_to_file_creates_pdf_file IS 

      expected      BLOB;
      actual        BLOB;
      test_filename VARCHAR2(200) := 'ACME_123456789_ACMEANVILC_20250408.pdf';

   BEGIN
      -- Get the blob 
      SELECT blob_data
        INTO expected
        FROM pi_aop_files
       WHERE filename = 'TEST_LETTER.pdf';
           
      -- Create the test file in default (RPI_INBOUND) directory
      letters.write_blob_to_file(expected, test_filename);

      -- Read the file from default (/opt/inbound) directory on the file system
      actual := letters.read_blob_from_file(test_filename);

      ut.expect(actual,
         'test_write_blob_to_file_creates_pdf_file'
         ).to_equal(expected);

      utl_file.fremove('RPI_INBOUND', test_filename);

   END test_write_blob_to_file_creates_pdf_file;




   -- %test(test_write_blob_to_file_creates_excel_file)
   PROCEDURE test_write_blob_to_file_creates_excel_file IS 

      expected      BLOB;
      actual        BLOB;
      test_filename VARCHAR2(200) := 'test_file.xlsx';

   BEGIN

      create_test_excel_file (test_filename);

      -- Get the blob 
      SELECT data_file
        INTO expected
        FROM pi_data_files
       WHERE file_name = 'test_file.xlsx';
           
      -- Create the test file in default (RPI_INBOUND) directory
      letters.write_blob_to_file(expected, test_filename);

      -- Read the file from default (/opt/inbound) directory on the file system
      actual := letters.read_blob_from_file(test_filename);

      ut.expect(actual,
         'test_write_blob_to_file_creates_excel_file'
         ).to_equal(expected);

      utl_file.fremove('RPI_INBOUND', test_filename);

   END test_write_blob_to_file_creates_excel_file;





   -- %test(test_export_data_to_excel_file_creates_file)
   PROCEDURE test_export_data_to_excel_file_creates_file IS 

      actual        BOOLEAN; -- file_exists parameter in fgetattr
      test_filename VARCHAR2(200) := 'test_excel_file.xlsx';
      test_sql      VARCHAR2(2000) := 'SELECT id, file_name, file_type, mime_type, upload_date FROM pi_data_files WHERE file_name = ''test_file.xlsx''';
      file_length   NUMBER;
      block_size    NUMBER;

   BEGIN
           
      -- Create the test file in default (RPI_INBOUND) directory
      letters.export_data_to_excel_file(test_sql, test_filename);

      -- Check if the file exists
      utl_file.fgetattr('RPI_INBOUND', test_filename, actual, file_length, block_size);

      ut.expect(actual,
         'test_export_data_to_excel_file_creates_file'
         ).to_be_true();

      -- Clean-up the file
      utl_file.fremove('RPI_INBOUND', test_filename);

   END test_export_data_to_excel_file_creates_file;




   -- %test(test_export_tracker_file_creates_file)
   PROCEDURE test_export_tracker_file_creates_file IS 

      actual             BOOLEAN; -- file_exists parameter in fgetattr
      test_filename      VARCHAR2(200) := 'another_test_excel_file.xlsx';
      test_source_file   VARCHAR2(200) := '32948396114863499/Missing_11-NDC_02162025_Phase1_Approved_03292025.xlsx';  
      file_length        NUMBER;
      block_size         NUMBER;

   BEGIN
      -- This test assumes the '32948396114863499/Missing_11-NDC_02162025_Phase1_Approved_03292025.xlsx' 
      -- exists in the XXADT_AHC_FILETRACKER_V view (it'd be difficult to create test data).

      -- Create the test file in default (RPI_INBOUND) directory
      letters.export_tracker_file(test_source_file, test_filename);

      -- Check if the file exists
      utl_file.fgetattr('RPI_INBOUND', test_filename, actual, file_length, block_size);

      ut.expect(actual,
         'test_export_tracker_file_creates_file'
         ).to_be_true();

      -- Clean-up the file
      utl_file.fremove('RPI_INBOUND', test_filename);

   END test_export_tracker_file_creates_file;



   -- %test(test_export_tracker_file_creates_file_specified_directory)
   PROCEDURE test_export_tracker_file_creates_file_specified_directory IS 

      actual             BOOLEAN; -- file_exists parameter in fgetattr
      test_filename      VARCHAR2(200) := 'another_test_excel_file.xlsx';
      test_source_file   VARCHAR2(200) := '32948396114863499/Missing_11-NDC_02162025_Phase1_Approved_03292025.xlsx';

      file_length        NUMBER;
      block_size         NUMBER;

   BEGIN
      -- This test assumes the '32948396114863499/Missing_11-NDC_02162025_Phase1_Approved_03292025.xlsx' 
      -- exists in the XXADT_AHC_FILETRACKER_V view (it'd be difficult to create test data).

      -- Create the test file in specified (AOP_FILE_DIR) directory
      letters.export_tracker_file(test_source_file, test_filename, 'AOP_FILE_DIR');

      -- Check if the file exists
      utl_file.fgetattr('AOP_FILE_DIR', test_filename, actual, file_length, block_size);

      ut.expect(actual,
         'test_export_tracker_file_creates_file_specified_directory'
         ).to_be_true();

      -- Clean-up the file
      utl_file.fremove('AOP_FILE_DIR', test_filename);

   END test_export_tracker_file_creates_file_specified_directory;






   -- %test(test_export_symkey_files_creates_multiple_files)
   PROCEDURE test_export_symkey_files_creates_multiple_files IS 

      actual             BOOLEAN; -- file_exists parameter in fgetattr
      test_source_file   VARCHAR2(200) := '32948396114863499/Missing_11-NDC_02162025_Phase1_Approved_03292025.xlsx'; 
      test_date          VARCHAR2(6)   := TO_CHAR(SYSDATE, 'MMDDRR');
      test_file_1        VARCHAR2(200) := 'ADVICE_AZHMO_' || test_date || '.xlsx';
      test_file_2        VARCHAR2(200) := 'ADVICE_CCHP_' || test_date || '.xlsx';
      test_file_3        VARCHAR2(200) := 'ADVICE_NOCA_' || test_date || '.xlsx';
      file_length        NUMBER;
      block_size         NUMBER;

   BEGIN
      -- This test assumes the '32948396114863499/Missing_11-NDC_02162025_Phase1_Approved_03292025.xlsx' 
      -- exists in the XXADT_AHC_SIMKEY_V view (it'd be difficult to create test data).
      --
      -- This test should create three files in the RPI_INBOUND directory;
      --    ADVICE_AZHMO_<date>.xlsx
      --    ADVICE_CCHP_<date>.xlsx
      --    ADVICE_NOCA_<date>.xlsx

      -- Create the test file in default (RPI_INBOUND) directory
      letters.export_symkey_files(test_source_file);

      -- Check if the file exists
      utl_file.fgetattr('RPI_INBOUND', test_file_1, actual, file_length, block_size);

      if actual then
         utl_file.fgetattr('RPI_INBOUND', test_file_2, actual, file_length, block_size);
      end if;

      if actual then
         utl_file.fgetattr('RPI_INBOUND', test_file_3, actual, file_length, block_size);
      end if;

      ut.expect(actual,
         'test_export_symkey_files_creates_multiple_files'
         ).to_be_true();

      -- Clean-up the files
      utl_file.fremove('RPI_INBOUND', test_file_1);
      utl_file.fremove('RPI_INBOUND', test_file_2);
      utl_file.fremove('RPI_INBOUND', test_file_3);

   END test_export_symkey_files_creates_multiple_files;






   -- %test(test_export_claim_letter_files_uploads_file)
   PROCEDURE test_export_claim_letter_files_uploads_file IS 

      --expected           VARCHAR2(50) := '20241029821052100079';
      actual             BOOLEAN; -- file_exists parameter in fgetattr
      test_letter_file   VARCHAR2(200) := 'AZHMO_20241223821019500051_JOSERNAVAR.pdf';
      test_source_file   VARCHAR2(200) := '32948396114863499/Missing_11-NDC_02162025_Phase1_Approved_03292025.xlsx';

      file_length        NUMBER;
      block_size         NUMBER;

   BEGIN
      -- This test assumes the '32948396114863499/Missing_11-NDC_02162025_Phase1_Approved_03292025.xlsx' 
      -- exists in the XXADT_AHC_FILETRACKER_V view (it'd be difficult to create test data).

      -- This test should create four files in the RPI_INBOUND directory;
      --    AZHMO_20241223821019500051_JOSERNAVAR.pdf
      --    CCHP_20241126821052100192_DELIAMLEAN.pdf
      --    CCHP_20241210821052100088_LINDASCHUR.pdf
      --    CCHP_20241218821019500054_RAFAELARRO.pdf
      --    CCHP_20241029821052100079_JOSEEATO.pdf
      --    CCHP_20241029821052100081_GERALDINEE.pdf
      --    CCHP_20241029821052100094_FRANCILLEJ.pdf
      --    CCHP_20241030821052100006_AVTARSATTW.pdf
      --    CCHP_20241101821019500052_ANITAJCABR.pdf
      --    CCHP_20241101821019500076_DONALDDBER.pdf
      --    CCHP_20241104821052100001_HANCOCKGRI.pdf
      --    CCHP_20241104821052100041_GEORGEMSOU.pdf
      --    CCHP_20241105821019500867_JOHNRMOSEL.pdf
      --    CCHP_20241105821019500884_RICHARDASA.pdf
      --    CCHP_20241106821019500386_PACITAJAIN.pdf
      --    CCHP_20241124821052100044_GABRIELEWU.pdf
      --    CCHP_20241126821019500412_CARMENRAMI.pdf
      --    CCHP_20241204821052100079_DONALDDBER.pdf
      --    CCHP_20241205821019500024_JANICEECRA.pdf
      --    CCHP_20241205821019500047_BRIANSWINC.pdf
      --    CCHP_20241205821019500061_JIMMYLCOOP.pdf
      --    CCHP_20241205821019500087_PAMELASHOS.pdf
      --    CCHP_20241205821019500153_FRANCILLEJ.pdf
      --    CCHP_20241205821019500179_JOSEEATO.pdf
      --    CCHP_20241206821019500102_LARRYESWEA.pdf
      --    CCHP_20241206821019500125_THEODORECS.pdf
      --    CCHP_20241206821019500149_JACQUELYNM.pdf
      --    CCHP_20241206821019500198_LIDAHERMIS.pdf
      --    CCHP_20241209821052100182_ROSSANACIT.pdf
      --    CCHP_20241210821019500597_MARILYNAIR.pdf
      --    CCHP_20241210821052100058_WALTERSIMO.pdf
      --    CCHP_20241210821052100105_NHAMVLE.pdf
      --    CCHP_20241210821052100128_HANCOCKGRI.pdf
      --    CCHP_20241212821019500396_DONALDDBER.pdf
      --    CCHP_20241212821019500408_SALVADORCM.pdf
      --    CCHP_20241212821019500437_ANITAJCABR.pdf
      --    CCHP_20241212821019500454_DONALDDBER.pdf
      --    CCHP_20241213821052100062_JIMMYPMURO.pdf
      --    CCHP_20241216821019500381_MATHEWMMAC.pdf
      --    CCHP_20241216821019500458_JOSELUISAN.pdf
      --    CCHP_20241216821052100066_EDDYLI.pdf
      --    CCHP_20241217821019500333_MARILYNAIR.pdf
      --    CCHP_20241219821052100175_MARIAEMARQ.pdf
      --    NOCA_20241210821052100020_JOANNPLOPE.pdf
      --    NOCA_20241213821052100013_JOANNPLOPE.pdf
      --    NOCA_20241216821019500023_CHRISTINAR.pdf
      --    NOCA_20241223821052100008_JOANNPLOPE.pdf

      -- Create the test file in default (RPI_INBOUND) directory
      letters.export_claim_letter_files(test_source_file);

      -- Check if the file exists
      utl_file.fgetattr('RPI_INBOUND', test_letter_file, actual, file_length, block_size);

      ut.expect(actual,
         'test_export_claim_letter_files_uploads_file'
         ).to_be_true();

      -- Clean-up the files (hard-coded for now)
      utl_file.fremove('RPI_INBOUND', test_letter_file);
      utl_file.fremove('RPI_INBOUND', 'CCHP_20241126821052100192_DELIAMLEAN.pdf');
      utl_file.fremove('RPI_INBOUND', 'CCHP_20241210821052100088_LINDASCHUR.pdf');
      utl_file.fremove('RPI_INBOUND', 'CCHP_20241218821019500054_RAFAELARRO.pdf');
      utl_file.fremove('RPI_INBOUND', 'CCHP_20241029821052100079_JOSEEATO.pdf');
      utl_file.fremove('RPI_INBOUND', 'CCHP_20241029821052100081_GERALDINEE.pdf');
      utl_file.fremove('RPI_INBOUND', 'CCHP_20241029821052100094_FRANCILLEJ.pdf');
      utl_file.fremove('RPI_INBOUND', 'CCHP_20241030821052100006_AVTARSATTW.pdf');
      utl_file.fremove('RPI_INBOUND', 'CCHP_20241101821019500052_ANITAJCABR.pdf');
      utl_file.fremove('RPI_INBOUND', 'CCHP_20241101821019500076_DONALDDBER.pdf');
      utl_file.fremove('RPI_INBOUND', 'CCHP_20241104821052100001_HANCOCKGRI.pdf');
      utl_file.fremove('RPI_INBOUND', 'CCHP_20241104821052100041_GEORGEMSOU.pdf');
      utl_file.fremove('RPI_INBOUND', 'CCHP_20241105821019500867_JOHNRMOSEL.pdf');
      utl_file.fremove('RPI_INBOUND', 'CCHP_20241105821019500884_RICHARDASA.pdf');
      utl_file.fremove('RPI_INBOUND', 'CCHP_20241106821019500386_PACITAJAIN.pdf');
      utl_file.fremove('RPI_INBOUND', 'CCHP_20241124821052100044_GABRIELEWU.pdf');
      utl_file.fremove('RPI_INBOUND', 'CCHP_20241126821019500412_CARMENRAMI.pdf');
      utl_file.fremove('RPI_INBOUND', 'CCHP_20241204821052100079_DONALDDBER.pdf');
      utl_file.fremove('RPI_INBOUND', 'CCHP_20241205821019500024_JANICEECRA.pdf');
      utl_file.fremove('RPI_INBOUND', 'CCHP_20241205821019500047_BRIANSWINC.pdf');
      utl_file.fremove('RPI_INBOUND', 'CCHP_20241205821019500061_JIMMYLCOOP.pdf');
      utl_file.fremove('RPI_INBOUND', 'CCHP_20241205821019500087_PAMELASHOS.pdf');
      utl_file.fremove('RPI_INBOUND', 'CCHP_20241205821019500153_FRANCILLEJ.pdf');
      utl_file.fremove('RPI_INBOUND', 'CCHP_20241205821019500179_JOSEEATO.pdf');
      utl_file.fremove('RPI_INBOUND', 'CCHP_20241206821019500102_LARRYESWEA.pdf');
      utl_file.fremove('RPI_INBOUND', 'CCHP_20241206821019500125_THEODORECS.pdf');
      utl_file.fremove('RPI_INBOUND', 'CCHP_20241206821019500149_JACQUELYNM.pdf');
      utl_file.fremove('RPI_INBOUND', 'CCHP_20241206821019500198_LIDAHERMIS.pdf');
      utl_file.fremove('RPI_INBOUND', 'CCHP_20241209821052100182_ROSSANACIT.pdf');
      utl_file.fremove('RPI_INBOUND', 'CCHP_20241210821019500597_MARILYNAIR.pdf');
      utl_file.fremove('RPI_INBOUND', 'CCHP_20241210821052100058_WALTERSIMO.pdf');
      utl_file.fremove('RPI_INBOUND', 'CCHP_20241210821052100105_NHAMVLE.pdf');
      utl_file.fremove('RPI_INBOUND', 'CCHP_20241210821052100128_HANCOCKGRI.pdf');
      utl_file.fremove('RPI_INBOUND', 'CCHP_20241212821019500396_DONALDDBER.pdf');
      utl_file.fremove('RPI_INBOUND', 'CCHP_20241212821019500408_SALVADORCM.pdf');
      utl_file.fremove('RPI_INBOUND', 'CCHP_20241212821019500437_ANITAJCABR.pdf');
      utl_file.fremove('RPI_INBOUND', 'CCHP_20241212821019500454_DONALDDBER.pdf');
      utl_file.fremove('RPI_INBOUND', 'CCHP_20241213821052100062_JIMMYPMURO.pdf');
      utl_file.fremove('RPI_INBOUND', 'CCHP_20241216821019500381_MATHEWMMAC.pdf');
      utl_file.fremove('RPI_INBOUND', 'CCHP_20241216821019500458_JOSELUISAN.pdf');
      utl_file.fremove('RPI_INBOUND', 'CCHP_20241216821052100066_EDDYLI.pdf');
      utl_file.fremove('RPI_INBOUND', 'CCHP_20241217821019500333_MARILYNAIR.pdf');
      utl_file.fremove('RPI_INBOUND', 'CCHP_20241219821052100175_MARIAEMARQ.pdf');
      utl_file.fremove('RPI_INBOUND', 'NOCA_20241210821052100020_JOANNPLOPE.pdf');
      utl_file.fremove('RPI_INBOUND', 'NOCA_20241213821052100013_JOANNPLOPE.pdf');
      utl_file.fremove('RPI_INBOUND', 'NOCA_20241216821019500023_CHRISTINAR.pdf');
      utl_file.fremove('RPI_INBOUND', 'NOCA_20241223821052100008_JOANNPLOPE.pdf');


   END test_export_claim_letter_files_uploads_file;



   -- %test(test_get_provider_letter_cursor_creates_cursor)
   PROCEDURE test_get_provider_letter_cursor_creates_cursor IS 
      expected      SYS_REFCURSOR;
      actual        SYS_REFCURSOR;
      letter_number VARCHAR2(50) := '*************'; 
   BEGIN
      
      OPEN expected FOR
         SELECT del.notificationrefnumber letternumber, 
                del.sourcesystemcode, 
                TO_CHAR(del.letterdate, 'MM/DD/YYYY') letterdate, 
                del.providertin, 
                NVL(del.providerattention, 'REFUND UNIT') providerattention,
                del.providergroupname,
                del.provideraddressline1,
                del.provideraddressline2,
                DECODE(provideraddressline2, NULL, 'false', 'true') provideraddressline2_available,
                del.providercity, 
                del.providerstatecode,
                del.providerzip,
                (SELECT TO_CHAR(SUM(overpaidamount), 'FM$999,999,999.00') 
                  FROM ntf_notificationrequests 
                 WHERE notificationdeliveryid = del.notificationdeliveryid) totaloverpayment,
                del.appealpobox,
                del.appealaddressline1,
                del.appealaddressline2,
                DECODE(appealaddressline2, NULL, 'false', 'true') appealaddressline2_available, 
                del.appealcity,
                del.appealstatecode,
                del.appealzipcode,
                del.LetterText1 blurb,
                REPLACE(del.providerid, '-', '') providercode,
                letters.get_provider_short_name(del.providergroupname) provider_short_name
           FROM ntf_notificationdeliveries del
          WHERE del.notificationrefnumber = letter_number
          ORDER BY del.notificationbatchid, del.notificationdeliveryid;

      actual := letters.get_provider_letter_cursor(letter_number);

      ut.expect(actual,
         'test_get_provider_letter_cursor_creates_cursor'
         ).to_equal(expected);

      IF expected%ISOPEN THEN
         CLOSE expected;
      END IF;

      IF actual%ISOPEN THEN
         CLOSE actual;
      END IF;

   END test_get_provider_letter_cursor_creates_cursor;
   





   -- %test(test_get_provider_letter_details_cursor_creates_cursor)
   PROCEDURE test_get_provider_letter_details_cursor_creates_cursor IS 
      expected      SYS_REFCURSOR;
      actual        SYS_REFCURSOR;
      letter_number VARCHAR2(50) := '*************'; 
   BEGIN
      
      OPEN expected FOR
         SELECT req.notificationdeliveryid,
                req.claimnumber,
                req.claimlinenumber,
                req.membername,
                req.memberid,
                req.patientaccountnumber,
                TO_CHAR(req.dateofservicefrom, 'MM/DD/YYYY') dateofservicefrom,
                TO_CHAR(req.dateofserviceto, 'MM/DD/YYYY') dateofserviceto,
                TO_CHAR(req.totalbillamount, 'FM$999,999,999.00') totalbillamount,
                TO_CHAR(req.paidbyplan, 'FM$999,999,999.00') paidbyplan,
                TO_CHAR(req.claimpaiddate, 'MM/DD/YYYY') claimpaiddate,
                req.checknumber,
                TO_CHAR(req.overpaidamount, 'FM$999,999,999.00') overpaidamount,
                req.overpaymentdescription,
                req.overpaymentconcept,
                TO_CHAR(req.submitdate, 'MM/DD/YYYY') submitdate,
                req.statuscode,
                TO_CHAR(req.hspceffdate, 'MM/DD/YYYY') hspceffdate,  
                TO_CHAR(req.hspcenddate, 'MM/DD/YYYY') hspcenddate
           FROM ntf_notificationrequests req
          WHERE req.notificationdeliveryid = 
                (SELECT del.notificationdeliveryid
                   FROM ntf_notificationdeliveries del
                  WHERE del.notificationrefnumber = letter_number)
          ORDER BY req.membername, req.claimnumber, req.claimlinenumber;

      actual := letters.get_provider_letter_details_cursor(letter_number);

      ut.expect(actual,
         'test_get_provider_letter_details_cursor_creates_cursor'
         ).to_equal(expected);

      IF expected%ISOPEN THEN
         CLOSE expected;
      END IF;

      IF actual%ISOPEN THEN
         CLOSE actual;
      END IF;

   END test_get_provider_letter_details_cursor_creates_cursor;






   -- %test(test_get_provider_short_name_returns_10_characters_without_spaces)
   PROCEDURE test_get_provider_short_name_returns_10_characters_without_spaces IS
      expected      VARCHAR2(10) := 'PINNACLEHO';
      actual        VARCHAR2(50);
      test_name     VARCHAR2(50) := 'PINNACLE HOME HEALTH AND HOSPICE INC';
   BEGIN
      actual := letters.get_provider_short_name(test_name);

      ut.expect(actual,
         'test_get_provider_short_name_returns_10_characters_without_spaces'
         ).to_equal(expected);
         
   END test_get_provider_short_name_returns_10_characters_without_spaces;



   -- %test(test_get_provider_short_name_removes_special_characters)
   PROCEDURE test_get_provider_short_name_removes_special_characters IS
      expected      VARCHAR2(10) := 'EYEOCULARS';
      actual        VARCHAR2(50);
      test_name     VARCHAR2(50) := 'EYE & OCULAR SURFACE CENTER OF TEXAS PLLC';
   BEGIN
      actual := letters.get_provider_short_name(test_name);

      ut.expect(actual,
         'test_get_provider_short_name_removes_special_characters'
         ).to_equal(expected);
         
   END test_get_provider_short_name_removes_special_characters;




   -- %test(test_get_claim_letter_cursor_creates_cursor)
   PROCEDURE test_get_claim_letter_cursor_creates_cursor IS 
      expected      SYS_REFCURSOR;
      actual        SYS_REFCURSOR;
      letter_number VARCHAR2(50) := '*************'; 
      claim_number  VARCHAR2(50) := '20241029821052100079';
   BEGIN
      
      OPEN expected FOR
         SELECT del.notificationrefnumber letternumber, 
                del.sourcesystemcode, 
                TO_CHAR(del.letterdate, 'MM/DD/YYYY') letterdate, 
                del.providertin, 
                NVL(del.providerattention, 'REFUND UNIT') providerattention,
                del.providergroupname,
                del.provideraddressline1,
                del.provideraddressline2,
                del.providercity, 
                del.providerstatecode,
                del.providerzip,
                del.appealpobox,
                del.appealaddressline1,
                del.appealaddressline2,
                del.appealcity,
                del.appealstatecode,
                del.appealzipcode,
                del.LetterText1 blurb,
                req.membername,
                req.memberid, 
                TO_CHAR(req.memberdob, 'MM/DD/YYYY') memberdob,
                req.claimnumber,
                req.patientaccountnumber,
                TO_CHAR(req.dateofservicefrom, 'MM/DD/YYYY') dateofservicefrom,
                TO_CHAR(req.dateofserviceto, 'MM/DD/YYYY') dateofserviceto,
                TO_CHAR(req.totalbillamount, 'FM$999,999,999.00') totalbillamount,
                TO_CHAR(req.paidbyplan, 'FM$999,999,999.00') paidbyplan,
                TO_CHAR(req.claimpaiddate, 'MM/DD/YYYY') claimpaiddate,
                req.checknumber,
                TO_CHAR(req.overpaidamount, 'FM$999,999,999.00') overpaidamount,
                TO_CHAR(req.hspceffdate, 'MM/DD/YYYY') hspceffdate,  
                TO_CHAR(req.hspcenddate, 'MM/DD/YYYY') hspcenddate,
                SUBSTR(REGEXP_REPLACE(membername, '[^A-Za-z0-9]', ''), 1, 10) membershortname
           FROM ntf_notificationdeliveries del,
                (SELECT notificationdeliveryid,
                        claimnumber,
                        MAX(membername) membername,
                        MAX(memberid) memberid,
                        MAX(memberdob) memberdob,
                        MAX(patientaccountnumber) patientaccountnumber,
                        MAX(dateofservicefrom) dateofservicefrom,
                        MAX(dateofserviceto) dateofserviceto,
                        MAX(totalbillamount) totalbillamount,
                        SUM(paidbyplan) paidbyplan,
                        MAX(claimpaiddate) claimpaiddate,
                        MAX(checknumber) checknumber,
                        SUM(overpaidamount) overpaidamount,
                        MAX(hspceffdate) hspceffdate,  
                        MAX(hspcenddate) hspcenddate
                   FROM ntf_notificationrequests req 
                  WHERE claimnumber = claim_number
                  GROUP BY notificationdeliveryid, claimnumber) req
          WHERE del.notificationdeliveryid = req.notificationdeliveryid
            AND del.notificationrefnumber = letter_number
            AND req.claimnumber = claim_number;

      actual := letters.get_claim_letter_cursor(letter_number, claim_number);

      ut.expect(actual,
         'test_get_claim_letter_cursor_creates_cursor'
         ).to_equal(expected);

      IF expected%ISOPEN THEN
         CLOSE expected;
      END IF;

      IF actual%ISOPEN THEN
         CLOSE actual;
      END IF;

   END test_get_claim_letter_cursor_creates_cursor;

   



   -- %test(test_get_claim_letter_details_cursor_creates_cursor)
   PROCEDURE test_get_claim_letter_details_cursor_creates_cursor IS 
      expected      SYS_REFCURSOR;
      actual        SYS_REFCURSOR;
      letter_number VARCHAR2(50) := '*************'; 
      claim_number  VARCHAR2(50) := '20241029821052100079';
   BEGIN
      
      OPEN expected FOR
         SELECT overpaymentdescription || 
                DECODE(claimlinenumber, '0', NULL,' on DOS ' || 
                TO_CHAR(dateofservicefrom,'dd-Mon-yyyy')) overpaymentdescription
           FROM ntf_notificationrequests 
          WHERE notificationdeliveryid = 
                (SELECT notificationdeliveryid
                   FROM ntf_notificationdeliveries
                  WHERE notificationrefnumber = letter_number)
            AND claimnumber = claim_number
          ORDER BY claimlinenumber;

      actual := letters.get_claim_letter_details_cursor(letter_number, claim_number);

      ut.expect(actual,
         'test_get_claim_letter_details_cursor_creates_cursor'
         ).to_equal(expected);

      IF expected%ISOPEN THEN
         CLOSE expected;
      END IF;

      IF actual%ISOPEN THEN
         CLOSE actual;
      END IF;

   END test_get_claim_letter_details_cursor_creates_cursor;



END test_letters;
/
  


SHOW ERRORS

SET SERVEROUTPUT ON SIZE 1000000

BEGIN
   ut.run('test_letters');
END;
/

SPOOL OFF

-- Complete
