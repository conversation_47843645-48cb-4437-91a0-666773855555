------------------------------------------------------------------------------
--
--   PROCEDURE:      pi_create_worksheet_column_templates_table.sql
--
--   PURPOSE:        This procedure is used to create the 
--                   PI_WORKSHEET_COLUMN_TEMPLATES table. 
-- 
--   PARAMETERS:     None. 
--
--   INSTRUCTIONS:   From within SQL*Plus (connected as APPS), type:
--                      START pi_create_worksheet_column_templates_table.sql
--
--   AUTHOR:         <PERSON>re.
--
--   INSTALL SCHEMA: APPS
--
--
--   Copyright (c) 2025 Kevin <PERSON>uire
--   All rights reserved.
--
--   This software is proprietary and confidential. Unauthorized copying of this
--   file, via any medium, is strictly prohibited without the express permission
--   of the author or owning organization.
--
------------------------------------------------------------------------------

WHENEVER OSERROR EXIT

SET ECHO ON


-- Open the log file
SPOOL pi_create_worksheet_column_templates_table


-- Drop the current table 
DROP TABLE pi_worksheet_column_templates;

-- Create the table
CREATE TABLE pi_worksheet_column_templates
(
   column_id            NUMBER GENERATED ALWAYS AS IDENTITY 
                        MINVALUE 1 MAXVALUE 9999999999999999999999999999 
                        INCREMENT BY 1 START WITH 1 NOT NULL ENABLE,
   worksheet_id         NUMBER        NOT NULL,
   column_order         NUMBER        NOT NULL,
   column_name          VARCHAR2(100) NOT NULL,
   data_type            VARCHAR2(100) DEFAULT 'text',
   font                 VARCHAR2(100) DEFAULT 'Default',
   font_size            NUMBER        DEFAULT 11,
   font_color           VARCHAR2(10)  DEFAULT 'Default',
   background_color     VARCHAR2(10)  DEFAULT 'Default',
   bold                 VARCHAR2(1)   DEFAULT 'N',
   italic               VARCHAR2(1)   DEFAULT 'N',
   condition_type       VARCHAR2(100) DEFAULT 'None',
   condition_value      VARCHAR2(100),
   condition_font_color VARCHAR2(10)  DEFAULT 'Default',
   condition_bg_color   VARCHAR2(10)  DEFAULT 'Default',
   lock_column          VARCHAR2(1)   DEFAULT 'N',
   hide_column          VARCHAR2(1)   DEFAULT 'N',
   createdby            VARCHAR2(200) DEFAULT 'SYSTEM',
   createddate          DATE          DEFAULT SYSDATE,
   modifiedby           VARCHAR2(200) DEFAULT 'SYSTEM',
   modifieddate         DATE          DEFAULT SYSDATE,
   --
   CONSTRAINT pi_worksheet_column_templates_pk
      PRIMARY KEY (column_id, worksheet_id)
      USING INDEX,
   CONSTRAINT  pi_worksheet_column_templates_fk
      FOREIGN KEY (worksheet_id)
      REFERENCES pi_worksheet_templates (worksheet_id)
);


-- Stop logging
SPOOL OFF 


-- Complete


