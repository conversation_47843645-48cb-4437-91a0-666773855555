------------------------------------------------------------------------------
--
--   PROCEDURE:      pi_create_scanned_documents_table.sql
--
--   PURPOSE:        This procedure is used to create the PI_SCANNED_DOCUMENTS 
--                   table. This table is used to hold scanned image files.
-- 
--   PARAMETERS:     None. 
--
--   INSTRUCTIONS:   From within SQL*Plus (connected as APPS), type:
--                      START pi_create_scanned_documents_table.sql
--
--   AUTHOR:         <PERSON>uire.
--
--   INSTALL SCHEMA: APPS
--
--
--   Copyright (c) 2025 Kevin <PERSON>uire
--   All rights reserved.
--
--   This software is proprietary and confidential. Unauthorized copying of this
--   file, via any medium, is strictly prohibited without the express permission
--   of the author or owning organization.
--
------------------------------------------------------------------------------

WHENEVER OSERROR EXIT

SET ECHO ON


-- Open the log file
SPOOL pi_create_scanned_documents_table


-- Drop the current table 
DROP TABLE pi_scanned_documents;

-- Create the table
CREATE TABLE pi_scanned_documents
(
   document_id         NUMBER GENERATED ALWAYS AS IDENTITY 
                       MINVALUE 1 MAXVALUE 9999999999999999999999999999 
                       INCREMENT BY 1 START WITH 1 NOT NULL ENABLE,
   original_filename   VARCHAR2(100),
   pdf_filename        VARCHAR2(100),
   pdf_file            BLOB,
   mimetype            VARCHAR2(50) DEFAULT 'application/pdf',
   load_date           DATE DEFAULT SYSDATE,
   --
   CONSTRAINT pi_scanned_documents_pk
     PRIMARY KEY (document_id)
     USING INDEX
);


-- Stop logging
SPOOL OFF 


-- Complete


