------------------------------------------------------------------------------
--
--   PROCEDURE:      pi_create_fnd_attachments_table.sql
--
--   PURPOSE:        This procedure is used to create the FND_ATTACHMENTS 
--                   table. 
-- 
--   PARAMETERS:     None. 
--
--   INSTRUCTIONS:   From within SQL*Plus (connected as APPS), type:
--                      START pi_create_fnd_attachments_table.sql
--
--   AUTHOR:         <PERSON>.
--
--   INSTALL SCHEMA: APPS
--
--
------------------------------------------------------------------------------

WHENEVER OSERROR EXIT

SET ECHO ON


-- Open the log file
SPOOL pi_create_fnd_attachments_table


-- Don't drop the current table (it may have data) 
-- DROP TABLE fnd_attachments;

-- Create the table
CREATE TABLE FND_Attachments
   (AttachmentID          NUMBER GENERATED ALWAYS AS IDENTITY(START with 1 INCREMENT by 1) PRIMARY KEY
   ,AttachmentSource      VARCHAR2(200)
   ,AttachmentSourceID1   VARCHAR2(200)
   ,AttachmentSourceID2   VARCHAR2(200)
   ,AttachmentSourceID3   VARCHAR2(200)
   ,AttachmentTypeID1Code VARCHAR2(20)
   ,AttachmentTypeID2Code VARCHAR2(20)
   ,DocumentComments      VARCHAR2(4000)
   ,DocumentStartDate     DATE DEFAULT NULL
   ,DocumentEndDate       DATE DEFAULT NULL
   ,AttachmentBLOB        BLOB
   ,FileName              VARCHAR2(4000)
   ,Mimetype              VARCHAR2(512) 
   ,FileCharset           VARCHAR2(512)
   ,CreatedBy             VARCHAR2(200)
   ,CreatedDate           DATE DEFAULT SYSDATE
   ,ModifiedBy            VARCHAR2(200)
   ,ModifiedDate          DATE DEFAULT SYSDATE
);


-- For an existing table, run the following ALTERs:
ALTER TABLE fnd_attachments
   MODIFY (attachmentsourceid1 VARCHAR2(200));

ALTER TABLE fnd_attachments
   MODIFY (attachmentsourceid2 VARCHAR2(200));

ALTER TABLE fnd_attachments
   MODIFY (attachmentsourceid3 VARCHAR2(200));      

ALTER TABLE fnd_attachments
   MODIFY (attachmenttypeid1code VARCHAR2(20));

ALTER TABLE fnd_attachments
   MODIFY (attachmenttypeid2code VARCHAR2(20));   

-- Stop logging
SPOOL OFF 


-- Complete
