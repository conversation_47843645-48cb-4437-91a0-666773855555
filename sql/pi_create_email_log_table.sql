------------------------------------------------------------------------------
--
--   PROCEDURE:      pi_create_email_log_table.sql
--
--   PURPOSE:        This procedure is used to create the PI_EMAIL_LOG table. 
--                   This table is used to store the Accept/Reject letter 
--                   responses.
-- 
--   PARAMETERS:     None. 
--
--   INSTRUCTIONS:   From within SQL*Plus (connected as APPS), type:
--                      START pi_create_email_log_table.sql
--
--   AUTHOR:         <PERSON>.
--
--   INSTALL SCHEMA: APPS
--
--
--   Copyright (c) 2025 Kevin <PERSON>uire
--   All rights reserved.
--
--   This software is proprietary and confidential. Unauthorized copying of this
--   file, via any medium, is strictly prohibited without the express permission
--   of the author or owning organization.
--
------------------------------------------------------------------------------

WHENEVER OSERROR EXIT

SET ECHO ON


-- Open the log file
SPOOL pi_create_email_log_table


-- Drop the current table 
DROP TABLE pi_email_log;

-- Create the table
CREATE TABLE pi_email_log
(
   email_id   NUMBER GENERATED ALWAYS AS IDENTITY 
              MINVALUE 1 MAXVALUE 9999999999999999999999999999 
              INCREMENT BY 1 START WITH 1 NOT NULL ENABLE,
   subject    VARCHAR2(100),
   sender     VARCHAR2(100),
   email_time DATE DEFAULT SYSDATE,
   load_date  DATE DEFAULT SYSDATE 
);


-- Stop logging
SPOOL OFF 


-- Complete


