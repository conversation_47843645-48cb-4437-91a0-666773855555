------------------------------------------------------------------------------
--
--   PROCEDURE:      pi_create_host_requests_table.sql
--
--   PURPOSE:        This procedure is used to create the PI_HOST_REQUESTS 
--                   table. This table is used to process host commands 
--                   submitted using PL/SQL.
-- 
--   PARAMETERS:     None. 
--
--   INSTRUCTIONS:   From within SQL*Plus (connected as APPS), type:
--                      START pi_create_host_requests_table.sql
--
--   AUTHOR:         <PERSON>.
--
--   INSTALL SCHEMA: APPS
--
--
--   Copyright (c) 2025 Kevin <PERSON>uire
--   All rights reserved.
--
--   This software is proprietary and confidential. Unauthorized copying of this
--   file, via any medium, is strictly prohibited without the express permission
--   of the author or owning organization.
--
------------------------------------------------------------------------------

WHENEVER OSERROR EXIT

SET ECHO ON


-- Open the log file
SPOOL pi_create_host_requests_table


-- Drop the current table 
DROP TABLE pi_host_requests;

-- Create the table
CREATE TABLE pi_host_requests
(
   id                   NUMBER GENERATED ALWAYS AS IDENTITY 
                        MINVALUE 1 MAXVALUE 9999999999999999999999999999 
                        INCREMENT BY 1 START WITH 1 NOT NULL ENABLE PRIMARY KEY,
   script_name          VARCHAR2(100)  NOT NULL,
   parameter_string     VARCHAR2(1000),
   status               VARCHAR2(255)  DEFAULT 'NEW',
   execution_results    CLOB,
   created_by           VARCHAR2(40)   DEFAULT USER,
   creation_date        DATE           DEFAULT SYSDATE,
   last_modified_by     VARCHAR2(40)   DEFAULT USER,
   list_modified_date   DATE           DEFAULT SYSDATE
);


-- Stop logging
SPOOL OFF 


-- Complete
