------------------------------------------------------------------------------
--
--   PROCEDURE:      pi_create_scanned_document_lines_table.sql
--
--   PURPOSE:        This procedure is used to create the 
--                   PI_SCANNED_DOCUMENT_LINES table. This table is used to 
--                   hold scanned image file text lines.
-- 
--   PARAMETERS:     None. 
--
--   INSTRUCTIONS:   From within SQL*Plus (connected as APPS), type:
--                      START pi_create_scanned_document_lines_table.sql
--
--   AUTHOR:         <PERSON>.
--
--   INSTALL SCHEMA: APPS
--
--
--   Copyright (c) 2025 <PERSON>
--   All rights reserved.
--
--   This software is proprietary and confidential. Unauthorized copying of this
--   file, via any medium, is strictly prohibited without the express permission
--   of the author or owning organization.
--
------------------------------------------------------------------------------

WHENEVER OSERROR EXIT

SET ECHO ON


-- Open the log file
SPOOL pi_create_scanned_document_lines_table


-- Drop the current table 
DROP TABLE pi_scanned_document_lines;

-- Create the table
CREATE TABLE pi_scanned_document_lines
(
   document_id   NUMBER NOT NULL,
   line_number   NUMBER NOT NULL,
   line_text     VARCHAR2(1000),
   load_date     DATE DEFAULT SYSDATE,
   --
   CONSTRAINT pi_scanned_document_lines_pk
     PRIMARY KEY (document_id, line_number)
     USING INDEX,
   CONSTRAINT  pi_scanned_document_lines_fk
      FOREIGN KEY (document_id)
      REFERENCES pi_scanned_documents (document_id) 
      ON DELETE CASCADE
);


-- Stop logging
SPOOL OFF 


-- Complete


