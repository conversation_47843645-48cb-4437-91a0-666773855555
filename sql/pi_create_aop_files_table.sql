------------------------------------------------------------------------------
--
--   PROCEDURE:      pi_create_aop_files_table.sql
--
--   PURPOSE:        This procedure is used to create the PI_AOP_FILES table. 
--                   This table is used to load AOP letter files.
-- 
--   PARAMETERS:     None. 
--
--   INSTRUCTIONS:   From within SQL*Plus (connected as APPS), type:
--                      START pi_create_aop_files_table.sql
--
--   AUTHOR:         <PERSON>re.
--
--   INSTALL SCHEMA: APPS
--
--
--   Copyright (c) 2025 Kevin <PERSON>uire
--   All rights reserved.
--
--   This software is proprietary and confidential. Unauthorized copying of this
--   file, via any medium, is strictly prohibited without the express permission
--   of the author or owning organization.
--
--
------------------------------------------------------------------------------

WHENEVER OSERROR EXIT

SET ECHO ON


-- Open the log file
SPOOL pi_create_aop_files_table


-- Drop the current table 
DROP TABLE pi_aop_files;

-- Create the table
CREATE TABLE pi_aop_files
(
   id         NUMBER GENERATED ALWAYS AS IDENTITY 
              MINVALUE 1 MAXVALUE 9999999999999999999999999999 
              INCREMENT BY 1 START WITH 1 NOT NULL ENABLE,
   filename   VARCHAR2(100),
   blob_data  BLOB
);


-- Stop logging
SPOOL OFF 


-- Complete


