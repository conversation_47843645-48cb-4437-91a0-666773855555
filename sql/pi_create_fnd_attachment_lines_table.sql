------------------------------------------------------------------------------
--
--   PROCEDURE:      pi_create_fnd_attachment_lines_table.sql
--
--   PURPOSE:        This procedure is used to create the 
--                   FND_ATTACHMENT_LINES table. This table is used to 
--                   hold the text lines extracted from the files loaded
--                   into FND_ATTACHMENTS.
-- 
--   PARAMETERS:     None. 
--
--   INSTRUCTIONS:   From within SQL*Plus (connected as APPS), type:
--                      START pi_create_fnd_attachment_lines_table.sql
--
--   AUTHOR:         <PERSON>.
--
--   INSTALL SCHEMA: APPS
--
--
--   Copyright (c) 2025 Kevin <PERSON>
--   All rights reserved.
--
--   This software is proprietary and confidential. Unauthorized copying of this
--   file, via any medium, is strictly prohibited without the express permission
--   of the author or owning organization.
--
------------------------------------------------------------------------------

WHENEVER OSERROR EXIT

SET ECHO ON


-- Open the log file
SPOOL pi_create_fnd_attachment_lines_table


-- Drop the current table 
-- DROP TABLE fnd_attachment_lines;

-- Create the table
CREATE TABLE fnd_attachment_lines
(
   attachmentid   NUMBER NOT NULL,
   line_number    NUMBER NOT NULL,
   line_text      VARCHAR2(1000),
   page_number    NUMBER,
   page_line      NUMBER,
   load_date      DATE DEFAULT SYSDATE,
   --
   CONSTRAINT fnd_attachment_lines_pk
     PRIMARY KEY (attachmentid, line_number)
     USING INDEX,
   CONSTRAINT  fnd_attachment_lines_fk
      FOREIGN KEY (attachmentid)
      REFERENCES fnd_attachments (attachmentid) 
      ON DELETE CASCADE
);


-- Stop logging
SPOOL OFF 


-- Complete


